import { defineConfig, devices } from '@playwright/test';

/**
 * Docker 环境专用的 Playwright 配置
 * 专门解决容器环境中的 --no-sandbox 问题
 * @see https://playwright.dev/docs/test-configuration
 */
export default defineConfig({
  testDir: './tests/e2e',
  fullyParallel: false,
  forbidOnly: true,
  retries: 2,
  workers: 1,
  reporter: [['html'], ['list']],
  
  use: {
    baseURL: 'http://localhost:4096',
    trace: 'retain-on-failure',
    screenshot: 'only-on-failure',
    video: 'retain-on-failure',
    
    // Docker 环境优化设置
    actionTimeout: 60000,
    navigationTimeout: 60000,
  },

  projects: [
    {
      name: 'chromium-docker',
      use: { 
        ...devices['Desktop Chrome'],
        launchOptions: {
          // Docker 环境必需的参数，避免 --no-sandbox 警告
          args: [
            '--no-sandbox',
            '--disable-setuid-sandbox',
            '--disable-dev-shm-usage',
            '--disable-gpu',
            '--no-first-run',
            '--no-zygote',
            '--single-process',
            '--disable-background-timer-throttling',
            '--disable-backgrounding-occluded-windows',
            '--disable-renderer-backgrounding',
            '--disable-features=TranslateUI,VizDisplayCompositor',
            '--disable-ipc-flooding-protection',
            '--disable-extensions',
            '--disable-default-apps',
            '--disable-sync',
            '--metrics-recording-only',
            '--no-default-browser-check',
            '--no-pings',
            '--password-store=basic',
            '--use-mock-keychain',
            '--disable-component-extensions-with-background-pages',
            '--disable-background-networking',
            '--disable-component-update',
            '--disable-client-side-phishing-detection',
            '--disable-hang-monitor',
            '--disable-prompt-on-repost',
            '--disable-web-resources',
            '--disable-features=AudioServiceOutOfProcess',
            '--headless=new',
          ],
          // 忽略 HTTPS 错误 - 暂时注释掉，因为类型不匹配
          // ignoreHTTPSErrors: true,
        },
      },
    },
  ],

  webServer: {
    command: 'pnpm run dev',
    url: 'http://localhost:4096',
    reuseExistingServer: false,
    timeout: 300 * 1000, // 5 分钟超时
  },
});

/**
 * 样式管理Store统一导出
 * 🎯 核心价值：样式状态管理集中导出
 */

// 样式Store导出
import {
  useStyleStore,
  type StyleConfig,
  type ThemeType,
  type StylePreset,
  type StyleStoreState,
} from '@/stores/styleStore';

import {
  useDynamicStyleStore,
  type DynamicStyleConfig,
  type DeviceInfo,
  type InteractionState,
  type DynamicStyleStoreState,
} from '@/stores/dynamicStyleStore';

import {
  SIZE_STYLES,
  VARIANT_STYLES,
  BASE_BUTTON_STYLES,
  BUTTON_STYLES,
  TAB_STYLES,
  INPUT_STYLES,
  GRID_STYLES,
  DEFAULT_MATRIX_STYLES,
  DEFAULT_CONTROL_PANEL_STYLES,
  DEFAULT_BUTTON_STYLES,
  DEFAULT_COLOR_SCHEME,
} from '@/stores/constants/styles';

import type {
  ButtonVariant,
  ButtonSize,
  ButtonState,
  ButtonConfig,
} from '@/lib/utils/buttonUtils';

export {
  useStyleStore,
  useDynamicStyleStore,
}

export type {
  StyleConfig,
  ThemeType,
  StylePreset,
  StyleStoreState,
  DynamicStyleConfig,
  DeviceInfo,
  InteractionState,
  DynamicStyleStoreState,
  ButtonVariant,
  ButtonSize,
  ButtonState,
  ButtonConfig,
}

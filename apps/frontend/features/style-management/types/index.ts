/**
 * 样式管理类型定义统一导出
 * 🎯 核心价值：样式相关类型定义集中管理
 */

// 从现有类型系统导出样式相关类型
export type {
  ButtonVariant,
  ButtonSize,
  ButtonState,
  ButtonConfig,
} from '@/lib/utils/buttonUtils';

export type {
  DynamicStyleConfig,
  DeviceInfo,
  InteractionState,
  DynamicStyleStoreState,
} from '@/stores/dynamicStyleStore';

export type {
  StyleConfig,
  ThemeType,
  StylePreset,
  StyleStoreState,
} from '@/stores/styleStore';

/**
 * 样式管理类型定义统一导出
 * 🎯 核心价值：样式相关类型定义集中管理
 */

// 从现有类型系统导出样式相关类型
export type {
  ButtonVariant,
  ButtonSize,
  ButtonState,
  ButtonConfig,
} from '@/lib/utils/buttonUtils';

export type {
  DynamicStyleConfig,
  DeviceInfo,
  InteractionState,
  DynamicStyleStoreState,
} from '@/stores/dynamicStyleStore';

export type {
  StyleConfig,
  ThemeType,
  StylePreset,
  StyleStoreState,
} from '@/stores/styleStore';

// 样式管理特定类型
export interface StylePanelProps {
  className?: string;
}

export interface ThemeControlsProps {
  className?: string;
  showAdvanced?: boolean;
  compactMode?: boolean;
}

export interface StyleConfigProps {
  className?: string;
  showPresets?: boolean;
  showAdvanced?: boolean;
}

export interface ThemeConfig {
  name: string;
  colors: Record<string, string>;
  styles: Record<string, any>;
}

export interface StyleManagementPreset {
  id: string;
  name: string;
  description?: string;
  config: Partial<{
    fontSize: number;
    matrixMargin: number;
    cellShape: 'square' | 'rounded';
    displayMode: 'number' | 'coordinate' | 'color';
    enableCircleScale: boolean;
    circleScaleFactor: number;
    theme: 'light' | 'dark';
  }>;
}

import React, { memo, useCallback, useState, useEffect } from 'react';
import type { BaseDisplayMode } from '@/components/grid-system/types';

/**
 * 显示模式控制组件 - 重构版
 * 🎯 核心价值：提供基础显示模式控制功能
 * ⚡ 功能范围：控制网格单元格内容显示方式（值、坐标、颜色）
 * 🔄 迁移状态：移除灰色模式和初始化按钮，增加颜色模式开关逻辑
 */

interface DisplayModeControlProps {
  className?: string;
  baseDisplayMode?: BaseDisplayMode;
  colorModeEnabled?: boolean;
  onBaseDisplayModeChange?: (mode: BaseDisplayMode) => void;
  onColorModeToggle?: (enabled: boolean) => void;
}

export const DisplayModeControl = memo<DisplayModeControlProps>(({
  className = '',
  baseDisplayMode = 'coordinates',
  colorModeEnabled = false,
  onBaseDisplayModeChange,
  onColorModeToggle
}) => {
  // 使用本地状态管理基础显示模式和颜色模式
  const [currentBaseMode, setCurrentBaseMode] = useState<BaseDisplayMode>(baseDisplayMode);
  const [currentColorMode, setCurrentColorMode] = useState<boolean>(colorModeEnabled);

  // 同步外部状态变化
  useEffect(() => {
    setCurrentBaseMode(baseDisplayMode);
  }, [baseDisplayMode]);

  useEffect(() => {
    setCurrentColorMode(colorModeEnabled);
  }, [colorModeEnabled]);

  // 基础显示模式变更处理
  const handleBaseDisplayModeChange = useCallback((mode: BaseDisplayMode) => {
    // 坐标模式可以随时切换
    if (mode === 'coordinates') {
      setCurrentBaseMode(mode);
      if (onBaseDisplayModeChange) {
        onBaseDisplayModeChange(mode);
      }
      return;
    }

    // 颜色和数值模式需要颜色模式开关启用
    if ((mode === 'color' || mode === 'value') && !currentColorMode) {
      return; // 颜色模式未启用时不允许切换到颜色或数值模式
    }

    setCurrentBaseMode(mode);
    if (onBaseDisplayModeChange) {
      onBaseDisplayModeChange(mode);
    }
  }, [onBaseDisplayModeChange, currentColorMode]);

  // 颜色模式切换处理
  const handleColorModeToggle = useCallback((enabled: boolean) => {
    setCurrentColorMode(enabled);
    if (onColorModeToggle) {
      onColorModeToggle(enabled);
    }

    // 如果禁用颜色模式且当前是颜色或数值模式，切换到坐标模式
    if (!enabled && (currentBaseMode === 'color' || currentBaseMode === 'value')) {
      setCurrentBaseMode('coordinates');
      if (onBaseDisplayModeChange) {
        onBaseDisplayModeChange('coordinates');
      }
    }
    // 如果启用颜色模式且当前是坐标模式，切换到颜色模式
    else if (enabled && currentBaseMode === 'coordinates') {
      setCurrentBaseMode('color');
      if (onBaseDisplayModeChange) {
        onBaseDisplayModeChange('color');
      }
    }
  }, [onColorModeToggle, onBaseDisplayModeChange, currentBaseMode]);

  return (
    <div className={`p-4 ${className}`}>
      <div className="space-y-6">

        {/* 颜色模式开关 */}
        <div className="space-y-3">
          <div className="border rounded-lg p-3 bg-blue-50">
            <div className="flex items-start">
              <div className="flex items-center h-5">
                <input
                  id="color-mode-toggle"
                  name="colorMode"
                  type="checkbox"
                  checked={currentColorMode}
                  onChange={(e) => handleColorModeToggle(e.target.checked)}
                  className="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                />
              </div>
              <div className="ml-3 text-sm">
                <label htmlFor="color-mode-toggle" className="font-medium text-blue-700">
                  颜色模式
                </label>
                <p className="text-blue-600 text-xs">
                  启用后可以切换颜色和数值显示模式
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* 基础显示模式选择 */}
        <div className="space-y-4">
          <h3 className="text-sm font-medium text-gray-700">显示模式</h3>

          <div className="space-y-3">
            {/* 坐标模式 - 独立模式，默认选中 */}
            <div className="border rounded-lg p-3 bg-orange-50">
              <div className="flex items-start">
                <div className="flex items-center h-5">
                  <input
                    id="base-mode-coordinates"
                    name="baseDisplayMode"
                    type="radio"
                    value="coordinates"
                    checked={currentBaseMode === 'coordinates'}
                    onChange={() => handleBaseDisplayModeChange('coordinates')}
                    className="h-4 w-4 text-orange-600 border-gray-300 focus:ring-orange-500"
                  />
                </div>
                <div className="ml-3 text-sm">
                  <label htmlFor="base-mode-coordinates" className="font-medium text-orange-700">
                    坐标模式
                  </label>
                  <p className="text-orange-600 text-xs">显示单元格的坐标位置（x,y）</p>
                  <div className="flex items-center mt-1">
                    <span className="text-xs text-orange-500">⚡ 任何时候都能触发，默认模式</span>
                  </div>
                </div>
              </div>
            </div>

            {/* 颜色模式 - 需要颜色模式开关启用 */}
            <div className={`border rounded-lg p-3 ${currentColorMode ? 'bg-purple-50' : 'bg-gray-100'}`}>
              <div className="flex items-start">
                <div className="flex items-center h-5">
                  <input
                    id="base-mode-color"
                    name="baseDisplayMode"
                    type="radio"
                    value="color"
                    checked={currentBaseMode === 'color'}
                    onChange={() => handleBaseDisplayModeChange('color')}
                    disabled={!currentColorMode}
                    className={`h-4 w-4 border-gray-300 focus:ring-purple-500 ${
                      currentColorMode ? 'text-purple-600' : 'text-gray-400'
                    }`}
                  />
                </div>
                <div className="ml-3 text-sm">
                  <label
                    htmlFor="base-mode-color"
                    className={`font-medium ${
                      currentColorMode ? 'text-purple-700' : 'text-gray-500'
                    }`}
                  >
                    颜色模式
                  </label>
                  <p className={`text-xs ${
                    currentColorMode ? 'text-purple-600' : 'text-gray-500'
                  }`}>
                    仅显示单元格的背景颜色 {!currentColorMode && '(需要启用颜色模式)'}
                  </p>
                </div>
              </div>
            </div>

            {/* 数值模式 - 颜色模式的子集，需要颜色模式开关启用 */}
            <div className={`border rounded-lg p-3 ml-4 ${currentColorMode ? 'bg-blue-50' : 'bg-gray-100'}`}>
              <div className="flex items-start">
                <div className="flex items-center h-5">
                  <input
                    id="base-mode-value"
                    name="baseDisplayMode"
                    type="radio"
                    value="value"
                    checked={currentBaseMode === 'value'}
                    onChange={() => handleBaseDisplayModeChange('value')}
                    disabled={!currentColorMode}
                    className={`h-4 w-4 border-gray-300 focus:ring-blue-500 ${
                      currentColorMode ? 'text-blue-600' : 'text-gray-400'
                    }`}
                  />
                </div>
                <div className="ml-3 text-sm">
                  <label 
                    htmlFor="base-mode-value" 
                    className={`font-medium ${
                      currentColorMode ? 'text-blue-700' : 'text-gray-500'
                    }`}
                  >
                    数值模式
                  </label>
                  <p className={`text-xs ${
                    currentColorMode ? 'text-blue-600' : 'text-gray-500'
                  }`}>
                    颜色模式 + 数值映射（彩色单元格: 1-8，特殊坐标: A-M）{!currentColorMode && ' (需要启用颜色模式)'}
                  </p>
                  <div className="flex items-center mt-1">
                    <span className={`text-xs ${
                      currentColorMode ? 'text-blue-500' : 'text-gray-400'
                    }`}>↳ 扩展自颜色模式，与坐标模式互斥</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* 模式关系说明 */}
          <div className="mt-4 p-3 bg-gray-50 rounded text-xs text-gray-600">
            <div className="font-medium mb-2">显示模式关系：</div>
            <div className="space-y-1">
              <div>• <span className="text-orange-600">坐标模式</span>：任何时候都能触发，默认模式</div>
              <div>• <span className="text-purple-600">颜色模式</span>：需要启用颜色模式开关</div>
              <div>• <span className="text-blue-600">数值模式</span>：颜色模式的扩展，与坐标模式互斥</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
});

DisplayModeControl.displayName = 'DisplayModeControl';

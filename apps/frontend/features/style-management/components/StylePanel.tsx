import React, { memo, useCallback, useEffect } from 'react';
import { DisplayModeControl } from './DisplayModeControl';
import type { BaseDisplayMode } from '@/components/grid-system/types';
import { useBaseDisplayMode, useColorModeEnabled, useGridConfigActions } from '@/stores/gridConfigStore';

/**
 * 样式面板组件 - 重构版
 * 🎯 核心价值：提供基础的网格显示控制功能和初始化功能
 * ⚡ 性能优化：使用memo包装和useCallback优化
 * 📊 功能范围：基础显示模式控制 + 颜色模式开关 + 初始化功能
 * 🔄 迁移状态：移除灰色模式，增加颜色模式开关逻辑
 */

interface StylePanelProps {
  className?: string;
  initialBaseDisplayMode?: BaseDisplayMode;
  initialColorModeEnabled?: boolean;
  onBaseDisplayModeChange?: (mode: BaseDisplayMode) => void;
  onColorModeToggle?: (enabled: boolean) => void;
}

export const StylePanel = memo<StylePanelProps>(({
  className = '',
  initialBaseDisplayMode = 'coordinates',
  initialColorModeEnabled = false,
  onBaseDisplayModeChange,
  onColorModeToggle
}) => {
  // 使用 store 中的状态
  const baseDisplayMode = useBaseDisplayMode();
  const colorModeEnabled = useColorModeEnabled();
  const { setDisplayMode, setColorModeEnabled } = useGridConfigActions();

  // 当外部props更新时同步store状态（向后兼容）
  useEffect(() => {
    if (initialBaseDisplayMode !== baseDisplayMode) {
      setDisplayMode(initialBaseDisplayMode);
    }
  }, [initialBaseDisplayMode, baseDisplayMode, setDisplayMode]);

  useEffect(() => {
    if (initialColorModeEnabled !== colorModeEnabled) {
      setColorModeEnabled(initialColorModeEnabled);
    }
  }, [initialColorModeEnabled, colorModeEnabled, setColorModeEnabled]);

  // 基础显示模式变更处理
  const handleBaseDisplayModeChange = useCallback((mode: BaseDisplayMode) => {
    setDisplayMode(mode);
    if (onBaseDisplayModeChange) {
      onBaseDisplayModeChange(mode);
    }
  }, [onBaseDisplayModeChange, setDisplayMode]);

  // 颜色模式切换处理
  const handleColorModeToggle = useCallback((enabled: boolean) => {
    setColorModeEnabled(enabled);
    if (onColorModeToggle) {
      onColorModeToggle(enabled);
    }
  }, [onColorModeToggle, setColorModeEnabled]);



  return (
    <div className={`space-y-4 ${className}`}>
      <div className="border rounded-lg bg-white shadow-sm">
        <div className="p-4 border-b">
          <h3 className="text-lg font-medium text-gray-800">网格显示控制</h3>
          <p className="text-sm text-gray-500 mt-1">
            控制网格单元格的显示方式和灰色模式过滤器
          </p>

          {/* 状态指示器 */}
          <div className="mt-3 flex flex-wrap gap-2">
            <div className={`px-2 py-1 rounded text-xs font-medium ${
              baseDisplayMode === 'color' ? 'bg-purple-100 text-purple-700' :
              baseDisplayMode === 'value' ? 'bg-blue-100 text-blue-700' :
              'bg-orange-100 text-orange-700'
            }`}>
              当前模式: {
                baseDisplayMode === 'color' ? '颜色' :
                baseDisplayMode === 'value' ? '数值' : '坐标'
              }
            </div>

            {colorModeEnabled && (
              <div className="px-2 py-1 rounded text-xs font-medium bg-green-100 text-green-700">
                颜色模式已启用
              </div>
            )}

            {(baseDisplayMode === 'color' || baseDisplayMode === 'value') && !colorModeEnabled && (
              <div className="px-2 py-1 rounded text-xs font-medium bg-red-100 text-red-700">
                颜色和数值模式需要启用颜色模式开关
              </div>
            )}
          </div>
        </div>

        {/* 显示模式控制 */}
        <DisplayModeControl
          baseDisplayMode={baseDisplayMode}
          colorModeEnabled={colorModeEnabled}
          onBaseDisplayModeChange={handleBaseDisplayModeChange}
          onColorModeToggle={handleColorModeToggle}
        />
      </div>
    </div>
  );
});

StylePanel.displayName = 'StylePanel';

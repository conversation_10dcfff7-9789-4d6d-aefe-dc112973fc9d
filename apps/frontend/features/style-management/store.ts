/**
 * 样式管理Store统一导出
 * 🎯 核心价值：样式状态管理集中导出
 */

// 样式Store导出
export {
  useStyleStore,
  type StyleConfig,
  type ThemeType,
  type StylePreset,
  type StyleStoreState,
} from '@/stores/styleStore';

export {
  useDynamicStyleStore,
  type DynamicStyleConfig,
  type DeviceInfo,
  type InteractionState,
  type DynamicStyleStoreState,
} from '@/stores/dynamicStyleStore';

export type {
  ButtonVariant,
  ButtonSize,
  ButtonState,
  ButtonConfig,
} from '@/lib/utils/buttonUtils';

/**
 * Grid System 功能状态管理导出
 * 🎯 核心价值：网格系统状态管理集中导出
 * 📦 Store范围：功能特定的状态管理，与全局stores集成
 */

// 从全局stores重新导出网格相关状态
export {
  useGridConfigStore,
  useBaseDisplayMode,
  useGridConfig,
  useGridConfigActions,
  type GridConfigStore,
} from '@/stores/gridConfigStore'

export {
  useBasicDataStore,
  useMatrixData,
  useColorValues,
  useColorVisibility,
  useGroupVisibility,
  useBlackCellData,
  useDataPointsAt,
  useGroupData,
  useColorData,
  useLevelData,
  useGridData,
  useCellAt,
} from '@/stores/basicDataStore'

// 功能特定的状态管理
export { useGridInteractionStore } from './gridInteractionStore'
// TODO: 待实现
// export { useGridSelectionStore } from './gridSelectionStore'

// Store类型导出
export type {
  GridInteractionState,
  GridSelectionState,
} from './types'

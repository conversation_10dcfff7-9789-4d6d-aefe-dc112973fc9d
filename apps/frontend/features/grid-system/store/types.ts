/**
 * Grid System Store Types
 * 
 * 网格系统store相关的类型定义
 * 🎯 核心价值：为store提供类型安全
 * 📦 类型范围：store状态、actions、配置等
 */

import type { CellData } from '@/lib/types/grid';

// 网格交互状态类型（精简版，移除拖拽和编辑状态）
export interface GridInteractionState {
  hoveredCell: CellData | null;
  selectedCells: Set<string>;
  focusedCell: CellData | null;
}

// 网格选择状态类型
export interface GridSelectionState {
  selectedCells: Map<string, CellData>;
  selectionMode: 'single' | 'multiple' | 'range';
  selectionStart: CellData | null;
  selectionEnd: CellData | null;
  isSelecting: boolean;
}

// Store配置类型
export interface StoreConfig {
  enablePersistence: boolean;
  persistenceKey: string;
  enableDevtools: boolean;
  enableSubscriptions: boolean;
}

// Store操作结果类型
export interface StoreOperationResult {
  success: boolean;
  error?: string;
  data?: any;
}

// Store事件类型（精简版，移除拖拽和编辑事件）
export type StoreEventType =
  | 'cell-selected'
  | 'cell-deselected'
  | 'selection-cleared'
  | 'hover-changed'
  | 'focus-changed';

export interface StoreEvent {
  type: StoreEventType;
  payload: any;
  timestamp: number;
}

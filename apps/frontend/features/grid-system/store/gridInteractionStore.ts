/**
 * Grid Interaction Store - 精简版
 *
 * 网格交互状态管理store（移除未使用的拖拽和编辑功能）
 * 🎯 核心价值：集中管理网格核心交互状态，支持跨组件状态共享
 * 📦 功能范围：悬停状态、选择状态、焦点状态
 */

import { create } from 'zustand';
import { subscribeWithSelector } from 'zustand/middleware';

import type { CellData } from '@/lib/types/grid';
import { logger } from '@/lib/hooks/useLogManager';

// 精简的交互状态接口
interface SimplifiedGridInteractionState {
  hoveredCell: CellData | null;
  selectedCells: Set<string>;
  focusedCell: CellData | null;
}

interface GridInteractionStore extends SimplifiedGridInteractionState {
  // 核心交互操作
  setHoveredCell: (cell: CellData | null) => void;
  setSelectedCells: (cells: Set<string>) => void;
  addSelectedCell: (cellKey: string) => void;
  removeSelectedCell: (cellKey: string) => void;
  clearSelection: () => void;
  setFocusedCell: (cell: CellData | null) => void;

  // 工具方法
  reset: () => void;
  getSelectedCellsArray: () => string[];
  isSelected: (cellKey: string) => boolean;
}

const initialState: SimplifiedGridInteractionState = {
  hoveredCell: null,
  selectedCells: new Set<string>(),
  focusedCell: null,
};

export const useGridInteractionStore = create<GridInteractionStore>()(
  subscribeWithSelector((set, get) => ({
    ...initialState,

    // 设置悬停单元格
    setHoveredCell: (cell) => {
      set({ hoveredCell: cell });
      if (cell) {
        logger.debug(`Store: 设置悬停单元格 (${cell.x}, ${cell.y})`, undefined, 'gridInteraction');
      }
    },

    // 设置选中单元格集合
    setSelectedCells: (cells) => {
      set({ selectedCells: new Set(cells) });
      logger.debug(`Store: 设置选中单元格，数量: ${cells.size}`, undefined, 'gridInteraction');
    },

    // 添加选中单元格
    addSelectedCell: (cellKey) => {
      const { selectedCells } = get();
      const newSelectedCells = new Set(selectedCells);
      newSelectedCells.add(cellKey);
      set({ selectedCells: newSelectedCells });
      logger.debug(`Store: 添加选中单元格 ${cellKey}`, undefined, 'gridInteraction');
    },

    // 移除选中单元格
    removeSelectedCell: (cellKey) => {
      const { selectedCells } = get();
      const newSelectedCells = new Set(selectedCells);
      newSelectedCells.delete(cellKey);
      set({ selectedCells: newSelectedCells });
      logger.debug(`Store: 移除选中单元格 ${cellKey}`, undefined, 'gridInteraction');
    },

    // 清空选择
    clearSelection: () => {
      set({ selectedCells: new Set() });
      logger.debug('Store: 清空选择', undefined, 'gridInteraction');
    },

    // 设置焦点单元格
    setFocusedCell: (cell) => {
      set({ focusedCell: cell });
      if (cell) {
        logger.debug(`Store: 设置焦点单元格 (${cell.x}, ${cell.y})`, undefined, 'gridInteraction');
      }
    },

    // 重置所有状态
    reset: () => {
      set(initialState);
    },

    // 获取选中单元格数组
    getSelectedCellsArray: () => {
      return Array.from(get().selectedCells);
    },

    // 检查单元格是否被选中
    isSelected: (cellKey) => {
      return get().selectedCells.has(cellKey);
    },
  }))
);



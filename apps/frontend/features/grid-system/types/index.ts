/**
 * Grid System 功能特定类型导出
 * 🎯 核心价值：网格系统功能特定类型集中管理
 * 📦 类型范围：组件专用类型、业务逻辑类型、功能接口等
 * 🔄 重构说明：核心业务类型保留在 @/lib/types/grid.ts
 */

import type { CellData, GridDimensions, GridUIConfig, GridConfig } from '@/lib/types/grid';
import type {
  GridDisplayMode,
  InteractionMode,
  SelectionMode
} from '@/stores/constants/grid';

// 重新导出核心类型
export type {
  CellData,
  GridDimensions,
  GridUIConfig,
  GridConfig as CoreGridConfig, // 向后兼容
} from '@/lib/types/grid'

// 功能特定的组件类型
export interface GridControlPanelProps {
  onDisplayModeChange?: (mode: string) => void
  onGrayModeToggle?: (enabled: boolean) => void
  onConfigChange?: (config: Partial<GridBusinessConfig>) => void
  className?: string
}

export interface GridContainerProps {
  data?: unknown[]
  config?: Partial<GridBusinessConfig>
  onCellClick?: (cell: CellData, event: React.MouseEvent) => void
  onCellHover?: (cell: CellData | null) => void
  className?: string
}

export interface GridOverlayProps {
  visible: boolean
  content: React.ReactNode
  position?: 'center' | 'top' | 'bottom'
  className?: string
}

// 业务逻辑特定的配置类型
export interface GridBusinessConfig {
  // 继承核心配置（使用统一的类型定义）
  displayMode: GridDisplayMode
  grayModeEnabled: boolean

  // 业务特定配置（使用统一的类型定义）
  interactionMode: InteractionMode
  selectionMode: SelectionMode
  autoSave: boolean
  realTimeUpdates: boolean

  // 性能配置
  virtualization: boolean
  batchSize: number
  debounceMs: number
}

// 交互状态类型
export interface GridInteractionState {
  hoveredCell: CellData | null
  selectedCells: Set<string>
  focusedCell: CellData | null
}

// 选择状态类型
export interface GridSelectionState {
  selectedCells: Map<string, CellData>
  selectionMode: 'single' | 'multiple' | 'range'
  selectionStart: CellData | null
  selectionEnd: CellData | null
  isSelecting: boolean
}

// Hook返回类型
export interface UseCellDataManagerReturn {
  getCellData: (x: number, y: number) => CellData | null
  updateCellData: (x: number, y: number, data: Partial<CellData>) => void
  batchUpdateCells: (updates: Array<{x: number, y: number, data: Partial<CellData>}>) => void
  resetCellData: (x: number, y: number) => void
  isLoading: boolean
  error: Error | null
}

export interface UseGridDataManagerReturn {
  gridData: CellData[][]
  refreshData: () => Promise<void>
  saveData: () => Promise<void>
  isDirty: boolean
  isLoading: boolean
  error: Error | null
}

export interface UseGridInteractionReturn {
  interactionState: GridInteractionState
  handleCellClick: (cell: CellData, event: React.MouseEvent) => void
  handleCellHover: (cell: CellData | null) => void
  handleCellFocus: (cell: CellData) => void
}

export interface UseGridSelectionReturn {
  selectionState: GridSelectionState
  selectCell: (cell: CellData, mode?: 'replace' | 'add' | 'toggle') => void
  selectRange: (start: CellData, end: CellData) => void
  clearSelection: () => void
  getSelectedCells: () => CellData[]
  isSelected: (cell: CellData) => boolean
}

export interface UseGridRendererReturn {
  renderGrid: () => React.ReactNode
  renderCell: (cell: CellData) => React.ReactNode
  getCellStyle: (cell: CellData) => React.CSSProperties
  getCellClassName: (cell: CellData) => string
  isVisible: (cell: CellData) => boolean
}

export interface UseGridLayoutReturn {
  gridLayout: {
    rows: number
    cols: number
    cellSize: number
    gap: number
    totalWidth: number
    totalHeight: number
  }
  calculateCellPosition: (x: number, y: number) => {x: number, y: number}
  getCellAtPosition: (clientX: number, clientY: number) => CellData | null
  updateLayout: (config: Partial<GridBusinessConfig>) => void
}

export interface UseGridBusinessConfigReturn {
  config: GridBusinessConfig
  updateConfig: (updates: Partial<GridBusinessConfig>) => void
  resetConfig: () => void
  validateConfig: (config: Partial<GridBusinessConfig>) => {isValid: boolean, errors: string[]}
  saveConfig: () => Promise<void>
  loadConfig: () => Promise<void>
}

export interface UseGridPerformanceReturn {
  metrics: {
    renderTime: number
    updateTime: number
    memoryUsage: number
    cellCount: number
  }
  optimizations: {
    virtualizationEnabled: boolean
    batchingEnabled: boolean
    memoizationEnabled: boolean
  }
  toggleOptimization: (key: keyof UseGridPerformanceReturn['optimizations']) => void
  measurePerformance: <T>(fn: () => T, label: string) => T
}

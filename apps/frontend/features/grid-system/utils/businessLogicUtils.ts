/**
 * Business Logic Utilities
 * 
 * 网格系统业务逻辑工具函数
 * 🎯 核心价值：提供业务逻辑相关的工具函数
 * 📦 功能范围：业务计算、状态管理、数据处理
 */

import type { CellData } from '@/lib/types/grid';
import type { GridBusinessConfig, GridInteractionState } from '../types';

export const businessLogicUtils = {
  /**
   * 计算选择区域
   * @param startCell 开始单元格
   * @param endCell 结束单元格
   * @returns 选择区域内的所有单元格坐标
   */
  calculateSelectionRange: (startCell: CellData, endCell: CellData): Array<{x: number, y: number}> => {
    const minX = Math.min(startCell.x, endCell.x);
    const maxX = Math.max(startCell.x, endCell.x);
    const minY = Math.min(startCell.y, endCell.y);
    const maxY = Math.max(startCell.y, endCell.y);
    
    const range: Array<{x: number, y: number}> = [];
    for (let x = minX; x <= maxX; x++) {
      for (let y = minY; y <= maxY; y++) {
        range.push({ x, y });
      }
    }
    return range;
  },

  /**
   * 检查是否需要自动保存
   * @param config 业务配置
   * @param lastSaveTime 上次保存时间
   * @param hasChanges 是否有更改
   * @returns 是否需要自动保存
   */
  shouldAutoSave: (config: GridBusinessConfig, lastSaveTime: number, hasChanges: boolean): boolean => {
    if (!config.autoSave || !hasChanges) return false;
    
    const now = Date.now();
    const timeSinceLastSave = now - lastSaveTime;
    const autoSaveInterval = 30000; // 30秒
    
    return timeSinceLastSave >= autoSaveInterval;
  },

  /**
   * 计算性能指标
   * @param startTime 开始时间
   * @param cellCount 单元格数量
   * @returns 性能指标
   */
  calculatePerformanceMetrics: (startTime: number, cellCount: number) => {
    const endTime = performance.now();
    const renderTime = endTime - startTime;
    const cellsPerMs = cellCount / renderTime;
    
    return {
      renderTime,
      cellCount,
      cellsPerMs,
      isOptimal: renderTime < 16, // 60fps
    };
  },

  /**
   * 生成操作历史记录
   * @param operation 操作类型
   * @param cells 影响的单元格
   * @param oldValues 旧值
   * @param newValues 新值
   * @returns 历史记录
   */
  createHistoryEntry: (
    operation: string,
    cells: CellData[],
    oldValues: unknown[],
    newValues: unknown[]
  ) => {
    return {
      id: `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      timestamp: Date.now(),
      operation,
      cells: cells.map(cell => ({ x: cell.x, y: cell.y })),
      oldValues,
      newValues,
      canUndo: true,
      canRedo: false,
    };
  },
};

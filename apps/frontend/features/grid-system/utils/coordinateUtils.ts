/**
 * Coordinate Utilities
 * 
 * 坐标转换和计算工具函数
 * 🎯 核心价值：提供坐标系统相关的工具函数
 * 📦 功能范围：坐标转换、距离计算、边界检查
 */

import type { CellData } from '@/lib/types/grid';
import { GRID_CONSTANTS } from '@/stores/constants/grid';

export const coordinateUtils = {
  /**
   * 将行列索引转换为网格坐标
   * @param row 行索引
   * @param col 列索引
   * @returns 网格坐标
   */
  rowColToGrid: (row: number, col: number): { x: number; y: number } => {
    const center = GRID_CONSTANTS.CENTER_OFFSET;
    return {
      x: col - center,
      y: center - row,
    };
  },

  /**
   * 将网格坐标转换为行列索引
   * @param x X坐标
   * @param y Y坐标
   * @returns 行列索引
   */
  gridToRowCol: (x: number, y: number): { row: number; col: number } => {
    const center = GRID_CONSTANTS.CENTER_OFFSET;
    return {
      row: center - y,
      col: x + center,
    };
  },

  /**
   * 检查坐标是否在网格范围内
   * @param x X坐标
   * @param y Y坐标
   * @returns 是否在范围内
   */
  isInBounds: (x: number, y: number): boolean => {
    const limit = GRID_CONSTANTS.CENTER_OFFSET;
    return x >= -limit && x <= limit && y >= -limit && y <= limit;
  },

  /**
   * 计算两点之间的曼哈顿距离
   * @param x1 第一个点的X坐标
   * @param y1 第一个点的Y坐标
   * @param x2 第二个点的X坐标
   * @param y2 第二个点的Y坐标
   * @returns 曼哈顿距离
   */
  manhattanDistance: (x1: number, y1: number, x2: number, y2: number): number => {
    return Math.abs(x2 - x1) + Math.abs(y2 - y1);
  },

  /**
   * 计算两点之间的欧几里得距离
   * @param x1 第一个点的X坐标
   * @param y1 第一个点的Y坐标
   * @param x2 第二个点的X坐标
   * @param y2 第二个点的Y坐标
   * @returns 欧几里得距离
   */
  euclideanDistance: (x1: number, y1: number, x2: number, y2: number): number => {
    const dx = x2 - x1;
    const dy = y2 - y1;
    return Math.sqrt(dx * dx + dy * dy);
  },

  /**
   * 获取单元格的邻居坐标
   * @param x X坐标
   * @param y Y坐标
   * @param includeDiagonal 是否包含对角线邻居
   * @returns 邻居坐标数组
   */
  getNeighbors: (x: number, y: number, includeDiagonal: boolean = false): Array<{ x: number; y: number }> => {
    const neighbors: Array<{ x: number; y: number }> = [];
    
    // 四个基本方向
    const directions = [
      { x: 0, y: 1 },   // 上
      { x: 1, y: 0 },   // 右
      { x: 0, y: -1 },  // 下
      { x: -1, y: 0 },  // 左
    ];
    
    // 对角线方向
    if (includeDiagonal) {
      directions.push(
        { x: 1, y: 1 },   // 右上
        { x: 1, y: -1 },  // 右下
        { x: -1, y: -1 }, // 左下
        { x: -1, y: 1 },  // 左上
      );
    }
    
    directions.forEach(dir => {
      const newX = x + dir.x;
      const newY = y + dir.y;
      
      if (coordinateUtils.isInBounds(newX, newY)) {
        neighbors.push({ x: newX, y: newY });
      }
    });
    
    return neighbors;
  },

  /**
   * 生成坐标键
   * @param x X坐标
   * @param y Y坐标
   * @returns 坐标键
   */
  getCoordinateKey: (x: number, y: number): string => {
    return `${x},${y}`;
  },

  /**
   * 解析坐标键
   * @param key 坐标键
   * @returns 坐标
   */
  parseCoordinateKey: (key: string): { x: number; y: number } => {
    const [x, y] = key.split(',').map(Number);
    return { x, y };
  },

  /**
   * 获取网格中心坐标
   * @returns 中心坐标
   */
  getCenter: (): { x: number; y: number } => {
    return { x: 0, y: 0 };
  },

  /**
   * 计算坐标到中心的距离
   * @param x X坐标
   * @param y Y坐标
   * @returns 到中心的距离
   */
  distanceFromCenter: (x: number, y: number): number => {
    return coordinateUtils.euclideanDistance(x, y, 0, 0);
  },

  /**
   * 获取指定半径内的所有坐标
   * @param centerX 中心X坐标
   * @param centerY 中心Y坐标
   * @param radius 半径
   * @returns 坐标数组
   */
  getCoordinatesInRadius: (centerX: number, centerY: number, radius: number): Array<{ x: number; y: number }> => {
    const coordinates: Array<{ x: number; y: number }> = [];
    
    for (let x = centerX - radius; x <= centerX + radius; x++) {
      for (let y = centerY - radius; y <= centerY + radius; y++) {
        if (coordinateUtils.isInBounds(x, y)) {
          const distance = coordinateUtils.euclideanDistance(centerX, centerY, x, y);
          if (distance <= radius) {
            coordinates.push({ x, y });
          }
        }
      }
    }
    
    return coordinates;
  },
};

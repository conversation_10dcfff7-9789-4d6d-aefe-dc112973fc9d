/**
 * Formatters
 * 
 * 格式化工具函数
 * 🎯 核心价值：提供数据格式化和显示功能
 * 📦 功能范围：数值格式化、文本格式化、显示转换
 */

export const formatters = {
  /**
   * 格式化坐标显示
   * @param x X坐标
   * @param y Y坐标
   * @returns 格式化的坐标字符串
   */
  formatCoordinate: (x: number, y: number): string => {
    return `(${x}, ${y})`;
  },

  /**
   * 格式化数值显示
   * @param value 数值
   * @param precision 精度
   * @returns 格式化的数值字符串
   */
  formatNumber: (value: number, precision: number = 2): string => {
    return value.toFixed(precision);
  },

  /**
   * 格式化百分比显示
   * @param value 数值（0-1）
   * @returns 格式化的百分比字符串
   */
  formatPercentage: (value: number): string => {
    return `${(value * 100).toFixed(1)}%`;
  },

  /**
   * 格式化颜色值
   * @param color 颜色值
   * @returns 格式化的颜色字符串
   */
  formatColor: (color: string): string => {
    return color.toUpperCase();
  },

  /**
   * 格式化时间戳
   * @param timestamp 时间戳
   * @returns 格式化的时间字符串
   */
  formatTimestamp: (timestamp: number): string => {
    return new Date(timestamp).toLocaleString();
  },

  /**
   * 格式化文件大小
   * @param bytes 字节数
   * @returns 格式化的文件大小字符串
   */
  formatFileSize: (bytes: number): string => {
    const units = ['B', 'KB', 'MB', 'GB'];
    let size = bytes;
    let unitIndex = 0;
    
    while (size >= 1024 && unitIndex < units.length - 1) {
      size /= 1024;
      unitIndex++;
    }
    
    return `${size.toFixed(1)} ${units[unitIndex]}`;
  },
};

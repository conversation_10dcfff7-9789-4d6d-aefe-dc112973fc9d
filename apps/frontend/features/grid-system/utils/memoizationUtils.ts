/**
 * Memoization Utilities
 * 
 * 记忆化工具函数
 * 🎯 核心价值：提供缓存和记忆化功能，优化性能
 * 📦 功能范围：函数缓存、结果记忆、缓存管理
 */

export const memoizationUtils = {
  /**
   * 创建记忆化函数
   * @param fn 要记忆化的函数
   * @param keyGenerator 键生成器
   * @returns 记忆化后的函数
   */
  memoize: <T extends (...args: unknown[]) => any>(
    fn: T,
    keyGenerator?: (...args: Parameters<T>) => string
  ): T => {
    const cache = new Map<string, ReturnType<T>>();
    
    return ((...args: Parameters<T>): ReturnType<T> => {
      const key = keyGenerator ? keyGenerator(...args) : JSON.stringify(args);
      
      if (cache.has(key)) {
        return cache.get(key)!;
      }
      
      const result = fn(...args);
      cache.set(key, result);
      return result;
    }) as T;
  },

  /**
   * 创建带过期时间的缓存
   * @param ttl 生存时间（毫秒）
   * @returns 缓存对象
   */
  createTTLCache: <K, V>(ttl: number) => {
    const cache = new Map<K, { value: V; expiry: number }>();
    
    return {
      get: (key: K): V | undefined => {
        const item = cache.get(key);
        if (!item) return undefined;
        
        if (Date.now() > item.expiry) {
          cache.delete(key);
          return undefined;
        }
        
        return item.value;
      },
      
      set: (key: K, value: V): void => {
        cache.set(key, {
          value,
          expiry: Date.now() + ttl,
        });
      },
      
      delete: (key: K): boolean => {
        return cache.delete(key);
      },
      
      clear: (): void => {
        cache.clear();
      },
      
      size: (): number => {
        return cache.size;
      },
    };
  },

  /**
   * 创建LRU缓存
   * @param maxSize 最大大小
   * @returns LRU缓存对象
   */
  createLRUCache: <K, V>(maxSize: number) => {
    const cache = new Map<K, V>();
    
    return {
      get: (key: K): V | undefined => {
        if (cache.has(key)) {
          const value = cache.get(key)!;
          // 重新插入以更新顺序
          cache.delete(key);
          cache.set(key, value);
          return value;
        }
        return undefined;
      },
      
      set: (key: K, value: V): void => {
        if (cache.has(key)) {
          cache.delete(key);
        } else if (cache.size >= maxSize) {
          // 删除最旧的项目
          const firstKey = cache.keys().next().value;
          if (firstKey !== undefined) {
            cache.delete(firstKey);
          }
        }
        cache.set(key, value);
      },
      
      delete: (key: K): boolean => {
        return cache.delete(key);
      },
      
      clear: (): void => {
        cache.clear();
      },
      
      size: (): number => {
        return cache.size;
      },
    };
  },
};

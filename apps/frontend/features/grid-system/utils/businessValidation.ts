/**
 * Business Validation Utilities
 * 
 * 网格系统业务特定的验证逻辑
 * 🎯 核心价值：提供业务层面的验证规则和逻辑
 * 📦 功能范围：业务规则验证、工作流验证、权限验证
 */

import type { CellData } from '@/lib/types/grid';
import type { GridBusinessConfig, GridInteractionState } from '../types';
import { validateCoordinates, validateBusinessConfig, type ValidationResult } from '@/lib/validation';

/**
 * 验证单元格是否可以被编辑
 * @param cell 单元格数据
 * @param config 业务配置
 * @param interactionState 交互状态
 * @returns 验证结果
 */
export const validateCellEditable = (
  cell: CellData,
  config: GridBusinessConfig,
  interactionState: GridInteractionState
): ValidationResult => {
  const errors: string[] = [];
  const warnings: string[] = [];

  // 检查交互模式
  if (config.interactionMode !== 'edit') {
    errors.push('当前模式不允许编辑');
    return { isValid: false, errors, warnings };
  }

  // 检查单元格是否激活
  if (!cell.isActive) {
    errors.push('非激活单元格不能编辑');
  }

  // 编辑状态检查已简化

  // 检查单元格是否被锁定（业务规则）
  if (cell.group === null) {
    warnings.push('特殊单元格编辑需要谨慎');
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
  };
};

/**
 * 验证单元格选择操作
 * @param cells 要选择的单元格
 * @param config 业务配置
 * @param currentSelection 当前选择状态
 * @returns 验证结果
 */
export const validateCellSelection = (
  cells: CellData[],
  config: GridBusinessConfig,
  currentSelection: Set<string>
): ValidationResult => {
  const errors: string[] = [];
  const warnings: string[] = [];

  // 检查选择模式
  if (config.selectionMode === 'single' && cells.length > 1) {
    errors.push('单选模式下只能选择一个单元格');
  }

  // 检查选择数量限制
  const totalSelected = currentSelection.size + cells.length;
  const maxSelection = getMaxSelectionCount(config.selectionMode);
  
  if (totalSelected > maxSelection) {
    errors.push(`选择数量超出限制：最多 ${maxSelection} 个`);
  }

  // 检查单元格是否可选择
  const unselectableCells = cells.filter(cell => !cell.isActive);
  if (unselectableCells.length > 0) {
    warnings.push(`${unselectableCells.length} 个单元格不可选择`);
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
  };
};

/**
 * 验证批量操作
 * @param cells 要操作的单元格
 * @param operation 操作类型
 * @param config 业务配置
 * @returns 验证结果
 */
export const validateBatchOperation = (
  cells: CellData[],
  operation: 'update' | 'delete' | 'copy' | 'paste',
  config: GridBusinessConfig
): ValidationResult => {
  const errors: string[] = [];
  const warnings: string[] = [];

  // 检查操作权限
  if (config.interactionMode === 'view') {
    errors.push('查看模式下不允许批量操作');
    return { isValid: false, errors, warnings };
  }

  // 检查批量大小
  if (cells.length > config.batchSize) {
    errors.push(`批量操作数量超出限制：${cells.length} > ${config.batchSize}`);
  }

  // 检查操作类型特定的规则
  switch (operation) {
    case 'update':
      const nonEditableCells = cells.filter(cell => !cell.isActive);
      if (nonEditableCells.length > 0) {
        warnings.push(`${nonEditableCells.length} 个单元格不可编辑`);
      }
      break;

    case 'delete':
      const specialCells = cells.filter(cell => cell.group === null);
      if (specialCells.length > 0) {
        warnings.push(`${specialCells.length} 个特殊单元格删除需要确认`);
      }
      break;

    case 'copy':
      if (cells.length === 0) {
        errors.push('没有选择要复制的单元格');
      }
      break;

    case 'paste':
      // 粘贴操作的特殊验证
      if (config.selectionMode === 'single' && cells.length > 1) {
        errors.push('单选模式下不支持多单元格粘贴');
      }
      break;
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
  };
};

/**
 * 验证网格配置更新
 * @param newConfig 新配置
 * @param currentConfig 当前配置
 * @returns 验证结果
 */
export const validateConfigUpdate = (
  newConfig: Partial<GridBusinessConfig>,
  currentConfig: GridBusinessConfig
): ValidationResult => {
  const errors: string[] = [];
  const warnings: string[] = [];

  // 使用通用验证
  const baseValidation = validateBusinessConfig(newConfig);
  errors.push(...baseValidation.errors);
  warnings.push(...baseValidation.warnings);

  // 业务特定的验证规则
  if (newConfig.interactionMode && newConfig.interactionMode !== currentConfig.interactionMode) {
    // 模式切换验证
    if (currentConfig.interactionMode === 'edit' && newConfig.interactionMode !== 'edit') {
      warnings.push('切换模式将退出编辑状态');
    }
  }

  if (newConfig.selectionMode && newConfig.selectionMode !== currentConfig.selectionMode) {
    // 选择模式切换验证
    warnings.push('切换选择模式将清空当前选择');
  }

  if (newConfig.autoSave !== undefined && newConfig.autoSave !== currentConfig.autoSave) {
    if (!newConfig.autoSave && currentConfig.autoSave) {
      warnings.push('关闭自动保存后需要手动保存更改');
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
  };
};

/**
 * 验证数据保存操作
 * @param hasUnsavedChanges 是否有未保存的更改
 * @param config 业务配置
 * @returns 验证结果
 */
export const validateSaveOperation = (
  hasUnsavedChanges: boolean,
  config: GridBusinessConfig
): ValidationResult => {
  const errors: string[] = [];
  const warnings: string[] = [];

  if (!hasUnsavedChanges) {
    warnings.push('没有需要保存的更改');
  }

  if (config.interactionMode === 'view') {
    errors.push('查看模式下不允许保存操作');
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
  };
};

/**
 * 获取最大选择数量
 * @param selectionMode 选择模式
 * @returns 最大选择数量
 */
function getMaxSelectionCount(selectionMode: string): number {
  switch (selectionMode) {
    case 'single':
      return 1;
    case 'multiple':
      return 100; // 合理的多选限制
    case 'range':
      return 1000; // 范围选择可以更多
    default:
      return 1;
  }
}

/**
 * 验证工作流状态转换
 * @param fromState 当前状态
 * @param toState 目标状态
 * @param context 上下文信息
 * @returns 验证结果
 */
export const validateWorkflowTransition = (
  fromState: string,
  toState: string,
  context: {
    hasUnsavedChanges: boolean;
    isEditing: boolean;
    selectedCells: number;
  }
): ValidationResult => {
  const errors: string[] = [];
  const warnings: string[] = [];

  // 定义状态转换规则
  const allowedTransitions: Record<string, string[]> = {
    'view': ['edit', 'select'],
    'edit': ['view', 'select'],
    'select': ['view', 'edit'],
  };

  // 检查转换是否允许
  if (!allowedTransitions[fromState]?.includes(toState)) {
    errors.push(`不允许从 ${fromState} 状态转换到 ${toState} 状态`);
    return { isValid: false, errors, warnings };
  }

  // 检查转换条件
  if (fromState === 'edit' && context.isEditing) {
    warnings.push('正在编辑中，切换状态将丢失当前编辑');
  }

  if (context.hasUnsavedChanges && toState === 'view') {
    warnings.push('有未保存的更改，切换到查看模式前建议保存');
  }

  if (fromState === 'select' && context.selectedCells > 0 && toState !== 'edit') {
    warnings.push('切换状态将清空当前选择');
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
  };
};

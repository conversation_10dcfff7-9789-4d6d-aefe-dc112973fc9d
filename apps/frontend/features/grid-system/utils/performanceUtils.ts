/**
 * Performance Utilities
 * 
 * 性能优化工具函数
 * 🎯 核心价值：提供性能监控和优化功能
 * 📦 功能范围：性能测量、内存监控、优化建议
 */

export const performanceUtils = {
  /**
   * 测量函数执行时间
   * @param fn 要测量的函数
   * @param label 标签
   * @returns 函数结果和执行时间
   */
  measureTime: <T>(fn: () => T, label: string): { result: T; time: number } => {
    const start = performance.now();
    const result = fn();
    const time = performance.now() - start;
    
    process.env.NODE_ENV === 'development' && console.log(`[Performance] ${label}: ${time.toFixed(2)}ms`);
    
    return { result, time };
  },

  /**
   * 创建防抖函数
   * @param fn 要防抖的函数
   * @param delay 延迟时间
   * @returns 防抖后的函数
   */
  debounce: <T extends (...args: unknown[]) => any>(fn: T, delay: number): T => {
    let timeoutId: NodeJS.Timeout;
    return ((...args: unknown[]) => {
      clearTimeout(timeoutId);
      timeoutId = setTimeout(() => fn(...args), delay);
    }) as T;
  },

  /**
   * 创建节流函数
   * @param fn 要节流的函数
   * @param delay 延迟时间
   * @returns 节流后的函数
   */
  throttle: <T extends (...args: unknown[]) => any>(fn: T, delay: number): T => {
    let lastCall = 0;
    return ((...args: unknown[]) => {
      const now = Date.now();
      if (now - lastCall >= delay) {
        lastCall = now;
        return fn(...args);
      }
    }) as T;
  },

  /**
   * 获取内存使用情况
   * @returns 内存使用信息
   */
  getMemoryUsage: (): unknown => {
    if ('memory' in performance) {
      return (performance as any).memory;
    }
    return null;
  },

  /**
   * 批处理函数
   * @param items 要处理的项目
   * @param batchSize 批次大小
   * @param processor 处理函数
   * @returns Promise
   */
  batchProcess: async <T, R>(
    items: T[],
    batchSize: number,
    processor: (batch: T[]) => Promise<R[]>
  ): Promise<R[]> => {
    const results: R[] = [];
    
    for (let i = 0; i < items.length; i += batchSize) {
      const batch = items.slice(i, i + batchSize);
      const batchResults = await processor(batch);
      results.push(...batchResults);
      
      // 让出控制权，避免阻塞UI
      await new Promise(resolve => setTimeout(resolve, 0));
    }
    
    return results;
  },
};

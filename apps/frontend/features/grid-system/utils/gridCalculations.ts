/**
 * Grid Calculations Utilities
 * 
 * 网格计算相关的工具函数
 * 🎯 核心价值：提供网格布局、坐标转换、距离计算等功能
 * 📦 功能范围：坐标系转换、布局计算、几何运算
 */

import type { CellData } from '@/lib/types/grid';

// 网格计算配置
export interface GridCalculationOptions {
  gridSize: number;
  cellSize: number;
  gap: number;
  padding: number;
  centerBased: boolean;
}

// 默认配置
const DEFAULT_OPTIONS: GridCalculationOptions = {
  gridSize: 33,
  cellSize: 24,
  gap: 2,
  padding: 16,
  centerBased: true,
};

/**
 * 网格计算工具类
 */
export const gridCalculations = {
  /**
   * 将网格坐标转换为像素坐标
   * @param x 网格X坐标
   * @param y 网格Y坐标
   * @param options 计算选项
   * @returns 像素坐标
   */
  gridToPixel: (x: number, y: number, options: Partial<GridCalculationOptions> = {}): { x: number; y: number } => {
    const opts = { ...DEFAULT_OPTIONS, ...options };
    const center = Math.floor(opts.gridSize / 2);
    
    if (opts.centerBased) {
      // 以中心为原点的坐标系
      const pixelX = opts.padding + (x + center) * (opts.cellSize + opts.gap);
      const pixelY = opts.padding + (center - y) * (opts.cellSize + opts.gap);
      return { x: pixelX, y: pixelY };
    } else {
      // 以左上角为原点的坐标系
      const pixelX = opts.padding + x * (opts.cellSize + opts.gap);
      const pixelY = opts.padding + y * (opts.cellSize + opts.gap);
      return { x: pixelX, y: pixelY };
    }
  },

  /**
   * 将像素坐标转换为网格坐标
   * @param pixelX 像素X坐标
   * @param pixelY 像素Y坐标
   * @param options 计算选项
   * @returns 网格坐标
   */
  pixelToGrid: (pixelX: number, pixelY: number, options: Partial<GridCalculationOptions> = {}): { x: number; y: number } => {
    const opts = { ...DEFAULT_OPTIONS, ...options };
    const center = Math.floor(opts.gridSize / 2);
    
    const gridX = Math.floor((pixelX - opts.padding) / (opts.cellSize + opts.gap));
    const gridY = Math.floor((pixelY - opts.padding) / (opts.cellSize + opts.gap));
    
    if (opts.centerBased) {
      // 转换为以中心为原点的坐标系
      return {
        x: gridX - center,
        y: center - gridY,
      };
    } else {
      return { x: gridX, y: gridY };
    }
  },

  /**
   * 计算网格的总尺寸
   * @param options 计算选项
   * @returns 总宽度和高度
   */
  calculateGridSize: (options: Partial<GridCalculationOptions> = {}): { width: number; height: number } => {
    const opts = { ...DEFAULT_OPTIONS, ...options };
    
    const width = opts.padding * 2 + opts.gridSize * opts.cellSize + (opts.gridSize - 1) * opts.gap;
    const height = opts.padding * 2 + opts.gridSize * opts.cellSize + (opts.gridSize - 1) * opts.gap;
    
    return { width, height };
  },

  /**
   * 计算两个单元格之间的距离
   * @param cell1 第一个单元格
   * @param cell2 第二个单元格
   * @returns 距离（网格单位）
   */
  calculateDistance: (cell1: CellData, cell2: CellData): number => {
    const dx = cell2.x - cell1.x;
    const dy = cell2.y - cell1.y;
    return Math.sqrt(dx * dx + dy * dy);
  },

  /**
   * 计算曼哈顿距离
   * @param cell1 第一个单元格
   * @param cell2 第二个单元格
   * @returns 曼哈顿距离
   */
  calculateManhattanDistance: (cell1: CellData, cell2: CellData): number => {
    return Math.abs(cell2.x - cell1.x) + Math.abs(cell2.y - cell1.y);
  },

  /**
   * 检查坐标是否在网格范围内
   * @param x X坐标
   * @param y Y坐标
   * @param options 计算选项
   * @returns 是否在范围内
   */
  isInBounds: (x: number, y: number, options: Partial<GridCalculationOptions> = {}): boolean => {
    const opts = { ...DEFAULT_OPTIONS, ...options };
    const halfSize = Math.floor(opts.gridSize / 2);
    
    if (opts.centerBased) {
      return x >= -halfSize && x <= halfSize && y >= -halfSize && y <= halfSize;
    } else {
      return x >= 0 && x < opts.gridSize && y >= 0 && y < opts.gridSize;
    }
  },

  /**
   * 获取单元格的邻居
   * @param x X坐标
   * @param y Y坐标
   * @param includesDiagonal 是否包含对角线邻居
   * @param options 计算选项
   * @returns 邻居坐标数组
   */
  getNeighbors: (
    x: number, 
    y: number, 
    includesDiagonal: boolean = false, 
    options: Partial<GridCalculationOptions> = {}
  ): Array<{ x: number; y: number }> => {
    const neighbors: Array<{ x: number; y: number }> = [];
    
    // 四个基本方向
    const directions = [
      { x: 0, y: 1 },   // 上
      { x: 1, y: 0 },   // 右
      { x: 0, y: -1 },  // 下
      { x: -1, y: 0 },  // 左
    ];
    
    // 对角线方向
    if (includesDiagonal) {
      directions.push(
        { x: 1, y: 1 },   // 右上
        { x: 1, y: -1 },  // 右下
        { x: -1, y: -1 }, // 左下
        { x: -1, y: 1 },  // 左上
      );
    }
    
    directions.forEach(dir => {
      const newX = x + dir.x;
      const newY = y + dir.y;
      
      if (gridCalculations.isInBounds(newX, newY, options)) {
        neighbors.push({ x: newX, y: newY });
      }
    });
    
    return neighbors;
  },

  /**
   * 计算网格中心点
   * @param options 计算选项
   * @returns 中心点坐标
   */
  getCenter: (options: Partial<GridCalculationOptions> = {}): { x: number; y: number } => {
    const opts = { ...DEFAULT_OPTIONS, ...options };
    
    if (opts.centerBased) {
      return { x: 0, y: 0 };
    } else {
      const center = Math.floor(opts.gridSize / 2);
      return { x: center, y: center };
    }
  },

  /**
   * 将行列索引转换为网格坐标
   * @param row 行索引
   * @param col 列索引
   * @param options 计算选项
   * @returns 网格坐标
   */
  rowColToGrid: (row: number, col: number, options: Partial<GridCalculationOptions> = {}): { x: number; y: number } => {
    const opts = { ...DEFAULT_OPTIONS, ...options };
    const center = Math.floor(opts.gridSize / 2);
    
    if (opts.centerBased) {
      return {
        x: col - center,
        y: center - row,
      };
    } else {
      return { x: col, y: row };
    }
  },

  /**
   * 将网格坐标转换为行列索引
   * @param x X坐标
   * @param y Y坐标
   * @param options 计算选项
   * @returns 行列索引
   */
  gridToRowCol: (x: number, y: number, options: Partial<GridCalculationOptions> = {}): { row: number; col: number } => {
    const opts = { ...DEFAULT_OPTIONS, ...options };
    const center = Math.floor(opts.gridSize / 2);
    
    if (opts.centerBased) {
      return {
        row: center - y,
        col: x + center,
      };
    } else {
      return { row: y, col: x };
    }
  },
};

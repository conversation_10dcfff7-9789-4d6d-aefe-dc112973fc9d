/**
 * Interaction Utilities
 * 
 * 网格系统交互工具函数
 * 🎯 核心价值：提供交互逻辑相关的工具函数
 * 📦 功能范围：事件处理、状态转换、用户交互
 */

import type { CellData } from '@/lib/types/grid';
import type { GridInteractionState } from '../types';

export const interactionUtils = {
  /**
   * 检查是否为多选操作
   * @param event 鼠标事件
   * @returns 是否为多选
   */
  isMultiSelectEvent: (event: React.MouseEvent): boolean => {
    return event.ctrlKey || event.metaKey;
  },

  /**
   * 检查是否为范围选择操作
   * @param event 鼠标事件
   * @returns 是否为范围选择
   */
  isRangeSelectEvent: (event: React.MouseEvent): boolean => {
    return event.shiftKey;
  },

  /**
   * 生成单元格键
   * @param cell 单元格数据
   * @returns 单元格键
   */
  getCellKey: (cell: CellData): string => {
    return `${cell.x},${cell.y}`;
  },

  /**
   * 解析单元格键
   * @param key 单元格键
   * @returns 坐标
   */
  parseCellKey: (key: string): { x: number; y: number } => {
    const [x, y] = key.split(',').map(Number);
    return { x, y };
  },

  /**
   * 检查拖拽是否有效
   * @param startCell 开始单元格
   * @param currentCell 当前单元格
   * @param minDistance 最小距离
   * @returns 是否有效拖拽
   */
  isValidDrag: (startCell: CellData, currentCell: CellData, minDistance: number = 1): boolean => {
    const distance = Math.abs(currentCell.x - startCell.x) + Math.abs(currentCell.y - startCell.y);
    return distance >= minDistance;
  },

  /**
   * 计算拖拽方向
   * @param startCell 开始单元格
   * @param endCell 结束单元格
   * @returns 拖拽方向
   */
  getDragDirection: (startCell: CellData, endCell: CellData): string => {
    const dx = endCell.x - startCell.x;
    const dy = endCell.y - startCell.y;
    
    if (Math.abs(dx) > Math.abs(dy)) {
      return dx > 0 ? 'right' : 'left';
    } else {
      return dy > 0 ? 'up' : 'down';
    }
  },

  /**
   * 防抖函数
   * @param func 要防抖的函数
   * @param delay 延迟时间
   * @returns 防抖后的函数
   */
  debounce: <T extends (...args: unknown[]) => any>(func: T, delay: number): T => {
    let timeoutId: NodeJS.Timeout;
    return ((...args: unknown[]) => {
      clearTimeout(timeoutId);
      timeoutId = setTimeout(() => func(...args), delay);
    }) as T;
  },

  /**
   * 节流函数
   * @param func 要节流的函数
   * @param delay 延迟时间
   * @returns 节流后的函数
   */
  throttle: <T extends (...args: unknown[]) => any>(func: T, delay: number): T => {
    let lastCall = 0;
    return ((...args: unknown[]) => {
      const now = Date.now();
      if (now - lastCall >= delay) {
        lastCall = now;
        return func(...args);
      }
    }) as T;
  },
};

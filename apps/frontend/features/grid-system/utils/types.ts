/**
 * Grid System Utils Types
 * 
 * 网格系统工具函数的类型定义
 * 🎯 核心价值：为工具函数提供类型安全
 * 📦 类型范围：工具函数参数、返回值、配置等
 */

// 网格计算选项
export interface GridCalculationOptions {
  gridSize: number;
  cellSize: number;
  gap: number;
  padding: number;
  centerBased: boolean;
}

// 坐标转换接口
export interface CoordinateTransform {
  fromGrid: (x: number, y: number) => { row: number; col: number };
  toGrid: (row: number, col: number) => { x: number; y: number };
  isValid: (x: number, y: number) => boolean;
}

// 数据转换选项
export interface DataTransformOptions {
  format: 'csv' | 'json' | 'xml';
  includeHeaders: boolean;
  delimiter?: string;
  encoding?: string;
}

// 性能指标
export interface PerformanceMetrics {
  renderTime: number;
  updateTime: number;
  memoryUsage: number;
  cellCount: number;
  fps: number;
  isOptimal: boolean;
}

// 记忆化配置
export interface MemoizationConfig {
  enabled: boolean;
  maxCacheSize: number;
  ttl: number; // 生存时间（毫秒）
  keyGenerator?: (...args: unknown[]) => string;
}

// 业务规则
export interface BusinessRules {
  allowEdit: boolean;
  allowDelete: boolean;
  allowCopy: boolean;
  allowPaste: boolean;
  maxSelection: number;
  requireConfirmation: boolean;
}

// 交互处理器
export interface InteractionHandlers {
  onClick: (event: React.MouseEvent) => void;
  onDoubleClick: (event: React.MouseEvent) => void;
  onHover: (event: React.MouseEvent) => void;
  onFocus: (event: React.FocusEvent) => void;
  onKeyDown: (event: React.KeyboardEvent) => void;
}

// 缓存项
export interface CacheItem<T> {
  value: T;
  timestamp: number;
  expiry?: number;
  accessCount: number;
}

// 批处理配置
export interface BatchProcessConfig {
  batchSize: number;
  delay: number;
  maxConcurrency: number;
  onProgress?: (processed: number, total: number) => void;
  onError?: (error: Error, item: any) => void;
}

// 验证规则
export interface ValidationRule<T> {
  name: string;
  validator: (value: T) => boolean;
  message: string;
  severity: 'error' | 'warning' | 'info';
}

// 格式化选项
export interface FormatOptions {
  precision?: number;
  locale?: string;
  currency?: string;
  dateFormat?: string;
  timeFormat?: string;
}

// 性能监控配置
export interface PerformanceMonitorConfig {
  enabled: boolean;
  sampleRate: number;
  thresholds: {
    renderTime: number;
    memoryUsage: number;
    fps: number;
  };
  onThresholdExceeded?: (metric: string, value: number, threshold: number) => void;
}

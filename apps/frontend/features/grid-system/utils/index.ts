/**
 * Grid System 功能工具函数导出
 * 🎯 核心价值：网格系统功能特定工具函数集中管理
 * 📦 工具范围：业务计算、数据转换、性能优化等功能特定工具
 * 🔄 重构说明：通用工具函数位于 @/lib/utils，验证逻辑位于 @/lib/validation
 */

// 网格计算工具
export { gridCalculations } from './gridCalculations'
export { coordinateUtils } from './coordinateUtils'

// 数据转换工具
export { dataTransformers } from './dataTransformers'
export { formatters } from './formatters'

// 性能优化工具
export { performanceUtils } from './performanceUtils'
export { memoizationUtils } from './memoizationUtils'

// 业务逻辑工具
export { businessLogicUtils } from './businessLogicUtils'
export { interactionUtils } from './interactionUtils'

// 业务验证工具
export {
  validateCellEditable,
  validateCellSelection,
  validateBatchOperation,
  validateConfigUpdate,
  validateSaveOperation,
  validateWorkflowTransition,
} from './businessValidation'

// 工具函数类型导出
export type {
  GridCalculationOptions,
  CoordinateTransform,
  DataTransformOptions,
  PerformanceMetrics,
  MemoizationConfig,
  BusinessRules,
  InteractionHandlers,
} from './types'

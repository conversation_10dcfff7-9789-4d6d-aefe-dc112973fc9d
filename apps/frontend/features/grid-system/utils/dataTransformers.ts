/**
 * Data Transformers
 * 
 * 数据转换工具函数
 * 🎯 核心价值：提供数据格式转换和处理功能
 * 📦 功能范围：数据格式化、转换、映射
 */

import type { CellData } from '@/lib/types/grid';

export const dataTransformers = {
  /**
   * 将网格数据转换为CSV格式
   * @param cells 网格数据
   * @returns CSV字符串
   */
  toCsv: (cells: CellData[][]): string => {
    const rows = cells.map(row => 
      row.map(cell => cell?.colorMappingValue || '').join(',')
    );
    return rows.join('\n');
  },

  /**
   * 将网格数据转换为JSON格式
   * @param cells 网格数据
   * @returns JSON字符串
   */
  toJson: (cells: CellData[][]): string => {
    const data = cells.map(row => 
      row.map(cell => ({
        x: cell.x,
        y: cell.y,
        value: cell.colorMappingValue,
        color: cell.color,
        isActive: cell.isActive,
      }))
    );
    return JSON.stringify(data, null, 2);
  },

  /**
   * 从JSON数据创建网格数据
   * @param jsonData JSON数据
   * @returns 网格数据
   */
  fromJson: (jsonData: string): CellData[][] | null => {
    try {
      const data = JSON.parse(jsonData);
      // 这里应该有完整的转换逻辑
      return data;
    } catch (error) {
      console.error('JSON解析失败:', error);
      return null;
    }
  },

  /**
   * 扁平化网格数据
   * @param cells 网格数据
   * @returns 扁平化的单元格数组
   */
  flatten: (cells: CellData[][]): CellData[] => {
    return cells.flat().filter(Boolean);
  },

  /**
   * 按条件过滤单元格
   * @param cells 网格数据
   * @param predicate 过滤条件
   * @returns 过滤后的单元格数组
   */
  filter: (cells: CellData[][], predicate: (cell: CellData) => boolean): CellData[] => {
    return dataTransformers.flatten(cells).filter(predicate);
  },
};

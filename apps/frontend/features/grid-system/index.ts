/**
 * Grid System 功能模块统一导出
 * 🎯 核心价值：网格系统业务逻辑集中管理
 * 📦 业务范围：网格渲染、单元格管理、网格交互、状态管理
 * ✅ 重构状态：按照方案A完全架构集成
 */

// 功能特定组件导出
export {
  GridContainer,
  GridControlPanel,
  GridOverlay,
} from './components'

// 业务逻辑Hooks导出
export {
  useCellDataManager,
  useGridDataManager,
  useGridInteraction,
  useGridSelection,
  useGridLayout,
  // TODO: 待实现的hooks
  // useGridBusinessConfig,
  // useGridPerformance,
} from './hooks'

// 功能状态管理导出
export {
  useGridInteractionStore,
  // TODO: 待实现
  // useGridSelectionStore,
} from './store'

// 功能特定类型导出
export type {
  GridControlPanelProps,
  GridContainerProps,
  GridOverlayProps,
  GridBusinessConfig,
  GridInteractionState,
  // TODO: 待实现
  // GridSelectionState,
  UseCellDataManagerReturn,
  UseGridDataManagerReturn,
  UseGridInteractionReturn,
  UseGridSelectionReturn,
  UseGridLayoutReturn,
  // TODO: 待实现的类型
  // UseGridBusinessConfigReturn,
  // UseGridPerformanceReturn,
} from './types'

// 功能工具函数导出
export {
  gridCalculations,
  coordinateUtils,
  dataTransformers,
  formatters,
  performanceUtils,
  memoizationUtils,
  businessLogicUtils,
  interactionUtils,
  validateCellEditable,
  validateCellSelection,
  validateBatchOperation,
  validateConfigUpdate,
  validateSaveOperation,
  validateWorkflowTransition,
} from './utils'

// 业务模块配置
export const GRID_SYSTEM_CONFIG = {
  name: 'grid-system',
  version: '2.0.0',
  description: '网格系统业务模块 - 方案A重构版本',
  features: [
    'grid-rendering',
    'cell-management', 
    'grid-interaction',
    'state-management',
    'business-logic',
  ],
  architecture: 'features-based',
  migrationFrom: 'components/grid-system',
} as const;

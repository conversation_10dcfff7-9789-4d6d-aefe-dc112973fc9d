/**
 * Grid System 功能特定组件导出
 * 🎯 核心价值：网格系统业务组件集中管理
 * 📦 组件范围：功能特定的组合组件、控制面板等
 */

// 功能特定组件导出
// 注意：纯UI组件(GridMatrix, GridCell)位于 @/components/grid-system
// 这里只包含业务逻辑相关的组合组件

// 网格控制面板组件（业务逻辑组件）
export { GridControlPanel } from './GridControlPanel'

// 网格容器组合组件（包含业务逻辑）
export { GridContainer } from './GridContainer'

// 网格覆盖层组件（功能特定）
export { GridOverlay } from './GridOverlay'

// 组件类型导出
export type {
  GridControlPanelProps,
  GridContainerProps,
  GridOverlayProps,
} from './types'

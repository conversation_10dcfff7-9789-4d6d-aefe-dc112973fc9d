/**
 * GridOverlay Component - 业务逻辑版本
 * 
 * 网格覆盖层组件，用于显示提示、加载状态、错误信息等
 * 🎯 核心价值：提供网格系统的覆盖层功能，增强用户体验
 * 📦 功能范围：信息展示、状态提示、用户引导
 */

'use client';

import React, { memo } from 'react';
import type { GridOverlayProps } from '../types';

/**
 * GridOverlay - 网格覆盖层组件
 * 在网格上方显示各种信息和状态
 */
export const GridOverlay = memo<GridOverlayProps>(({
  visible,
  content,
  position = 'center',
  className = '',
}) => {
  if (!visible) {
    return null;
  }

  const positionClasses = {
    center: 'items-center justify-center',
    top: 'items-start justify-center pt-8',
    bottom: 'items-end justify-center pb-8',
  };

  return (
    <div
      className={`
        grid-overlay
        absolute inset-0 z-50
        flex ${positionClasses[position]}
        bg-black bg-opacity-50
        transition-opacity duration-200
        ${className}
      `}
      role="dialog"
      aria-modal="true"
    >
      <div
        className="
          overlay-content
          max-w-md mx-4 p-6
          bg-white rounded-lg shadow-lg
          transform transition-transform duration-200
          scale-100
        "
      >
        {content}
      </div>
    </div>
  );
});

GridOverlay.displayName = 'GridOverlay';

/**
 * GridContainer Component - 业务逻辑版本
 * 
 * 网格容器组合组件，包含业务逻辑和状态管理
 * 🎯 核心价值：提供完整的网格功能，包括数据管理、交互逻辑、状态同步
 * 📦 功能范围：业务逻辑组合、状态管理、事件处理
 * 🔄 架构说明：使用纯UI组件(GridMatrix)进行渲染，本组件负责业务逻辑
 */

'use client';

import React, { memo, useCallback, useEffect, useMemo } from 'react';
import { GridMatrix } from '@/components/grid-system';
import { logger } from '@/lib/hooks/useLogManager';
import { useGridDataManager } from '../hooks/useGridDataManager';
import { useGridInteraction } from '../hooks/useGridInteraction';
import { useGridInteractionStore } from '../store/gridInteractionStore';

import type { GridContainerProps } from '../types';
import type { CellData } from '@/lib/types/grid';

/**
 * GridContainer - 网格容器组合组件
 * 结合业务逻辑和UI渲染的完整网格解决方案
 */
const GridContainer = memo<GridContainerProps>(({
  config,
  onCellClick,
  onCellHover,
  className,
}) => {
  // 业务数据管理
  const {
    refreshData,
    saveData,
    isDirty,
    isLoading,
    error,
  } = useGridDataManager();

  // 交互逻辑管理（精简版）
  const {
    interactionState,
    handleCellClick: handleInteractionClick,
    handleCellHover: handleInteractionHover,
  } = useGridInteraction();

  // 全局交互状态
  const {
    hoveredCell,
    selectedCells,
    focusedCell,
    setHoveredCell,
    addSelectedCell,
    removeSelectedCell,
    clearSelection,
  } = useGridInteractionStore();

  // 处理单元格点击事件
  const handleCellClickInternal = useCallback((cell: CellData, event: React.MouseEvent) => {
    try {
      // 调用交互逻辑处理
      handleInteractionClick(cell, event);
      
      // 更新全局状态
      const cellKey = `${cell.x},${cell.y}`;
      if (event.ctrlKey || event.metaKey) {
        // Ctrl/Cmd + 点击：切换选择
        if (selectedCells.has(cellKey)) {
          removeSelectedCell(cellKey);
        } else {
          addSelectedCell(cellKey);
        }
      } else {
        // 普通点击：清空选择并选中当前单元格
        clearSelection();
        addSelectedCell(cellKey);
      }
      
      // 调用外部回调
      onCellClick?.(cell, event);
      
      logger.debug(`GridContainer: 单元格点击 (${cell.x}, ${cell.y})`, undefined, 'useGridData');
    } catch (error) {
      logger.error('GridContainer: 处理单元格点击失败:', error, 'useGridData');
    }
  }, [
    handleInteractionClick,
    selectedCells,
    addSelectedCell,
    removeSelectedCell,
    clearSelection,
    onCellClick,
  ]);

  // 处理单元格悬停事件
  const handleCellHoverInternal = useCallback((cell: CellData | null) => {
    try {
      // 调用交互逻辑处理
      handleInteractionHover(cell);
      
      // 更新全局状态
      setHoveredCell(cell);
      
      // 调用外部回调
      onCellHover?.(cell);
      
      if (cell) {
        logger.debug(`GridContainer: 单元格悬停 (${cell.x}, ${cell.y})`, undefined, 'useGridData');
      }
    } catch (error) {
      logger.error('GridContainer: 处理单元格悬停失败:', error, 'useGridData');
    }
  }, [handleInteractionHover, setHoveredCell, onCellHover]);

  // 键盘事件处理（精简版，移除编辑功能）
  const handleKeyDown = useCallback((event: React.KeyboardEvent) => {
    if (!focusedCell) return;

    try {
      switch (event.key) {
        case 'Escape':
          // 清空选择
          clearSelection();
          event.preventDefault();
          break;

        case 'Delete':
        case 'Backspace':
          // 删除选中的单元格内容
          // 这里可以实现删除逻辑
          event.preventDefault();
          break;

        case 'ArrowUp':
        case 'ArrowDown':
        case 'ArrowLeft':
        case 'ArrowRight':
          // 方向键导航
          // 这里可以实现键盘导航逻辑
          event.preventDefault();
          break;
      }
    } catch (error) {
      logger.error('GridContainer: 键盘事件处理失败:', error, 'useGridData');
    }
  }, [focusedCell, clearSelection]);

  // 自动保存逻辑
  useEffect(() => {
    if (isDirty && config?.autoSave) {
      const timer = setTimeout(() => {
        saveData();
      }, 2000); // 2秒后自动保存

      return () => clearTimeout(timer);
    }
  }, [isDirty, config?.autoSave, saveData]);

  // 错误处理
  useEffect(() => {
    if (error) {
      // 这里可以显示错误提示
    }
  }, [error]);

  // 生成传递给UI组件的配置
  const uiConfig = useMemo(() => ({
    size: 24,
    cellShape: 'square' as const,
    displayMode: config?.displayMode || 'value' as const,
    gap: 2,
    padding: 16,
    fontSize: 12,
    scale: {
      enabled: true,
      factor: 1.1,
    },
    animation: {
      enabled: true,
      duration: 200,
    },
  }), [config]);

  // 渲染加载状态
  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="text-gray-500">加载网格数据中...</div>
      </div>
    );
  }

  // 渲染错误状态
  if (error) {
    return (
      <div className="flex flex-col items-center justify-center p-8 text-red-500">
        <div className="mb-2">网格数据加载失败</div>
        <button
          onClick={refreshData}
          className="px-4 py-2 bg-red-100 text-red-700 rounded hover:bg-red-200"
        >
          重试
        </button>
      </div>
    );
  }

  return (
    <div
      className={`grid-container ${className || ''}`}
      onKeyDown={handleKeyDown}
      tabIndex={0}
      role="grid"
      aria-label="网格容器"
    >
      <GridMatrix
        config={uiConfig}
        onCellClick={handleCellClickInternal}
        onCellHover={handleCellHoverInternal}
        className="grid-matrix"
      />
      
      {/* 调试信息（开发环境） */}
      {process.env.NODE_ENV === 'development' && (
        <div className="mt-4 p-2 bg-gray-100 text-xs">
          <div>选中单元格: {selectedCells.size}</div>
          <div>悬停单元格: {hoveredCell ? `(${hoveredCell.x}, ${hoveredCell.y})` : '无'}</div>
          <div>数据状态: {isDirty ? '已修改' : '已保存'}</div>
          <div>交互状态: 查看中</div>
        </div>
      )}
    </div>
  );
});

GridContainer.displayName = 'GridContainer';

export { GridContainer };

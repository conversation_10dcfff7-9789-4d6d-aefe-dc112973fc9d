/**
 * GridControlPanel Component - 业务逻辑版本
 * 
 * 网格控制面板组件，提供网格配置和控制功能
 * 🎯 核心价值：提供网格系统的控制界面，包括显示模式、配置选项等
 * 📦 功能范围：配置管理、模式切换、用户控制
 */

'use client';

import React, { memo, useCallback, useState } from 'react';
import { useGridConfigStore } from '@/stores/gridConfigStore';
import type { GridControlPanelProps } from '../types';

/**
 * GridControlPanel - 网格控制面板组件
 * 提供网格系统的各种控制选项
 */
export const GridControlPanel = memo<GridControlPanelProps>(({
  onDisplayModeChange,
  onGrayModeToggle,
  onConfigChange,
  className = '',
}) => {
  // 获取网格配置状态
  const baseDisplayMode = useGridConfigStore((state) => state.baseDisplayMode);
  const setDisplayMode = useGridConfigStore((state) => state.setDisplayMode);

  // 处理显示模式变更
  const handleDisplayModeChange = useCallback((mode: string) => {
    setDisplayMode(mode as any);
    onDisplayModeChange?.(mode);
  }, [setDisplayMode, onDisplayModeChange]);

  // 处理灰度模式切换 - 暂时使用本地状态
  const [isGrayModeEnabled, setIsGrayModeEnabled] = useState(false);
  const handleGrayModeToggle = useCallback(() => {
    const newEnabled = !isGrayModeEnabled;
    setIsGrayModeEnabled(newEnabled);
    onGrayModeToggle?.(newEnabled);
  }, [isGrayModeEnabled, onGrayModeToggle]);

  // 处理配置变更
  const handleConfigChange = useCallback((key: string, value: any) => {
    const config = { [key]: value };
    onConfigChange?.(config);
  }, [onConfigChange]);

  return (
    <div className={`grid-control-panel p-4 bg-white border rounded-lg shadow-sm ${className}`}>
      <h3 className="text-lg font-medium mb-4">网格控制面板</h3>
      
      {/* 显示模式控制 */}
      <div className="mb-4">
        <label className="block text-sm font-medium text-gray-700 mb-2">
          显示模式
        </label>
        <div className="flex space-x-2">
          {['value', 'coordinates', 'color'].map((mode) => (
            <button
              key={mode}
              onClick={() => handleDisplayModeChange(mode)}
              className={`px-3 py-1 text-sm rounded ${
                baseDisplayMode === mode
                  ? 'bg-blue-500 text-white'
                  : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
              }`}
            >
              {mode === 'value' ? '数值' : mode === 'coordinates' ? '坐标' : '颜色'}
            </button>
          ))}
        </div>
      </div>

      {/* 灰度模式控制 */}
      <div className="mb-4">
        <label className="flex items-center">
          <input
            type="checkbox"
            checked={isGrayModeEnabled}
            onChange={handleGrayModeToggle}
            className="mr-2"
          />
          <span className="text-sm font-medium text-gray-700">
            启用灰度模式
          </span>
        </label>
      </div>

      {/* 其他配置选项 */}
      <div className="space-y-3">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            单元格大小
          </label>
          <input
            type="range"
            min="12"
            max="48"
            defaultValue="24"
            onChange={(e) => handleConfigChange('cellSize', parseInt(e.target.value))}
            className="w-full"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            字体大小
          </label>
          <input
            type="range"
            min="8"
            max="20"
            defaultValue="12"
            onChange={(e) => handleConfigChange('fontSize', parseInt(e.target.value))}
            className="w-full"
          />
        </div>

        <div>
          <label className="flex items-center">
            <input
              type="checkbox"
              defaultChecked={true}
              onChange={(e) => handleConfigChange('animationEnabled', e.target.checked)}
              className="mr-2"
            />
            <span className="text-sm font-medium text-gray-700">
              启用动画效果
            </span>
          </label>
        </div>
      </div>
    </div>
  );
});

GridControlPanel.displayName = 'GridControlPanel';

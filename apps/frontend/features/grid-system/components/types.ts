/**
 * Grid System Components Types
 * 
 * 网格系统业务组件的类型定义
 * 🎯 核心价值：为业务组件提供类型安全
 * 📦 类型范围：业务组件props、配置、状态等
 */

import type { CellData } from '@/lib/types/grid';
import type { GridBusinessConfig } from '../types';

// GridControlPanel组件Props
export interface GridControlPanelProps {
  onDisplayModeChange?: (mode: string) => void;
  onGrayModeToggle?: (enabled: boolean) => void;
  onConfigChange?: (config: Partial<GridBusinessConfig>) => void;
  className?: string;
}

// GridContainer组件Props
export interface GridContainerProps {
  data?: unknown[];
  config?: Partial<GridBusinessConfig>;
  onCellClick?: (cell: CellData, event: React.MouseEvent) => void;
  onCellHover?: (cell: CellData | null) => void;
  className?: string;
}

// GridOverlay组件Props
export interface GridOverlayProps {
  visible: boolean;
  content: React.ReactNode;
  position?: 'center' | 'top' | 'bottom';
  className?: string;
}

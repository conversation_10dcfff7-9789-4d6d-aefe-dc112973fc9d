/**
 * Grid System 业务逻辑Hooks导出
 * 🎯 核心价值：网格系统业务逻辑集中管理
 * 📦 Hook范围：数据管理、交互逻辑、渲染逻辑等业务相关hooks
 */

// 业务数据管理hooks
export { useCellDataManager } from './useCellDataManager'
export { useGridDataManager } from './useGridDataManager'

// 网格交互逻辑hooks
export { useGridInteraction } from './useGridInteraction'
export { useGridSelection } from './useGridSelection'

// 网格渲染逻辑hooks
export { useGridLayout } from './useGridLayout'

// 网格配置管理hooks（业务逻辑版本）
// TODO: 待实现
// export { useGridBusinessConfig } from './useGridBusinessConfig'

// 性能优化hooks
// TODO: 待实现
// export { useGridPerformance } from './useGridPerformance'

// Hook类型导出
export type {
  UseCellDataManagerReturn,
  UseGridDataManagerReturn,
  UseGridInteractionReturn,
  UseGridSelectionReturn,
  UseGridLayoutReturn,
  // TODO: 待实现的类型
  // UseGridBusinessConfigReturn,
  // UseGridPerformanceReturn,
} from './types'

/**
 * useGridLayout Hook
 * 
 * 网格布局逻辑的业务hook
 * 🎯 核心价值：管理网格的布局计算和位置
 * 📦 功能范围：布局计算、位置转换、尺寸管理
 */

import { useCallback, useMemo } from 'react';
import { GRID_CONSTANTS } from '@/stores/constants/grid';
import type { CellData } from '@/lib/types/grid';
import type { UseGridLayoutReturn, GridBusinessConfig } from '../types';

export const useGridLayout = (): UseGridLayoutReturn => {
  // 网格布局信息
  const gridLayout = useMemo(() => ({
    rows: GRID_CONSTANTS.GRID_SIZE,
    cols: GRID_CONSTANTS.GRID_SIZE,
    cellSize: GRID_CONSTANTS.DEFAULT_CELL_SIZE,
    gap: GRID_CONSTANTS.DEFAULT_GAP,
    totalWidth: GRID_CONSTANTS.GRID_SIZE * GRID_CONSTANTS.DEFAULT_CELL_SIZE + (GRID_CONSTANTS.GRID_SIZE - 1) * GRID_CONSTANTS.DEFAULT_GAP,
    totalHeight: GRID_CONSTANTS.GRID_SIZE * GRID_CONSTANTS.DEFAULT_CELL_SIZE + (GRID_CONSTANTS.GRID_SIZE - 1) * GRID_CONSTANTS.DEFAULT_GAP,
  }), []);

  // 计算单元格位置
  const calculateCellPosition = useCallback((x: number, y: number): { x: number; y: number } => {
    const center = GRID_CONSTANTS.CENTER_OFFSET;
    const col = x + center;
    const row = center - y;
    
    return {
      x: col * (gridLayout.cellSize + gridLayout.gap),
      y: row * (gridLayout.cellSize + gridLayout.gap),
    };
  }, [gridLayout]);

  // 根据像素位置获取单元格
  const getCellAtPosition = useCallback((clientX: number, clientY: number): CellData | null => {
    const col = Math.floor(clientX / (gridLayout.cellSize + gridLayout.gap));
    const row = Math.floor(clientY / (gridLayout.cellSize + gridLayout.gap));
    
    const center = GRID_CONSTANTS.CENTER_OFFSET;
    const x = col - center;
    const y = center - row;
    
    // 检查是否在有效范围内
    if (x < -center || x > center || y < -center || y > center) {
      return null;
    }
    
    // 创建临时单元格数据
    const cellData: CellData = {
      id: `cell-${x}-${y}`,
      row,
      col,
      x,
      y,
      index: row * GRID_CONSTANTS.GRID_SIZE + col,
      color: '#f3f4f6',
      colorMappingValue: 0,
      level: 1,
      group: null,
      isActive: true,
      number: row * GRID_CONSTANTS.GRID_SIZE + col,
    };
    
    return cellData;
  }, [gridLayout]);

  // 更新布局
  const updateLayout = useCallback((config: Partial<GridBusinessConfig>): void => {
    // 这里可以根据配置更新布局
    process.env.NODE_ENV === 'development' && console.log('更新布局配置:', config);
  }, []);

  return {
    gridLayout,
    calculateCellPosition,
    getCellAtPosition,
    updateLayout,
  };
};

/**
 * useGridSelection Hook
 * 
 * 网格选择逻辑的业务hook
 * 🎯 核心价值：管理网格的选择状态和逻辑
 * 📦 功能范围：选择管理、范围选择、多选逻辑
 */

import { useCallback, useState } from 'react';
import { logger } from '@/lib/hooks/useLogManager';
import type { CellData } from '@/lib/types/grid';
import type { UseGridSelectionReturn, GridSelectionState } from '../types';

export const useGridSelection = (): UseGridSelectionReturn => {
  const [selectionState, setSelectionState] = useState<GridSelectionState>({
    selectedCells: new Map<string, CellData>(),
    selectionMode: 'single',
    selectionStart: null,
    selectionEnd: null,
    isSelecting: false,
  });

  // 选择单元格
  const selectCell = useCallback((cell: CellData, mode: 'replace' | 'add' | 'toggle' = 'replace'): void => {
    try {
      setSelectionState(prev => {
        const newSelectedCells = new Map(prev.selectedCells);
        const cellKey = `${cell.x},${cell.y}`;

        switch (mode) {
          case 'replace':
            newSelectedCells.clear();
            newSelectedCells.set(cellKey, cell);
            break;
          
          case 'add':
            newSelectedCells.set(cellKey, cell);
            break;
          
          case 'toggle':
            if (newSelectedCells.has(cellKey)) {
              newSelectedCells.delete(cellKey);
            } else {
              newSelectedCells.set(cellKey, cell);
            }
            break;
        }

        logger.debug(`选择单元格: (${cell.x}, ${cell.y}), 模式: ${mode}, 总数: ${newSelectedCells.size}`, undefined, 'useGridData');

        return {
          ...prev,
          selectedCells: newSelectedCells,
        };
      });
    } catch (error) {
      logger.error('选择单元格失败:', error, 'useGridData');
    }
  }, []);

  // 范围选择
  const selectRange = useCallback((start: CellData, end: CellData): void => {
    try {
      setSelectionState(prev => {
        const newSelectedCells = new Map<string, CellData>();
        
        const minX = Math.min(start.x, end.x);
        const maxX = Math.max(start.x, end.x);
        const minY = Math.min(start.y, end.y);
        const maxY = Math.max(start.y, end.y);

        for (let x = minX; x <= maxX; x++) {
          for (let y = minY; y <= maxY; y++) {
            const cellKey = `${x},${y}`;
            // 这里应该从网格数据中获取实际的单元格
            const cellData: CellData = {
              id: `cell-${x}-${y}`,
              row: 16 - y,
              col: x + 16,
              x,
              y,
              index: (16 - y) * 33 + (x + 16),
              color: '#f3f4f6',
              colorMappingValue: 0,
              level: 1,
              group: null,
              isActive: true,
              number: (16 - y) * 33 + (x + 16),
            };
            newSelectedCells.set(cellKey, cellData);
          }
        }

        logger.debug(`范围选择: (${start.x}, ${start.y}) 到 (${end.x}, ${end.y}), 总数: ${newSelectedCells.size}`, undefined, 'useGridData');

        return {
          ...prev,
          selectedCells: newSelectedCells,
          selectionStart: start,
          selectionEnd: end,
        };
      });
    } catch (error) {
      logger.error('范围选择失败:', error, 'useGridData');
    }
  }, []);

  // 清空选择
  const clearSelection = useCallback((): void => {
    setSelectionState(prev => ({
      ...prev,
      selectedCells: new Map(),
      selectionStart: null,
      selectionEnd: null,
      isSelecting: false,
    }));
    logger.debug('清空选择', undefined, 'useGridData');
  }, []);

  // 获取选中的单元格数组
  const getSelectedCells = useCallback((): CellData[] => {
    return Array.from(selectionState.selectedCells.values());
  }, [selectionState.selectedCells]);

  // 检查单元格是否被选中
  const isSelected = useCallback((cell: CellData): boolean => {
    const cellKey = `${cell.x},${cell.y}`;
    return selectionState.selectedCells.has(cellKey);
  }, [selectionState.selectedCells]);

  return {
    selectionState,
    selectCell,
    selectRange,
    clearSelection,
    getSelectedCells,
    isSelected,
  };
};

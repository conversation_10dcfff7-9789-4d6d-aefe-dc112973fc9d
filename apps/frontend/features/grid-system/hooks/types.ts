/**
 * Grid System Hooks Types
 * 
 * 网格系统hooks的类型定义
 * 🎯 核心价值：为hooks提供类型安全
 * 📦 类型范围：hook返回类型、参数类型、配置类型等
 */

import type { CellData } from '@/lib/types/grid';
import type { GridInteractionState, GridSelectionState, GridBusinessConfig } from '../types';

// Hook返回类型定义
export interface UseCellDataManagerReturn {
  getCellData: (x: number, y: number) => CellData | null;
  updateCellData: (x: number, y: number, data: Partial<CellData>) => void;
  batchUpdateCells: (updates: Array<{x: number, y: number, data: Partial<CellData>}>) => void;
  resetCellData: (x: number, y: number) => void;
  isLoading: boolean;
  error: Error | null;
}

export interface UseGridDataManagerReturn {
  gridData: CellData[][];
  refreshData: () => Promise<void>;
  saveData: () => Promise<void>;
  isDirty: boolean;
  isLoading: boolean;
  error: Error | null;
}

export interface UseGridInteractionReturn {
  interactionState: GridInteractionState;
  handleCellClick: (cell: CellData, event: React.MouseEvent) => void;
  handleCellHover: (cell: CellData | null) => void;
  handleCellFocus: (cell: CellData) => void;
  startDrag: (cell: CellData) => void;  // 保留接口兼容性，但功能已移除
  endDrag: () => void;                  // 保留接口兼容性，但功能已移除
  startEdit: (cell: CellData) => void;  // 保留接口兼容性，但功能已移除
  endEdit: (save?: boolean) => void;    // 保留接口兼容性，但功能已移除
}

export interface UseGridSelectionReturn {
  selectionState: GridSelectionState;
  selectCell: (cell: CellData, mode?: 'replace' | 'add' | 'toggle') => void;
  selectRange: (start: CellData, end: CellData) => void;
  clearSelection: () => void;
  getSelectedCells: () => CellData[];
  isSelected: (cell: CellData) => boolean;
}



export interface UseGridLayoutReturn {
  gridLayout: {
    rows: number;
    cols: number;
    cellSize: number;
    gap: number;
    totalWidth: number;
    totalHeight: number;
  };
  calculateCellPosition: (x: number, y: number) => {x: number, y: number};
  getCellAtPosition: (clientX: number, clientY: number) => CellData | null;
  updateLayout: (config: Partial<GridBusinessConfig>) => void;
}

export interface UseGridBusinessConfigReturn {
  config: GridBusinessConfig;
  updateConfig: (updates: Partial<GridBusinessConfig>) => void;
  resetConfig: () => void;
  validateConfig: (config: Partial<GridBusinessConfig>) => {isValid: boolean, errors: string[]};
  saveConfig: () => Promise<void>;
  loadConfig: () => Promise<void>;
}

export interface UseGridPerformanceReturn {
  metrics: {
    renderTime: number;
    updateTime: number;
    memoryUsage: number;
    cellCount: number;
  };
  optimizations: {
    virtualizationEnabled: boolean;
    batchingEnabled: boolean;
    memoizationEnabled: boolean;
  };
  toggleOptimization: (key: keyof UseGridPerformanceReturn['optimizations']) => void;
  measurePerformance: <T>(fn: () => T, label: string) => T;
}

// Hook配置类型
export interface GridHookConfig {
  enableDebug: boolean;
  enablePerformanceTracking: boolean;
  enableErrorBoundary: boolean;
  debounceMs: number;
  throttleMs: number;
}

// Hook事件类型
export type GridHookEvent = 
  | 'data-loaded'
  | 'data-updated'
  | 'data-saved'
  | 'interaction-started'
  | 'interaction-ended'
  | 'selection-changed'
  | 'config-updated'
  | 'error-occurred';

export interface GridHookEventPayload {
  type: GridHookEvent;
  data: any;
  timestamp: number;
  source: string;
}

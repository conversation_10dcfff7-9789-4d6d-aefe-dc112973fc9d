/**
 * useCellDataManager Hook
 * 
 * 单元格数据管理的业务逻辑hook
 * 🎯 核心价值：提供单元格级别的数据操作和管理
 * 📦 功能范围：单元格CRUD操作、批量更新、数据验证
 */

import { useCallback, useState } from 'react';
import { useBasicDataStore } from '@/stores/basicDataStore';
import { logger } from '@/lib/hooks/useLogManager';

import type { CellData } from '@/lib/types/grid';
import type { UseCellDataManagerReturn } from '../types';

export const useCellDataManager = (): UseCellDataManagerReturn => {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  // 获取store中的数据和方法
  const matrixData = useBasicDataStore((state) => state.matrixData);
  const regenerateMatrixData = useBasicDataStore((state) => state.regenerateMatrixData);

  // 获取单元格数据
  const getCellData = useCallback((x: number, y: number): CellData | null => {
    try {
      const coordKey = `${x},${y}`;
      const dataPoints = matrixData?.byCoordinate?.get(coordKey);
      const dataPoint = dataPoints?.[0];
      
      if (!dataPoint) return null;

      // 转换为CellData格式
      const cellData: CellData = {
        id: `cell-${x}-${y}`,
        row: 16 - y, // 转换坐标系
        col: x + 16,
        x,
        y,
        index: (16 - y) * 33 + (x + 16),
        color: dataPoint.color || '#f3f4f6',
        colorMappingValue: dataPoint.level || 0,
        level: dataPoint.level || 1,
        group: dataPoint.group, // 直接使用字母组，不需要转换
        isActive: true,
        number: (16 - y) * 33 + (x + 16),
      };

      return cellData;
    } catch (error) {
      logger.error(`获取单元格数据失败 (${x}, ${y}):`, error, 'useGridData');
      setError(error as Error);
      return null;
    }
  }, [matrixData]);

  // 更新单元格数据
  const updateCellData = useCallback((x: number, y: number, data: Partial<CellData>): void => {
    try {
      setIsLoading(true);
      setError(null);

      // 由于当前store没有updateMatrixData方法，我们暂时重新生成数据
      // TODO: 实现真正的单元格更新逻辑
      regenerateMatrixData();

      logger.debug(`单元格数据更新成功 (${x}, ${y})`, undefined, 'useGridData');
    } catch (error) {
      logger.error(`更新单元格数据失败 (${x}, ${y}):`, error, 'useGridData');
      setError(error as Error);
    } finally {
      setIsLoading(false);
    }
  }, [regenerateMatrixData]);

  // 批量更新单元格
  const batchUpdateCells = useCallback((updates: Array<{x: number, y: number, data: Partial<CellData>}>): void => {
    try {
      setIsLoading(true);
      setError(null);

      // 由于当前store没有updateMatrixData方法，我们暂时重新生成数据
      // TODO: 实现真正的批量更新逻辑
      regenerateMatrixData();

      logger.debug(`批量更新完成，共更新 ${updates.length} 个单元格`, undefined, 'useGridData');
    } catch (error) {
      logger.error('批量更新单元格失败:', error, 'useGridData');
      setError(error as Error);
    } finally {
      setIsLoading(false);
    }
  }, [regenerateMatrixData]);

  // 重置单元格数据
  const resetCellData = useCallback((x: number, y: number): void => {
    try {
      setIsLoading(true);
      setError(null);

      // 由于当前store没有updateMatrixData方法，我们暂时重新生成数据
      // TODO: 实现真正的重置逻辑
      regenerateMatrixData();
    } catch (error) {
      setError(error as Error);
    } finally {
      setIsLoading(false);
    }
  }, [regenerateMatrixData]);

  return {
    getCellData,
    updateCellData,
    batchUpdateCells,
    resetCellData,
    isLoading,
    error,
  };
};

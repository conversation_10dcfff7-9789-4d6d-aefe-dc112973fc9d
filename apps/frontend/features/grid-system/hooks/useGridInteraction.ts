/**
 * useGridInteraction Hook - 精简版
 *
 * 网格交互逻辑的业务hook（移除拖拽和编辑功能）
 * 🎯 核心价值：管理网格的用户交互逻辑，包括点击、悬停、焦点等
 * 📦 功能范围：交互状态管理、事件处理、交互模式控制
 */

import { useCallback, useState, useRef, useEffect } from 'react';
import { logger } from '@/lib/hooks/useLogManager';
import type { CellData } from '@/lib/types/grid';
import type { UseGridInteractionReturn, GridInteractionState } from '../types';

export const useGridInteraction = (): UseGridInteractionReturn => {
  // 交互状态（完整版）
  const [interactionState, setInteractionState] = useState<GridInteractionState>({
    hoveredCell: null,
    selectedCells: new Set<string>(),
    focusedCell: null,
  });

  // 用于防抖的引用
  const hoverTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const clickTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // 处理单元格点击
  const handleCellClick = useCallback((cell: CellData, event: React.MouseEvent): void => {
    try {
      // 清除之前的点击超时
      if (clickTimeoutRef.current) {
        clearTimeout(clickTimeoutRef.current);
      }

      // 防抖处理
      clickTimeoutRef.current = setTimeout(() => {
        setInteractionState(prev => {
          const newSelectedCells = new Set(prev.selectedCells);
          const cellKey = `${cell.x},${cell.y}`;

          // 根据修饰键决定选择行为
          if (event.ctrlKey || event.metaKey) {
            // Ctrl/Cmd + 点击：切换选择
            if (newSelectedCells.has(cellKey)) {
              newSelectedCells.delete(cellKey);
            } else {
              newSelectedCells.add(cellKey);
            }
          } else if (event.shiftKey && prev.focusedCell) {
            // Shift + 点击：范围选择
            // 这里可以实现范围选择逻辑
            newSelectedCells.clear();
            newSelectedCells.add(cellKey);
          } else {
            // 普通点击：单选
            newSelectedCells.clear();
            newSelectedCells.add(cellKey);
          }

          logger.debug(`单元格点击: (${cell.x}, ${cell.y}), 选中数量: ${newSelectedCells.size}`, undefined, 'useGridData');

          return {
            ...prev,
            selectedCells: newSelectedCells,
            focusedCell: cell,
          };
        });
      }, 100); // 100ms防抖
    } catch (error) {
      logger.error('处理单元格点击失败:', error, 'useGridData');
    }
  }, []);

  // 处理单元格悬停
  const handleCellHover = useCallback((cell: CellData | null): void => {
    try {
      // 清除之前的悬停超时
      if (hoverTimeoutRef.current) {
        clearTimeout(hoverTimeoutRef.current);
      }

      // 防抖处理
      hoverTimeoutRef.current = setTimeout(() => {
        setInteractionState(prev => ({
          ...prev,
          hoveredCell: cell,
        }));

        if (cell) {
          logger.debug(`单元格悬停: (${cell.x}, ${cell.y})`, undefined, 'useGridData');
        }
      }, 50); // 50ms防抖，悬停响应要快一些
    } catch (error) {
      logger.error('处理单元格悬停失败:', error, 'useGridData');
    }
  }, []);

  // 处理单元格焦点
  const handleCellFocus = useCallback((cell: CellData): void => {
    try {
      setInteractionState(prev => ({
        ...prev,
        focusedCell: cell,
      }));

      logger.debug(`单元格获得焦点: (${cell.x}, ${cell.y})`, undefined, 'useGridData');
    } catch (error) {
      logger.error('处理单元格焦点失败:', error, 'useGridData');
    }
  }, []);



  // 清理定时器
  const cleanup = useCallback(() => {
    if (hoverTimeoutRef.current) {
      clearTimeout(hoverTimeoutRef.current);
    }
    if (clickTimeoutRef.current) {
      clearTimeout(clickTimeoutRef.current);
    }
  }, []);

  // 组件卸载时清理
  useEffect(() => {
    return cleanup;
  }, [cleanup]);

  return {
    interactionState,
    handleCellClick,
    handleCellHover,
    handleCellFocus,
  };
};

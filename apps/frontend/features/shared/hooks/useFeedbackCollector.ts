/**
 * 使用反馈收集Hook
 * 🎯 核心价值：收集hooks使用反馈，分析使用模式，持续优化
 * 📦 功能：使用统计、错误收集、性能反馈、用户行为分析
 * ⚡ 类型安全：完整的TypeScript支持
 */

import { useState, useCallback, useRef, useEffect, useMemo } from 'react';

// 反馈类型
export type FeedbackType = 'usage' | 'error' | 'performance' | 'behavior' | 'suggestion';

// 反馈严重级别
export type FeedbackSeverity = 'info' | 'warning' | 'error' | 'critical';

// 反馈数据接口
export interface FeedbackData {
  id: string;
  type: FeedbackType;
  severity: FeedbackSeverity;
  hookName: string;
  message: string;
  context: Record<string, any>;
  timestamp: number;
  userAgent: string;
  sessionId: string;
  userId?: string;
}

// 使用统计数据
export interface UsageStats {
  hookName: string;
  callCount: number;
  averageExecutionTime: number;
  errorRate: number;
  lastUsed: number;
  popularFeatures: string[];
}

// 反馈收集配置
export interface FeedbackCollectorConfig {
  // 收集开关
  enableUsageTracking?: boolean;
  enableErrorTracking?: boolean;
  enablePerformanceTracking?: boolean;
  enableBehaviorTracking?: boolean;
  
  // 采样配置
  usageSampleRate?: number;
  errorSampleRate?: number;
  performanceSampleRate?: number;
  
  // 存储配置
  maxFeedbackHistory?: number;
  enableLocalStorage?: boolean;
  enableSessionStorage?: boolean;
  
  // 上报配置
  enableReporting?: boolean;
  reportingEndpoint?: string;
  reportingInterval?: number;
  batchSize?: number;
  
  // 回调函数
  onFeedbackCollected?: (feedback: FeedbackData) => void;
  onUsageStatsUpdated?: (stats: UsageStats) => void;
  onCriticalError?: (feedback: FeedbackData) => void;
}

// Hook返回类型
export interface UseFeedbackCollectorReturn {
  // 反馈数据
  feedbackHistory: FeedbackData[];
  usageStats: Map<string, UsageStats>;
  sessionStats: {
    totalCalls: number;
    uniqueHooks: number;
    errorCount: number;
    averageResponseTime: number;
  };
  
  // 收集方法
  collectUsageFeedback: (hookName: string, feature: string, context?: Record<string, any>) => void;
  collectErrorFeedback: (hookName: string, error: Error, context?: Record<string, any>) => void;
  collectPerformanceFeedback: (hookName: string, duration: number, context?: Record<string, any>) => void;
  collectBehaviorFeedback: (hookName: string, action: string, context?: Record<string, any>) => void;
  collectCustomFeedback: (feedback: Partial<FeedbackData>) => void;
  
  // 统计方法
  getHookUsageStats: (hookName: string) => UsageStats | undefined;
  getPopularHooks: (limit?: number) => Array<{ hookName: string; usage: number }>;
  getErrorPronHooks: (limit?: number) => Array<{ hookName: string; errorRate: number }>;
  getSlowHooks: (limit?: number) => Array<{ hookName: string; avgTime: number }>;
  
  // 分析方法
  analyzeTrends: (timeRange: number) => {
    usageTrend: 'increasing' | 'decreasing' | 'stable';
    errorTrend: 'increasing' | 'decreasing' | 'stable';
    performanceTrend: 'improving' | 'degrading' | 'stable';
  };
  
  generateReport: () => {
    summary: string;
    recommendations: string[];
    metrics: Record<string, number>;
  };
  
  // 控制方法
  clearFeedback: () => void;
  exportFeedback: () => string;
  enableCollection: () => void;
  disableCollection: () => void;
}

// 生成唯一ID
const generateFeedbackId = (): string => {
  return `feedback_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
};

// 生成会话ID
const generateSessionId = (): string => {
  return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
};

// 获取用户代理信息
const getUserAgent = (): string => {
  return navigator.userAgent;
};

export function useFeedbackCollector(config: FeedbackCollectorConfig = {}): UseFeedbackCollectorReturn {
  const {
    enableUsageTracking = true,
    enableErrorTracking = true,
    enablePerformanceTracking = true,
    enableBehaviorTracking = true,
    usageSampleRate = 0.1, // 10%采样
    errorSampleRate = 1.0, // 100%采样
    performanceSampleRate = 0.2, // 20%采样
    maxFeedbackHistory = 1000,
    enableLocalStorage = true,
    enableSessionStorage = false,
    enableReporting = false,
    reportingEndpoint,
    reportingInterval = 300000, // 5分钟
    batchSize = 50,
    onFeedbackCollected,
    onUsageStatsUpdated,
    onCriticalError,
  } = config;

  // 状态管理
  const [feedbackHistory, setFeedbackHistory] = useState<FeedbackData[]>([]);
  const [usageStats, setUsageStats] = useState<Map<string, UsageStats>>(new Map());
  const [isCollectionEnabled, setIsCollectionEnabled] = useState(true);
  
  // 引用管理
  const sessionId = useRef(generateSessionId());
  const reportingQueue = useRef<FeedbackData[]>([]);
  const sessionStartTime = useRef(Date.now());

  // 检查是否应该采样
  const shouldSample = useCallback((type: FeedbackType) => {
    const rates = {
      usage: usageSampleRate,
      error: errorSampleRate,
      performance: performanceSampleRate,
      behavior: usageSampleRate,
      suggestion: 1.0,
    };
    return Math.random() < rates[type];
  }, [usageSampleRate, errorSampleRate, performanceSampleRate]);

  // 创建反馈数据
  const createFeedback = useCallback((
    type: FeedbackType,
    severity: FeedbackSeverity,
    hookName: string,
    message: string,
    context: Record<string, any> = {}
  ): FeedbackData => {
    return {
      id: generateFeedbackId(),
      type,
      severity,
      hookName,
      message,
      context,
      timestamp: Date.now(),
      userAgent: getUserAgent(),
      sessionId: sessionId.current,
    };
  }, []);

  // 收集反馈的通用方法
  const collectFeedback = useCallback((feedback: FeedbackData) => {
    if (!isCollectionEnabled || !shouldSample(feedback.type)) return;
    
    // 添加到历史记录
    setFeedbackHistory(prev => {
      const newHistory = [feedback, ...prev];
      return newHistory.slice(0, maxFeedbackHistory);
    });
    
    // 更新使用统计
    setUsageStats(prev => {
      const current = prev.get(feedback.hookName) || {
        hookName: feedback.hookName,
        callCount: 0,
        averageExecutionTime: 0,
        errorRate: 0,
        lastUsed: 0,
        popularFeatures: [],
      };
      
      const updated: UsageStats = {
        ...current,
        callCount: current.callCount + 1,
        lastUsed: feedback.timestamp,
      };
      
      // 更新错误率
      if (feedback.type === 'error') {
        const totalErrors = feedbackHistory.filter(f => 
          f.hookName === feedback.hookName && f.type === 'error'
        ).length + 1;
        updated.errorRate = totalErrors / updated.callCount;
      }
      
      // 更新执行时间
      if (feedback.type === 'performance' && feedback.context.duration) {
        const totalTime = current.averageExecutionTime * (current.callCount - 1) + feedback.context.duration;
        updated.averageExecutionTime = totalTime / current.callCount;
      }
      
      // 更新热门功能
      if (feedback.context.feature) {
        const features = [...current.popularFeatures];
        const featureIndex = features.indexOf(feedback.context.feature);
        if (featureIndex === -1) {
          features.push(feedback.context.feature);
        }
        updated.popularFeatures = features.slice(0, 10); // 保留前10个
      }
      
      const newStats = new Map(prev);
      newStats.set(feedback.hookName, updated);
      
      onUsageStatsUpdated?.(updated);
      return newStats;
    });
    
    // 存储到本地
    if (enableLocalStorage) {
      try {
        const stored = localStorage.getItem('hookFeedback') || '[]';
        const feedbacks = JSON.parse(stored);
        feedbacks.unshift(feedback);
        localStorage.setItem('hookFeedback', JSON.stringify(feedbacks.slice(0, 100)));
      } catch (error) {
        process.env.NODE_ENV === 'development' && console.warn('Failed to store feedback to localStorage:', error);
      }
    }
    
    // 添加到上报队列
    if (enableReporting) {
      reportingQueue.current.push(feedback);
    }
    
    // 调用回调
    onFeedbackCollected?.(feedback);
    
    // 处理关键错误
    if (feedback.severity === 'critical') {
      onCriticalError?.(feedback);
    }
  }, [isCollectionEnabled, shouldSample, maxFeedbackHistory, feedbackHistory, enableLocalStorage, enableReporting, onFeedbackCollected, onUsageStatsUpdated, onCriticalError]);

  // 收集使用反馈
  const collectUsageFeedback = useCallback((
    hookName: string,
    feature: string,
    context: Record<string, any> = {}
  ) => {
    if (!enableUsageTracking) return;
    
    const feedback = createFeedback(
      'usage',
      'info',
      hookName,
      `Hook ${hookName} used feature: ${feature}`,
      { feature, ...context }
    );
    
    collectFeedback(feedback);
  }, [enableUsageTracking, createFeedback, collectFeedback]);

  // 收集错误反馈
  const collectErrorFeedback = useCallback((
    hookName: string,
    error: Error,
    context: Record<string, any> = {}
  ) => {
    if (!enableErrorTracking) return;
    
    const severity: FeedbackSeverity = error.name === 'TypeError' ? 'critical' : 'error';
    
    const feedback = createFeedback(
      'error',
      severity,
      hookName,
      `Error in ${hookName}: ${error.message}`,
      { 
        errorName: error.name,
        errorStack: error.stack,
        ...context 
      }
    );
    
    collectFeedback(feedback);
  }, [enableErrorTracking, createFeedback, collectFeedback]);

  // 收集性能反馈
  const collectPerformanceFeedback = useCallback((
    hookName: string,
    duration: number,
    context: Record<string, any> = {}
  ) => {
    if (!enablePerformanceTracking) return;
    
    const severity: FeedbackSeverity = duration > 1000 ? 'warning' : 'info';
    
    const feedback = createFeedback(
      'performance',
      severity,
      hookName,
      `Hook ${hookName} execution time: ${duration}ms`,
      { duration, ...context }
    );
    
    collectFeedback(feedback);
  }, [enablePerformanceTracking, createFeedback, collectFeedback]);

  // 收集行为反馈
  const collectBehaviorFeedback = useCallback((
    hookName: string,
    action: string,
    context: Record<string, any> = {}
  ) => {
    if (!enableBehaviorTracking) return;
    
    const feedback = createFeedback(
      'behavior',
      'info',
      hookName,
      `User action in ${hookName}: ${action}`,
      { action, ...context }
    );
    
    collectFeedback(feedback);
  }, [enableBehaviorTracking, createFeedback, collectFeedback]);

  // 收集自定义反馈
  const collectCustomFeedback = useCallback((partialFeedback: Partial<FeedbackData>) => {
    const feedback: FeedbackData = {
      id: generateFeedbackId(),
      type: 'suggestion',
      severity: 'info',
      hookName: 'unknown',
      message: '',
      context: {},
      timestamp: Date.now(),
      userAgent: getUserAgent(),
      sessionId: sessionId.current,
      ...partialFeedback,
    };
    
    collectFeedback(feedback);
  }, [collectFeedback]);

  // 获取Hook使用统计
  const getHookUsageStats = useCallback((hookName: string) => {
    return usageStats.get(hookName);
  }, [usageStats]);

  // 获取热门Hooks
  const getPopularHooks = useCallback((limit = 10) => {
    return Array.from(usageStats.entries())
      .map(([hookName, stats]) => ({ hookName, usage: stats.callCount }))
      .sort((a, b) => b.usage - a.usage)
      .slice(0, limit);
  }, [usageStats]);

  // 获取容易出错的Hooks
  const getErrorPronHooks = useCallback((limit = 10) => {
    return Array.from(usageStats.entries())
      .map(([hookName, stats]) => ({ hookName, errorRate: stats.errorRate }))
      .filter(item => item.errorRate > 0)
      .sort((a, b) => b.errorRate - a.errorRate)
      .slice(0, limit);
  }, [usageStats]);

  // 获取慢Hooks
  const getSlowHooks = useCallback((limit = 10) => {
    return Array.from(usageStats.entries())
      .map(([hookName, stats]) => ({ hookName, avgTime: stats.averageExecutionTime }))
      .filter(item => item.avgTime > 0)
      .sort((a, b) => b.avgTime - a.avgTime)
      .slice(0, limit);
  }, [usageStats]);

  // 计算会话统计
  const sessionStats = useMemo(() => {
    const sessionFeedbacks = feedbackHistory.filter(f => f.sessionId === sessionId.current);
    const uniqueHooks = new Set(sessionFeedbacks.map(f => f.hookName)).size;
    const errorCount = sessionFeedbacks.filter(f => f.type === 'error').length;
    const performanceFeedbacks = sessionFeedbacks.filter(f => f.type === 'performance');
    const averageResponseTime = performanceFeedbacks.length > 0
      ? performanceFeedbacks.reduce((sum, f) => sum + (f.context.duration || 0), 0) / performanceFeedbacks.length
      : 0;
    
    return {
      totalCalls: sessionFeedbacks.length,
      uniqueHooks,
      errorCount,
      averageResponseTime,
    };
  }, [feedbackHistory]);

  // 分析趋势
  const analyzeTrends = useCallback((timeRange: number) => {
    const cutoff = Date.now() - timeRange;
    const recentFeedbacks = feedbackHistory.filter(f => f.timestamp > cutoff);
    const olderFeedbacks = feedbackHistory.filter(f => f.timestamp <= cutoff);
    
    const recentUsage = recentFeedbacks.filter(f => f.type === 'usage').length;
    const olderUsage = olderFeedbacks.filter(f => f.type === 'usage').length;
    
    const recentErrors = recentFeedbacks.filter(f => f.type === 'error').length;
    const olderErrors = olderFeedbacks.filter(f => f.type === 'error').length;
    
    const recentPerf = recentFeedbacks.filter(f => f.type === 'performance');
    const olderPerf = olderFeedbacks.filter(f => f.type === 'performance');
    
    const recentAvgTime = recentPerf.length > 0
      ? recentPerf.reduce((sum, f) => sum + (f.context.duration || 0), 0) / recentPerf.length
      : 0;
    const olderAvgTime = olderPerf.length > 0
      ? olderPerf.reduce((sum, f) => sum + (f.context.duration || 0), 0) / olderPerf.length
      : 0;
    
    return {
      usageTrend: recentUsage > olderUsage ? 'increasing' : recentUsage < olderUsage ? 'decreasing' : 'stable',
      errorTrend: recentErrors > olderErrors ? 'increasing' : recentErrors < olderErrors ? 'decreasing' : 'stable',
      performanceTrend: recentAvgTime < olderAvgTime ? 'improving' : recentAvgTime > olderAvgTime ? 'degrading' : 'stable',
    } as const;
  }, [feedbackHistory]);

  // 生成报告
  const generateReport = useCallback(() => {
    const totalFeedbacks = feedbackHistory.length;
    const errorRate = feedbackHistory.filter(f => f.type === 'error').length / totalFeedbacks;
    const popularHooks = getPopularHooks(5);
    const slowHooks = getSlowHooks(3);
    const trends = analyzeTrends(24 * 60 * 60 * 1000); // 24小时
    
    const summary = `
      总反馈数: ${totalFeedbacks}
      错误率: ${(errorRate * 100).toFixed(2)}%
      活跃Hooks: ${usageStats.size}
      会话时长: ${Math.round((Date.now() - sessionStartTime.current) / 1000 / 60)}分钟
    `;
    
    const recommendations: string[] = [];
    
    if (errorRate > 0.1) {
      recommendations.push('错误率较高，建议检查错误处理机制');
    }
    
    if (slowHooks.length > 0) {
      recommendations.push(`发现慢Hooks: ${slowHooks.map(h => h.hookName).join(', ')}，建议优化性能`);
    }
    
    if (trends.usageTrend === 'decreasing') {
      recommendations.push('使用量下降，建议检查用户体验');
    }
    
    return {
      summary: summary.trim(),
      recommendations,
      metrics: {
        totalFeedbacks,
        errorRate,
        activeHooks: usageStats.size,
        sessionDuration: Date.now() - sessionStartTime.current,
      },
    };
  }, [feedbackHistory, usageStats.size, getPopularHooks, getSlowHooks, analyzeTrends]);

  // 清除反馈
  const clearFeedback = useCallback(() => {
    setFeedbackHistory([]);
    setUsageStats(new Map());
    reportingQueue.current = [];
    
    if (enableLocalStorage) {
      localStorage.removeItem('hookFeedback');
    }
  }, [enableLocalStorage]);

  // 导出反馈
  const exportFeedback = useCallback(() => {
    return JSON.stringify({
      feedbacks: feedbackHistory,
      usageStats: Array.from(usageStats.entries()),
      sessionStats,
      report: generateReport(),
      exportTime: Date.now(),
    }, null, 2);
  }, [feedbackHistory, usageStats, sessionStats, generateReport]);

  // 启用/禁用收集
  const enableCollection = useCallback(() => setIsCollectionEnabled(true), []);
  const disableCollection = useCallback(() => setIsCollectionEnabled(false), []);

  // 反馈上报
  useEffect(() => {
    if (!enableReporting || !reportingEndpoint) return;
    
    const reportFeedbacks = async () => {
      if (reportingQueue.current.length === 0) return;
      
      const batch = reportingQueue.current.splice(0, batchSize);
      
      try {
        await fetch(reportingEndpoint, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            feedbacks: batch,
            sessionId: sessionId.current,
            timestamp: Date.now(),
          }),
        });
      } catch (error) {
        process.env.NODE_ENV === 'development' && console.warn('反馈上报失败:', error);
        // 重新加入队列
        reportingQueue.current.unshift(...batch);
      }
    };
    
    const interval = setInterval(reportFeedbacks, reportingInterval);
    return () => clearInterval(interval);
  }, [enableReporting, reportingEndpoint, reportingInterval, batchSize]);

  return {
    // 反馈数据
    feedbackHistory,
    usageStats,
    sessionStats,
    
    // 收集方法
    collectUsageFeedback,
    collectErrorFeedback,
    collectPerformanceFeedback,
    collectBehaviorFeedback,
    collectCustomFeedback,
    
    // 统计方法
    getHookUsageStats,
    getPopularHooks,
    getErrorPronHooks,
    getSlowHooks,
    
    // 分析方法
    analyzeTrends,
    generateReport,
    
    // 控制方法
    clearFeedback,
    exportFeedback,
    enableCollection,
    disableCollection,
  };
}

/**
 * 统一错误处理Hook
 * 🎯 核心价值：统一错误处理模式，减少重复的错误处理代码
 * 📦 功能：错误捕获、错误分类、错误恢复、错误上报
 * ⚡ 类型安全：完整的TypeScript支持
 */

import { useState, useCallback, useRef, useEffect } from 'react';

// 错误类型
export type ErrorType = 'network' | 'validation' | 'business' | 'system' | 'unknown';

// 错误严重级别
export type ErrorSeverity = 'low' | 'medium' | 'high' | 'critical';

// 错误信息接口
export interface ErrorInfo {
  id: string;
  type: ErrorType;
  severity: ErrorSeverity;
  message: string;
  details?: any;
  timestamp: number;
  context?: Record<string, any>;
  stack?: string;
}

// 错误处理配置
export interface ErrorHandlerConfig {
  // 错误分类规则
  classifyError?: (error: unknown) => { type: ErrorType; severity: ErrorSeverity };
  
  // 错误恢复策略
  enableAutoRecovery?: boolean;
  maxRetryAttempts?: number;
  retryDelay?: number;
  
  // 错误上报
  enableReporting?: boolean;
  reportingEndpoint?: string;
  
  // 错误存储
  maxErrorHistory?: number;
  enablePersistence?: boolean;
  
  // 回调函数
  onError?: (error: ErrorInfo) => void;
  onRecovery?: (error: ErrorInfo) => void;
}

// Hook返回类型
export interface UseErrorHandlerReturn {
  // 错误状态
  currentError: ErrorInfo | null;
  errorHistory: ErrorInfo[];
  hasError: boolean;
  isRecovering: boolean;
  
  // 错误处理方法
  handleError: (error: any, context?: Record<string, any>) => ErrorInfo;
  clearError: () => void;
  clearAllErrors: () => void;
  retryLastOperation: () => Promise<void>;
  
  // 错误查询
  getErrorsByType: (type: ErrorType) => ErrorInfo[];
  getErrorsBySeverity: (severity: ErrorSeverity) => ErrorInfo[];
  getRecentErrors: (minutes: number) => ErrorInfo[];
  
  // 错误统计
  getErrorStats: () => {
    total: number;
    byType: Record<ErrorType, number>;
    bySeverity: Record<ErrorSeverity, number>;
  };
  
  // 工具方法
  wrapAsync: <T>(fn: () => Promise<T>, context?: Record<string, any>) => Promise<T>;
  wrapSync: <T>(fn: () => T, context?: Record<string, any>) => T | null;
}

// 默认错误分类器
const defaultClassifyError = (error: unknown): { type: ErrorType; severity: ErrorSeverity } => {
  // 类型保护：检查是否是对象且有相关属性
  const errorObj = error as any;

  if (errorObj?.name === 'NetworkError' || errorObj?.code === 'NETWORK_ERROR') {
    return { type: 'network', severity: 'medium' };
  }

  if (errorObj?.name === 'ValidationError' || errorObj?.message?.includes('validation')) {
    return { type: 'validation', severity: 'low' };
  }

  if (errorObj?.name === 'BusinessError' || errorObj?.code?.startsWith('BIZ_')) {
    return { type: 'business', severity: 'medium' };
  }
  
  if (errorObj?.name === 'SystemError' || errorObj?.code?.startsWith('SYS_')) {
    return { type: 'system', severity: 'high' };
  }
  
  return { type: 'unknown', severity: 'medium' };
};

// 生成错误ID
const generateErrorId = (): string => {
  return `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
};

export function useErrorHandler(config: ErrorHandlerConfig = {}): UseErrorHandlerReturn {
  const {
    classifyError = defaultClassifyError,
    enableAutoRecovery = false,
    maxRetryAttempts = 3,
    retryDelay = 1000,
    enableReporting = false,
    reportingEndpoint,
    maxErrorHistory = 50,
    enablePersistence = false,
    onError,
    onRecovery,
  } = config;

  // 状态管理
  const [currentError, setCurrentError] = useState<ErrorInfo | null>(null);
  const [errorHistory, setErrorHistory] = useState<ErrorInfo[]>([]);
  const [isRecovering, setIsRecovering] = useState(false);
  
  // 引用管理
  const retryCountRef = useRef<number>(0);
  const lastOperationRef = useRef<(() => Promise<void>) | null>(null);

  // 计算派生状态
  const hasError = currentError !== null;

  // 错误处理核心方法
  const handleError = useCallback((error: any, context?: Record<string, any>): ErrorInfo => {
    const { type, severity } = classifyError(error);
    
    const errorInfo: ErrorInfo = {
      id: generateErrorId(),
      type,
      severity,
      message: error?.message || String(error),
      details: error,
      timestamp: Date.now(),
      context,
      stack: error?.stack,
    };

    // 更新当前错误
    setCurrentError(errorInfo);
    
    // 添加到历史记录
    setErrorHistory(prev => {
      const newHistory = [errorInfo, ...prev];
      return newHistory.slice(0, maxErrorHistory);
    });

    // 调用错误回调
    onError?.(errorInfo);

    // 错误上报
    if (enableReporting && reportingEndpoint) {
      reportError(errorInfo);
    }

    // 持久化存储
    if (enablePersistence) {
      persistError(errorInfo);
    }

    // 自动恢复
    if (enableAutoRecovery && retryCountRef.current < maxRetryAttempts) {
      setTimeout(() => {
        retryLastOperation();
      }, retryDelay);
    }

    return errorInfo;
  }, [classifyError, maxErrorHistory, onError, enableReporting, reportingEndpoint, enablePersistence, enableAutoRecovery, maxRetryAttempts, retryDelay]);

  // 清除当前错误
  const clearError = useCallback(() => {
    setCurrentError(null);
    retryCountRef.current = 0;
  }, []);

  // 清除所有错误
  const clearAllErrors = useCallback(() => {
    setCurrentError(null);
    setErrorHistory([]);
    retryCountRef.current = 0;
  }, []);

  // 重试最后一次操作
  const retryLastOperation = useCallback(async () => {
    if (!lastOperationRef.current || retryCountRef.current >= maxRetryAttempts) {
      return;
    }

    setIsRecovering(true);
    retryCountRef.current++;

    try {
      await lastOperationRef.current();
      setCurrentError(null);
      retryCountRef.current = 0;
      onRecovery?.(currentError!);
    } catch (error) {
      handleError(error, { isRetry: true, retryCount: retryCountRef.current });
    } finally {
      setIsRecovering(false);
    }
  }, [maxRetryAttempts, onRecovery, currentError, handleError]);

  // 按类型查询错误
  const getErrorsByType = useCallback((type: ErrorType) => {
    return errorHistory.filter(error => error.type === type);
  }, [errorHistory]);

  // 按严重级别查询错误
  const getErrorsBySeverity = useCallback((severity: ErrorSeverity) => {
    return errorHistory.filter(error => error.severity === severity);
  }, [errorHistory]);

  // 获取最近的错误
  const getRecentErrors = useCallback((minutes: number) => {
    const cutoff = Date.now() - (minutes * 60 * 1000);
    return errorHistory.filter(error => error.timestamp > cutoff);
  }, [errorHistory]);

  // 获取错误统计
  const getErrorStats = useCallback(() => {
    const stats = {
      total: errorHistory.length,
      byType: {} as Record<ErrorType, number>,
      bySeverity: {} as Record<ErrorSeverity, number>,
    };

    errorHistory.forEach(error => {
      stats.byType[error.type] = (stats.byType[error.type] || 0) + 1;
      stats.bySeverity[error.severity] = (stats.bySeverity[error.severity] || 0) + 1;
    });

    return stats;
  }, [errorHistory]);

  // 包装异步函数
  const wrapAsync = useCallback(<T>(
    fn: () => Promise<T>,
    context?: Record<string, any>
  ): Promise<T> => {
    lastOperationRef.current = fn as any;
    
    return fn().catch(error => {
      handleError(error, context);
      throw error;
    });
  }, [handleError]);

  // 包装同步函数
  const wrapSync = useCallback(<T>(
    fn: () => T,
    context?: Record<string, any>
  ): T | null => {
    try {
      return fn();
    } catch (error) {
      handleError(error, context);
      return null;
    }
  }, [handleError]);

  // 错误上报
  const reportError = useCallback(async (errorInfo: ErrorInfo) => {
    if (!reportingEndpoint) return;
    
    try {
      await fetch(reportingEndpoint, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(errorInfo),
      });
    } catch (error) {
      process.env.NODE_ENV === 'development' && console.warn('错误上报失败:', error);
    }
  }, [reportingEndpoint]);

  // 错误持久化
  const persistError = useCallback((errorInfo: ErrorInfo) => {
    try {
      const key = 'error_history';
      const stored = localStorage.getItem(key);
      const history = stored ? JSON.parse(stored) : [];
      history.unshift(errorInfo);
      localStorage.setItem(key, JSON.stringify(history.slice(0, maxErrorHistory)));
    } catch (error) {
      process.env.NODE_ENV === 'development' && console.warn('错误持久化失败:', error);
    }
  }, [maxErrorHistory]);

  return {
    // 错误状态
    currentError,
    errorHistory,
    hasError,
    isRecovering,
    
    // 错误处理方法
    handleError,
    clearError,
    clearAllErrors,
    retryLastOperation,
    
    // 错误查询
    getErrorsByType,
    getErrorsBySeverity,
    getRecentErrors,
    
    // 错误统计
    getErrorStats,
    
    // 工具方法
    wrapAsync,
    wrapSync,
  };
}

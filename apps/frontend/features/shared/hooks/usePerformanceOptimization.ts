/**
 * 通用性能优化Hook
 * 🎯 核心价值：统一性能优化模式，缓存、防抖、节流等
 * 📦 功能：计算缓存、批量操作、防抖节流、内存管理
 * ⚡ 类型安全：完整的TypeScript支持
 */

import { useCallback, useRef, useMemo, useEffect } from 'react';

// 缓存配置
export interface CacheConfig {
  maxSize?: number;
  ttl?: number; // 生存时间（毫秒）
  enableLRU?: boolean; // 启用LRU淘汰策略
}

// 批量操作配置
export interface BatchConfig {
  batchSize?: number;
  delay?: number; // 批量延迟（毫秒）
  maxWait?: number; // 最大等待时间（毫秒）
}

// 防抖配置
export interface DebounceConfig {
  delay: number;
  maxWait?: number;
  leading?: boolean;
  trailing?: boolean;
}

// 节流配置
export interface ThrottleConfig {
  delay: number;
  leading?: boolean;
  trailing?: boolean;
}

// 缓存项
interface CacheItem<T> {
  value: T;
  timestamp: number;
  accessCount: number;
  lastAccess: number;
}

// LRU缓存实现
class LRUCache<K, V> {
  private cache = new Map<K, CacheItem<V>>();
  private maxSize: number;
  private ttl: number;

  constructor(maxSize = 100, ttl = 5 * 60 * 1000) {
    this.maxSize = maxSize;
    this.ttl = ttl;
  }

  get(key: K): V | undefined {
    const item = this.cache.get(key);
    if (!item) return undefined;

    const now = Date.now();
    if (now - item.timestamp > this.ttl) {
      this.cache.delete(key);
      return undefined;
    }

    // 更新访问信息
    item.accessCount++;
    item.lastAccess = now;
    
    // 移到末尾（最近使用）
    this.cache.delete(key);
    this.cache.set(key, item);

    return item.value;
  }

  set(key: K, value: V): void {
    const now = Date.now();
    
    // 如果已存在，更新值
    if (this.cache.has(key)) {
      const item = this.cache.get(key)!;
      item.value = value;
      item.timestamp = now;
      item.lastAccess = now;
      item.accessCount++;
      
      // 移到末尾
      this.cache.delete(key);
      this.cache.set(key, item);
      return;
    }

    // 检查容量
    if (this.cache.size >= this.maxSize) {
      // 删除最久未使用的项
      const firstKey = this.cache.keys().next().value;
      if (firstKey !== undefined) {
        this.cache.delete(firstKey);
      }
    }

    // 添加新项
    this.cache.set(key, {
      value,
      timestamp: now,
      accessCount: 1,
      lastAccess: now,
    });
  }

  clear(): void {
    this.cache.clear();
  }

  size(): number {
    return this.cache.size;
  }

  getStats() {
    const items = Array.from(this.cache.values());
    return {
      size: this.cache.size,
      totalAccess: items.reduce((sum, item) => sum + item.accessCount, 0),
      avgAccess: items.length > 0 ? 
        items.reduce((sum, item) => sum + item.accessCount, 0) / items.length : 0,
    };
  }
}

// 计算缓存Hook
export function useComputationCache<TArgs extends any[], TResult>(
  computeFn: (...args: TArgs) => TResult,
  config: CacheConfig = {}
) {
  const { maxSize = 100, ttl = 5 * 60 * 1000, enableLRU = true } = config;
  
  const cacheRef = useRef<LRUCache<string, TResult>>(
    new LRUCache(maxSize, ttl)
  );

  const cachedCompute = useCallback((...args: TArgs): TResult => {
    const key = JSON.stringify(args);
    
    // 尝试从缓存获取
    const cached = cacheRef.current.get(key);
    if (cached !== undefined) {
      return cached;
    }

    // 计算新值
    const result = computeFn(...args);
    
    // 存入缓存
    cacheRef.current.set(key, result);
    
    return result;
  }, [computeFn]);

  const clearCache = useCallback(() => {
    cacheRef.current.clear();
  }, []);

  const getCacheStats = useCallback(() => {
    return cacheRef.current.getStats();
  }, []);

  return {
    compute: cachedCompute,
    clearCache,
    getCacheStats,
  };
}

// 批量操作Hook
export function useBatchOperation<T, R>(
  operation: (items: T[]) => Promise<R[]>,
  config: BatchConfig = {}
) {
  const { batchSize = 10, delay = 100, maxWait = 1000 } = config;
  
  const queueRef = useRef<T[]>([]);
  const resolversRef = useRef<Array<(result: R) => void>>([]);
  const rejectersRef = useRef<Array<(error: Error) => void>>([]);
  const timerRef = useRef<NodeJS.Timeout | null>(null);
  const startTimeRef = useRef<number | null>(null);

  const processBatch = useCallback(async () => {
    if (queueRef.current.length === 0) return;

    const items = queueRef.current.splice(0);
    const resolvers = resolversRef.current.splice(0);
    const rejecters = rejectersRef.current.splice(0);

    try {
      const results = await operation(items);
      
      results.forEach((result, index) => {
        if (resolvers[index]) {
          resolvers[index](result);
        }
      });
    } catch (error) {
      rejecters.forEach(reject => {
        reject(error as Error);
      });
    }

    startTimeRef.current = null;
  }, [operation]);

  const scheduleProcess = useCallback(() => {
    if (timerRef.current) {
      clearTimeout(timerRef.current);
    }

    const now = Date.now();
    const shouldProcessImmediately = 
      queueRef.current.length >= batchSize ||
      (startTimeRef.current && now - startTimeRef.current >= maxWait);

    if (shouldProcessImmediately) {
      processBatch();
    } else {
      timerRef.current = setTimeout(processBatch, delay);
    }
  }, [processBatch, batchSize, delay, maxWait]);

  const addToBatch = useCallback((item: T): Promise<R> => {
    return new Promise<R>((resolve, reject) => {
      queueRef.current.push(item);
      resolversRef.current.push(resolve);
      rejectersRef.current.push(reject);

      if (startTimeRef.current === null) {
        startTimeRef.current = Date.now();
      }

      scheduleProcess();
    });
  }, [scheduleProcess]);

  // 清理定时器
  useEffect(() => {
    return () => {
      if (timerRef.current) {
        clearTimeout(timerRef.current);
      }
    };
  }, []);

  return {
    addToBatch,
    processBatch,
    getQueueSize: () => queueRef.current.length,
  };
}

// 防抖Hook
export function useDebounce<TArgs extends any[]>(
  fn: (...args: TArgs) => void,
  config: DebounceConfig
) {
  const { delay, maxWait, leading = false, trailing = true } = config;
  
  const timerRef = useRef<NodeJS.Timeout | null>(null);
  const maxTimerRef = useRef<NodeJS.Timeout | null>(null);
  const lastCallTimeRef = useRef<number | null>(null);
  const lastInvokeTimeRef = useRef<number>(0);

  const debouncedFn = useCallback((...args: TArgs) => {
    const now = Date.now();
    const isInvoking = lastCallTimeRef.current === null;

    lastCallTimeRef.current = now;

    const invokeNow = () => {
      lastInvokeTimeRef.current = now;
      fn(...args);
    };

    const shouldInvokeLeading = leading && isInvoking;
    const shouldInvokeTrailing = trailing;

    // 清除现有定时器
    if (timerRef.current) {
      clearTimeout(timerRef.current);
    }

    // 立即执行（leading）
    if (shouldInvokeLeading) {
      invokeNow();
    }

    // 设置延迟执行
    if (shouldInvokeTrailing) {
      timerRef.current = setTimeout(() => {
        if (lastCallTimeRef.current && now === lastCallTimeRef.current) {
          invokeNow();
        }
      }, delay);
    }

    // 最大等待时间处理
    if (maxWait && !maxTimerRef.current) {
      maxTimerRef.current = setTimeout(() => {
        if (lastCallTimeRef.current) {
          invokeNow();
        }
        maxTimerRef.current = null;
      }, maxWait);
    }
  }, [fn, delay, maxWait, leading, trailing]);

  const cancel = useCallback(() => {
    if (timerRef.current) {
      clearTimeout(timerRef.current);
      timerRef.current = null;
    }
    if (maxTimerRef.current) {
      clearTimeout(maxTimerRef.current);
      maxTimerRef.current = null;
    }
    lastCallTimeRef.current = null;
  }, []);

  const flush = useCallback((...args: TArgs) => {
    if (timerRef.current && lastCallTimeRef.current) {
      cancel();
      fn(...args);
    }
  }, [fn, cancel]);

  // 清理
  useEffect(() => {
    return cancel;
  }, [cancel]);

  return {
    debouncedFn,
    cancel,
    flush,
  };
}

// 节流Hook
export function useThrottle<TArgs extends any[]>(
  fn: (...args: TArgs) => void,
  config: ThrottleConfig
) {
  const { delay, leading = true, trailing = true } = config;
  
  const lastInvokeTimeRef = useRef<number>(0);
  const timerRef = useRef<NodeJS.Timeout | null>(null);
  const lastArgsRef = useRef<TArgs | null>(null);

  const throttledFn = useCallback((...args: TArgs) => {
    const now = Date.now();
    const timeSinceLastInvoke = now - lastInvokeTimeRef.current;

    lastArgsRef.current = args;

    const invokeNow = () => {
      lastInvokeTimeRef.current = now;
      fn(...args);
    };

    const shouldInvokeLeading = leading && timeSinceLastInvoke >= delay;
    
    if (shouldInvokeLeading) {
      invokeNow();
      return;
    }

    // 设置trailing调用
    if (trailing && !timerRef.current) {
      timerRef.current = setTimeout(() => {
        if (lastArgsRef.current) {
          lastInvokeTimeRef.current = Date.now();
          fn(...lastArgsRef.current);
        }
        timerRef.current = null;
      }, delay - timeSinceLastInvoke);
    }
  }, [fn, delay, leading, trailing]);

  const cancel = useCallback(() => {
    if (timerRef.current) {
      clearTimeout(timerRef.current);
      timerRef.current = null;
    }
    lastArgsRef.current = null;
  }, []);

  // 清理
  useEffect(() => {
    return cancel;
  }, [cancel]);

  return {
    throttledFn,
    cancel,
  };
}

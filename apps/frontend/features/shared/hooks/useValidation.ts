/**
 * 通用验证Hook
 * 🎯 核心价值：统一验证逻辑、错误处理、表单验证模式
 * 📦 功能：字段验证、批量验证、异步验证、错误管理
 * ⚡ 类型安全：完整的TypeScript支持
 */

import { useState, useCallback, useMemo } from 'react';

// 验证规则类型
export interface ValidationRule<T = any> {
  required?: boolean;
  minLength?: number;
  maxLength?: number;
  min?: number;
  max?: number;
  pattern?: RegExp;
  custom?: (value: T) => string | null;
  asyncValidator?: (value: T) => Promise<string | null>;
}

// 字段验证配置
export interface FieldValidation<T = any> {
  rules: ValidationRule<T>;
  message?: string;
  validateOnChange?: boolean;
  validateOnBlur?: boolean;
}

// 验证配置
export interface ValidationConfig<T extends Record<string, any>> {
  fields: {
    [K in keyof T]?: FieldValidation<T[K]>;
  };
  validateOnSubmit?: boolean;
  stopOnFirstError?: boolean;
}

// 验证状态
export interface ValidationState<T extends Record<string, any>> {
  errors: Partial<Record<keyof T, string>>;
  isValidating: boolean;
  isValid: boolean;
  hasErrors: boolean;
  touchedFields: Set<keyof T>;
}

// 验证操作
export interface ValidationActions<T extends Record<string, any>> {
  validate: (field?: keyof T) => Promise<boolean>;
  validateField: (field: keyof T, value: T[keyof T]) => Promise<string | null>;
  validateAll: () => Promise<boolean>;
  clearErrors: (field?: keyof T) => void;
  setError: (field: keyof T, error: string) => void;
  markTouched: (field: keyof T) => void;
  reset: () => void;
}

export type UseValidationReturn<T extends Record<string, any>> = 
  ValidationState<T> & ValidationActions<T>;

export function useValidation<T extends Record<string, any>>(
  config: ValidationConfig<T>,
  initialData?: Partial<T>
): UseValidationReturn<T> {
  const { fields, validateOnSubmit = true, stopOnFirstError = false } = config;

  // 状态管理
  const [errors, setErrors] = useState<Partial<Record<keyof T, string>>>({});
  const [isValidating, setIsValidating] = useState(false);
  const [touchedFields, setTouchedFields] = useState<Set<keyof T>>(new Set());

  // 计算派生状态
  const hasErrors = useMemo(() => {
    return Object.keys(errors).length > 0;
  }, [errors]);

  const isValid = useMemo(() => {
    return !hasErrors && touchedFields.size > 0;
  }, [hasErrors, touchedFields.size]);

  // 验证单个字段
  const validateField = useCallback(async (
    field: keyof T, 
    value: T[keyof T]
  ): Promise<string | null> => {
    const fieldConfig = fields[field];
    if (!fieldConfig) return null;

    const { rules } = fieldConfig;

    // 必填验证
    if (rules.required && (value === null || value === undefined || value === '')) {
      return fieldConfig.message || `${String(field)}是必填项`;
    }

    // 如果值为空且不是必填，跳过其他验证
    if (value === null || value === undefined || value === '') {
      return null;
    }

    // 字符串长度验证
    if (typeof value === 'string') {
      if (rules.minLength && value.length < rules.minLength) {
        return fieldConfig.message || `${String(field)}最少需要${rules.minLength}个字符`;
      }
      if (rules.maxLength && value.length > rules.maxLength) {
        return fieldConfig.message || `${String(field)}最多允许${rules.maxLength}个字符`;
      }
    }

    // 数值范围验证
    if (typeof value === 'number') {
      if (rules.min !== undefined && value < rules.min) {
        return fieldConfig.message || `${String(field)}不能小于${rules.min}`;
      }
      if (rules.max !== undefined && value > rules.max) {
        return fieldConfig.message || `${String(field)}不能大于${rules.max}`;
      }
    }

    // 正则表达式验证
    if (rules.pattern && typeof value === 'string') {
      if (!rules.pattern.test(value)) {
        return fieldConfig.message || `${String(field)}格式不正确`;
      }
    }

    // 自定义验证
    if (rules.custom) {
      const customError = rules.custom(value);
      if (customError) {
        return customError;
      }
    }

    // 异步验证
    if (rules.asyncValidator) {
      try {
        const asyncError = await rules.asyncValidator(value);
        if (asyncError) {
          return asyncError;
        }
      } catch (error) {
        return '验证过程中发生错误';
      }
    }

    return null;
  }, [fields]);

  // 验证指定字段或所有字段
  const validate = useCallback(async (field?: keyof T): Promise<boolean> => {
    setIsValidating(true);

    try {
      if (field) {
        // 验证单个字段
        const value = initialData?.[field];
        const error = await validateField(field, value as T[keyof T]);
        
        setErrors(prev => {
          const newErrors = { ...prev };
          if (error) {
            newErrors[field] = error;
          } else {
            delete newErrors[field];
          }
          return newErrors;
        });

        return !error;
      } else {
        // 验证所有字段
        return await validateAll();
      }
    } finally {
      setIsValidating(false);
    }
  }, [validateField, initialData]);

  // 验证所有字段
  const validateAll = useCallback(async (): Promise<boolean> => {
    if (!initialData) return true;

    setIsValidating(true);
    const newErrors: Partial<Record<keyof T, string>> = {};
    let hasValidationErrors = false;

    try {
      for (const field of Object.keys(fields) as (keyof T)[]) {
        const value = initialData[field];
        const error = await validateField(field, value as T[keyof T]);
        
        if (error) {
          newErrors[field] = error;
          hasValidationErrors = true;
          
          if (stopOnFirstError) {
            break;
          }
        }
      }

      setErrors(newErrors);
      return !hasValidationErrors;
    } finally {
      setIsValidating(false);
    }
  }, [fields, validateField, initialData, stopOnFirstError]);

  // 清除错误
  const clearErrors = useCallback((field?: keyof T) => {
    if (field) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[field];
        return newErrors;
      });
    } else {
      setErrors({});
    }
  }, []);

  // 设置错误
  const setError = useCallback((field: keyof T, error: string) => {
    setErrors(prev => ({
      ...prev,
      [field]: error,
    }));
  }, []);

  // 标记字段为已触摸
  const markTouched = useCallback((field: keyof T) => {
    setTouchedFields(prev => new Set(prev).add(field));
  }, []);

  // 重置验证状态
  const reset = useCallback(() => {
    setErrors({});
    setIsValidating(false);
    setTouchedFields(new Set());
  }, []);

  return {
    // 状态
    errors,
    isValidating,
    isValid,
    hasErrors,
    touchedFields,

    // 操作
    validate,
    validateField,
    validateAll,
    clearErrors,
    setError,
    markTouched,
    reset,
  };
}

// 常用验证规则
export const commonValidationRules = {
  email: {
    pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
    message: '请输入有效的邮箱地址',
  },
  phone: {
    pattern: /^1[3-9]\d{9}$/,
    message: '请输入有效的手机号码',
  },
  url: {
    pattern: /^https?:\/\/.+/,
    message: '请输入有效的URL地址',
  },
  number: {
    pattern: /^\d+$/,
    message: '请输入有效的数字',
  },
  positiveNumber: {
    min: 0,
    message: '请输入正数',
  },
} as const;

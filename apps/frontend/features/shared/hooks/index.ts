/**
 * 共享Hooks统一导出
 * 🎯 核心价值：跨feature共享的hooks集中管理
 * 📦 功能：通用hooks、性能优化、状态管理
 */

// 业务数据管理
export { useBusinessData } from './useBusinessData';
export type {
  BusinessDataType,
  QueryConditions,
  BusinessStats,
  BusinessDataConfig,
  UseBusinessDataReturn
} from './useBusinessData';

// 通用验证
export { useValidation, commonValidationRules } from './useValidation';
export type {
  ValidationRule,
  FieldValidation,
  ValidationConfig,
  ValidationState,
  ValidationActions,
  UseValidationReturn
} from './useValidation';

// Store集成
export {
  useStoreIntegration,
  useSingleStore,
  storeIntegrationUtils
} from './useStoreIntegration';
export type {
  StoreSelector,
  StoreConfig,
  MultiStoreConfig,
  StoreIntegrationOptions,
  StoreIntegrationResult
} from './useStoreIntegration';

// 性能优化
export {
  useComputationCache,
  useBatchOperation,
  useDebounce,
  useThrottle
} from './usePerformanceOptimization';
export type {
  CacheConfig,
  BatchConfig,
  DebounceConfig,
  ThrottleConfig
} from './usePerformanceOptimization';



// 错误处理
export { useErrorHandler } from './useErrorHandler';
export type {
  ErrorType,
  ErrorSeverity,
  ErrorInfo,
  ErrorHandlerConfig,
  UseErrorHandlerReturn
} from './useErrorHandler';

// 加载状态管理
export { useLoadingState } from './useLoadingState';
export type {
  LoadingTask,
  LoadingStateConfig,
  UseLoadingStateReturn
} from './useLoadingState';



// 反馈收集
export { useFeedbackCollector } from './useFeedbackCollector';
export type {
  FeedbackType,
  FeedbackSeverity,
  FeedbackData,
  UsageStats,
  FeedbackCollectorConfig,
  UseFeedbackCollectorReturn
} from './useFeedbackCollector';

// 统一导出，保持兼容性
export const SharedHooks = {
  useBusinessData: () => import('./useBusinessData').then(m => m.useBusinessData),
  useValidation: () => import('./useValidation').then(m => m.useValidation),
  useStoreIntegration: () => import('./useStoreIntegration').then(m => m.useStoreIntegration),
  useErrorHandler: () => import('./useErrorHandler').then(m => m.useErrorHandler),
  useLoadingState: () => import('./useLoadingState').then(m => m.useLoadingState),
};

/**
 * 通用Store集成Hook
 * 🎯 核心价值：统一多Store集成模式，简化状态管理
 * 📦 功能：多Store订阅、状态同步、选择器优化
 * ⚡ 类型安全：完整的TypeScript支持
 */

import { useCallback, useMemo, useRef, useEffect } from 'react';

// Store选择器类型
export type StoreSelector<TStore, TSelected> = (store: TStore) => TSelected;

// Store配置
export interface StoreConfig<TStore, TSelected> {
  store: () => TStore;
  selector: StoreSelector<TStore, TSelected>;
  equalityFn?: (a: TSelected, b: TSelected) => boolean;
}

// 多Store配置
export interface MultiStoreConfig {
  [key: string]: StoreConfig<any, any>;
}

// Store集成选项
export interface StoreIntegrationOptions {
  enableBatching?: boolean;
  enableDeepEqual?: boolean;
  enableMemoization?: boolean;
}

// Store集成返回类型
export type StoreIntegrationResult<T extends MultiStoreConfig> = {
  [K in keyof T]: T[K] extends StoreConfig<any, infer TSelected> ? TSelected : never;
} & {
  // 工具方法
  refresh: () => void;
  subscribe: (callback: () => void) => () => void;
  getSnapshot: () => any;
};

// 默认相等性比较函数
const defaultEqualityFn = <T>(a: T, b: T): boolean => {
  return Object.is(a, b);
};

// 深度相等性比较函数
const deepEqualityFn = <T>(a: T, b: T): boolean => {
  if (Object.is(a, b)) return true;
  
  if (typeof a !== 'object' || a === null || typeof b !== 'object' || b === null) {
    return false;
  }
  
  const keysA = Object.keys(a);
  const keysB = Object.keys(b);
  
  if (keysA.length !== keysB.length) return false;
  
  for (const key of keysA) {
    if (!keysB.includes(key)) return false;
    if (!deepEqualityFn((a as any)[key], (b as any)[key])) return false;
  }
  
  return true;
};

export function useStoreIntegration<T extends MultiStoreConfig>(
  config: T,
  options: StoreIntegrationOptions = {}
): StoreIntegrationResult<T> {
  const {
    enableBatching = true,
    enableDeepEqual = false,
    enableMemoization = true,
  } = options;

  // 缓存引用
  const configRef = useRef(config);
  const subscribersRef = useRef<Set<() => void>>(new Set());
  const lastValuesRef = useRef<Record<string, any>>({});

  // 更新配置引用
  useEffect(() => {
    configRef.current = config;
  }, [config]);

  // 获取当前值
  const getCurrentValues = useCallback(() => {
    const values: Record<string, any> = {};
    
    for (const [key, storeConfig] of Object.entries(configRef.current)) {
      try {
        const store = storeConfig.store();
        const selected = storeConfig.selector(store);
        values[key] = selected;
      } catch (error) {
        process.env.NODE_ENV === 'development' && console.warn(`Store integration error for ${key}:`, error);
        values[key] = undefined;
      }
    }
    
    return values;
  }, []);

  // 检查值是否发生变化
  const hasValuesChanged = useCallback((newValues: Record<string, any>) => {
    const lastValues = lastValuesRef.current;
    
    for (const [key, newValue] of Object.entries(newValues)) {
      const lastValue = lastValues[key];
      const storeConfig = configRef.current[key];
      
      if (!storeConfig) continue;
      
      const equalityFn = storeConfig.equalityFn || 
        (enableDeepEqual ? deepEqualityFn : defaultEqualityFn);
      
      if (!equalityFn(lastValue, newValue)) {
        return true;
      }
    }
    
    return false;
  }, [enableDeepEqual]);

  // 通知订阅者
  const notifySubscribers = useCallback(() => {
    if (enableBatching) {
      // 批量更新，在下一个微任务中执行
      Promise.resolve().then(() => {
        subscribersRef.current.forEach(callback => {
          try {
            callback();
          } catch (error) {
            process.env.NODE_ENV === 'development' && console.error('Store subscriber error:', error);
          }
        });
      });
    } else {
      // 立即更新
      subscribersRef.current.forEach(callback => {
        try {
          callback();
        } catch (error) {
          process.env.NODE_ENV === 'development' && console.error('Store subscriber error:', error);
        }
      });
    }
  }, [enableBatching]);

  // 刷新所有值
  const refresh = useCallback(() => {
    const newValues = getCurrentValues();
    
    if (hasValuesChanged(newValues)) {
      lastValuesRef.current = newValues;
      notifySubscribers();
    }
  }, [getCurrentValues, hasValuesChanged, notifySubscribers]);

  // 订阅变化
  const subscribe = useCallback((callback: () => void) => {
    subscribersRef.current.add(callback);
    
    return () => {
      subscribersRef.current.delete(callback);
    };
  }, []);

  // 获取快照
  const getSnapshot = useCallback(() => {
    return lastValuesRef.current;
  }, []);

  // 计算当前值（带缓存）
  const currentValues = useMemo(() => {
    const values = getCurrentValues();
    
    if (enableMemoization) {
      // 只有在值真正改变时才更新
      if (hasValuesChanged(values)) {
        lastValuesRef.current = values;
      } else {
        return lastValuesRef.current;
      }
    }
    
    lastValuesRef.current = values;
    return values;
  }, [getCurrentValues, hasValuesChanged, enableMemoization]);

  // 初始化
  useEffect(() => {
    lastValuesRef.current = getCurrentValues();
  }, [getCurrentValues]);

  // 创建结果对象
  const result = useMemo(() => {
    const storeValues = { ...currentValues };
    
    return {
      ...storeValues,
      refresh,
      subscribe,
      getSnapshot,
    } as StoreIntegrationResult<T>;
  }, [currentValues, refresh, subscribe, getSnapshot]);

  return result;
}

// 便捷的单Store集成Hook
export function useSingleStore<TStore, TSelected>(
  store: () => TStore,
  selector: StoreSelector<TStore, TSelected>,
  equalityFn?: (a: TSelected, b: TSelected) => boolean
): TSelected {
  const config = useMemo(() => ({
    value: { store, selector, equalityFn },
  }), [store, selector, equalityFn]);

  const result = useStoreIntegration(config);
  return result.value;
}

// Store集成工具函数
export const storeIntegrationUtils = {
  // 创建选择器
  createSelector: <TStore, TSelected>(
    selector: StoreSelector<TStore, TSelected>
  ) => selector,

  // 创建复合选择器
  createCompositeSelector: <TStore, T1, T2, TResult>(
    selector1: StoreSelector<TStore, T1>,
    selector2: StoreSelector<TStore, T2>,
    combiner: (a: T1, b: T2) => TResult
  ): StoreSelector<TStore, TResult> => {
    return (store: TStore) => {
      const a = selector1(store);
      const b = selector2(store);
      return combiner(a, b);
    };
  },

  // 创建缓存选择器
  createMemoizedSelector: <TStore, TSelected>(
    selector: StoreSelector<TStore, TSelected>,
    equalityFn?: (a: TSelected, b: TSelected) => boolean
  ) => {
    let lastInput: TStore | undefined;
    let lastOutput: TSelected;
    const isEqual = equalityFn || defaultEqualityFn;

    return (store: TStore): TSelected => {
      if (lastInput === undefined || !Object.is(lastInput, store)) {
        const newOutput = selector(store);
        if (lastInput === undefined || !isEqual(lastOutput, newOutput)) {
          lastOutput = newOutput;
        }
        lastInput = store;
      }
      return lastOutput;
    };
  },
};

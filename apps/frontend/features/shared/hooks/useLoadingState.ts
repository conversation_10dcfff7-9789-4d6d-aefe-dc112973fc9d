/**
 * 统一加载状态管理Hook
 * 🎯 核心价值：统一加载状态管理模式，减少重复的加载状态代码
 * 📦 功能：多任务加载状态、加载进度、加载超时、加载取消
 * ⚡ 类型安全：完整的TypeScript支持
 */

import { useState, useCallback, useRef, useEffect } from 'react';

// 加载任务类型
export interface LoadingTask {
  id: string;
  name: string;
  startTime: number;
  timeout?: number;
  progress?: number;
  status: 'pending' | 'running' | 'completed' | 'failed' | 'cancelled';
  error?: string;
}

// 加载状态配置
export interface LoadingStateConfig {
  // 默认超时时间（毫秒）
  defaultTimeout?: number;
  
  // 是否启用进度跟踪
  enableProgress?: boolean;
  
  // 是否启用任务队列
  enableQueue?: boolean;
  
  // 最大并发任务数
  maxConcurrentTasks?: number;
  
  // 回调函数
  onTaskStart?: (task: LoadingTask) => void;
  onTaskComplete?: (task: LoadingTask) => void;
  onTaskFail?: (task: LoadingTask) => void;
  onTaskCancel?: (task: LoadingTask) => void;
  onTimeout?: (task: LoadingTask) => void;
}

// Hook返回类型
export interface UseLoadingStateReturn {
  // 加载状态
  isLoading: boolean;
  isAnyLoading: boolean;
  loadingTasks: LoadingTask[];
  activeTasks: LoadingTask[];
  completedTasks: LoadingTask[];
  failedTasks: LoadingTask[];
  
  // 进度信息
  overallProgress: number;
  totalTasks: number;
  completedCount: number;
  failedCount: number;
  
  // 任务控制
  startTask: (name: string, options?: { timeout?: number; id?: string }) => string;
  completeTask: (taskId: string, result?: any) => void;
  failTask: (taskId: string, error: string) => void;
  cancelTask: (taskId: string) => void;
  updateProgress: (taskId: string, progress: number) => void;
  
  // 批量操作
  startMultipleTasks: (tasks: Array<{ name: string; timeout?: number }>) => string[];
  cancelAllTasks: () => void;
  clearCompletedTasks: () => void;
  clearAllTasks: () => void;
  
  // 工具方法
  wrapAsync: <T>(
    taskName: string,
    fn: (updateProgress?: (progress: number) => void) => Promise<T>,
    options?: { timeout?: number }
  ) => Promise<T>;
  
  getTaskById: (taskId: string) => LoadingTask | undefined;
  getTasksByName: (name: string) => LoadingTask[];
  
  // 状态查询
  isTaskRunning: (taskId: string) => boolean;
  hasFailedTasks: boolean;
  getAverageTaskDuration: () => number;
}

// 生成任务ID
const generateTaskId = (): string => {
  return `task_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
};

export function useLoadingState(config: LoadingStateConfig = {}): UseLoadingStateReturn {
  const {
    defaultTimeout = 30000, // 30秒默认超时
    enableProgress = true,
    enableQueue = false,
    maxConcurrentTasks = 5,
    onTaskStart,
    onTaskComplete,
    onTaskFail,
    onTaskCancel,
    onTimeout,
  } = config;

  // 状态管理
  const [tasks, setTasks] = useState<LoadingTask[]>([]);
  
  // 引用管理
  const timeoutRefs = useRef<Map<string, NodeJS.Timeout>>(new Map());
  const abortControllers = useRef<Map<string, AbortController>>(new Map());

  // 计算派生状态
  const activeTasks = tasks.filter(task => task.status === 'running');
  const completedTasks = tasks.filter(task => task.status === 'completed');
  const failedTasks = tasks.filter(task => task.status === 'failed');
  
  const isLoading = activeTasks.length > 0;
  const isAnyLoading = tasks.some(task => task.status === 'running' || task.status === 'pending');
  const hasFailedTasks = failedTasks.length > 0;
  
  const totalTasks = tasks.length;
  const completedCount = completedTasks.length;
  const failedCount = failedTasks.length;
  
  // 计算整体进度
  const overallProgress = totalTasks === 0 ? 0 : 
    Math.round((completedCount / totalTasks) * 100);

  // 开始任务
  const startTask = useCallback((
    name: string,
    options: { timeout?: number; id?: string } = {}
  ): string => {
    const taskId = options.id || generateTaskId();
    const timeout = options.timeout || defaultTimeout;
    
    const newTask: LoadingTask = {
      id: taskId,
      name,
      startTime: Date.now(),
      timeout,
      progress: enableProgress ? 0 : undefined,
      status: enableQueue && activeTasks.length >= maxConcurrentTasks ? 'pending' : 'running',
    };

    setTasks(prev => [...prev, newTask]);

    // 设置超时
    if (timeout > 0) {
      const timeoutId = setTimeout(() => {
        failTask(taskId, `任务超时 (${timeout}ms)`);
        onTimeout?.(newTask);
      }, timeout);
      timeoutRefs.current.set(taskId, timeoutId);
    }

    // 调用开始回调
    onTaskStart?.(newTask);

    return taskId;
  }, [defaultTimeout, enableProgress, enableQueue, activeTasks.length, maxConcurrentTasks, onTaskStart, onTimeout]);

  // 完成任务
  const completeTask = useCallback((taskId: string, result?: any) => {
    setTasks(prev => prev.map(task => 
      task.id === taskId 
        ? { ...task, status: 'completed' as const, progress: enableProgress ? 100 : undefined }
        : task
    ));

    // 清理超时
    const timeoutId = timeoutRefs.current.get(taskId);
    if (timeoutId) {
      clearTimeout(timeoutId);
      timeoutRefs.current.delete(taskId);
    }

    // 清理AbortController
    abortControllers.current.delete(taskId);

    // 调用完成回调
    const task = tasks.find(t => t.id === taskId);
    if (task) {
      onTaskComplete?.({ ...task, status: 'completed' });
    }
  }, [enableProgress, tasks, onTaskComplete]);

  // 失败任务
  const failTask = useCallback((taskId: string, error: string) => {
    setTasks(prev => prev.map(task => 
      task.id === taskId 
        ? { ...task, status: 'failed' as const, error }
        : task
    ));

    // 清理超时
    const timeoutId = timeoutRefs.current.get(taskId);
    if (timeoutId) {
      clearTimeout(timeoutId);
      timeoutRefs.current.delete(taskId);
    }

    // 清理AbortController
    abortControllers.current.delete(taskId);

    // 调用失败回调
    const task = tasks.find(t => t.id === taskId);
    if (task) {
      onTaskFail?.({ ...task, status: 'failed', error });
    }
  }, [tasks, onTaskFail]);

  // 取消任务
  const cancelTask = useCallback((taskId: string) => {
    setTasks(prev => prev.map(task => 
      task.id === taskId 
        ? { ...task, status: 'cancelled' as const }
        : task
    ));

    // 清理超时
    const timeoutId = timeoutRefs.current.get(taskId);
    if (timeoutId) {
      clearTimeout(timeoutId);
      timeoutRefs.current.delete(taskId);
    }

    // 取消请求
    const controller = abortControllers.current.get(taskId);
    if (controller) {
      controller.abort();
      abortControllers.current.delete(taskId);
    }

    // 调用取消回调
    const task = tasks.find(t => t.id === taskId);
    if (task) {
      onTaskCancel?.({ ...task, status: 'cancelled' });
    }
  }, [tasks, onTaskCancel]);

  // 更新进度
  const updateProgress = useCallback((taskId: string, progress: number) => {
    if (!enableProgress) return;
    
    setTasks(prev => prev.map(task => 
      task.id === taskId 
        ? { ...task, progress: Math.max(0, Math.min(100, progress)) }
        : task
    ));
  }, [enableProgress]);

  // 批量开始任务
  const startMultipleTasks = useCallback((taskConfigs: Array<{ name: string; timeout?: number }>) => {
    return taskConfigs.map(config => startTask(config.name, { timeout: config.timeout }));
  }, [startTask]);

  // 取消所有任务
  const cancelAllTasks = useCallback(() => {
    activeTasks.forEach(task => cancelTask(task.id));
  }, [activeTasks, cancelTask]);

  // 清理已完成任务
  const clearCompletedTasks = useCallback(() => {
    setTasks(prev => prev.filter(task => task.status !== 'completed'));
  }, []);

  // 清理所有任务
  const clearAllTasks = useCallback(() => {
    // 取消所有活动任务
    cancelAllTasks();
    // 清理所有任务
    setTasks([]);
  }, [cancelAllTasks]);

  // 包装异步函数
  const wrapAsync = useCallback(<T>(
    taskName: string,
    fn: (updateProgress?: (progress: number) => void) => Promise<T>,
    options: { timeout?: number } = {}
  ): Promise<T> => {
    const taskId = startTask(taskName, options);
    
    const progressUpdater = enableProgress 
      ? (progress: number) => updateProgress(taskId, progress)
      : undefined;
    
    return fn(progressUpdater)
      .then(result => {
        completeTask(taskId, result);
        return result;
      })
      .catch(error => {
        failTask(taskId, error?.message || String(error));
        throw error;
      });
  }, [startTask, enableProgress, updateProgress, completeTask, failTask]);

  // 查询方法
  const getTaskById = useCallback((taskId: string) => {
    return tasks.find(task => task.id === taskId);
  }, [tasks]);

  const getTasksByName = useCallback((name: string) => {
    return tasks.filter(task => task.name === name);
  }, [tasks]);

  const isTaskRunning = useCallback((taskId: string) => {
    const task = getTaskById(taskId);
    return task?.status === 'running';
  }, [getTaskById]);

  const getAverageTaskDuration = useCallback(() => {
    const completedTasksWithDuration = completedTasks.filter(task => task.startTime);
    if (completedTasksWithDuration.length === 0) return 0;
    
    const totalDuration = completedTasksWithDuration.reduce((sum, task) => {
      return sum + (Date.now() - task.startTime);
    }, 0);
    
    return totalDuration / completedTasksWithDuration.length;
  }, [completedTasks]);

  // 清理效果
  useEffect(() => {
    return () => {
      // 清理所有超时
      timeoutRefs.current.forEach(timeoutId => clearTimeout(timeoutId));
      timeoutRefs.current.clear();
      
      // 取消所有请求
      abortControllers.current.forEach(controller => controller.abort());
      abortControllers.current.clear();
    };
  }, []);

  return {
    // 加载状态
    isLoading,
    isAnyLoading,
    loadingTasks: tasks,
    activeTasks,
    completedTasks,
    failedTasks,
    
    // 进度信息
    overallProgress,
    totalTasks,
    completedCount,
    failedCount,
    
    // 任务控制
    startTask,
    completeTask,
    failTask,
    cancelTask,
    updateProgress,
    
    // 批量操作
    startMultipleTasks,
    cancelAllTasks,
    clearCompletedTasks,
    clearAllTasks,
    
    // 工具方法
    wrapAsync,
    getTaskById,
    getTasksByName,
    
    // 状态查询
    isTaskRunning,
    hasFailedTasks,
    getAverageTaskDuration,
  };
}

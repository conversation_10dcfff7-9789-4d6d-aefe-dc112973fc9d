/**
 * 共享类型定义统一导出
 * 🎯 核心价值：跨feature共享的类型定义集中管理
 */

// 系统配置类型
export interface SystemConfig {
  appName: string;
  version: string;
  environment: 'development' | 'production' | 'test';
  features: {
    [key: string]: boolean;
  };
  limits: {
    maxFileSize: number;
    maxProjects: number;
    maxUsers: number;
  };
}

// 用户偏好类型
export interface UserPreferences {
  language: string;
  theme: 'light' | 'dark' | 'auto';
  timezone: string;
  dateFormat: string;
  timeFormat: '12h' | '24h';
  notifications: {
    enabled: boolean;
    email: boolean;
    push: boolean;
    sound: boolean;
  };
  accessibility: {
    highContrast: boolean;
    largeText: boolean;
    reduceMotion: boolean;
  };
}

// 应用状态类型
export interface AppState {
  isOnline: boolean;
  lastActivity: string;
  activeFeatures: string[];
  currentProject: string | null;
  sidebarCollapsed: boolean;
  fullscreen: boolean;
}

// 文件上传响应类型
export interface FileUploadResponse {
  id: string;
  filename: string;
  originalName: string;
  size: number;
  mimeType: string;
  url: string;
  uploadedAt: string;
}

// 验证结果类型
export interface ValidationResult {
  isValid: boolean;
  errors: {
    field: string;
    message: string;
    code: string;
  }[];
  warnings: {
    field: string;
    message: string;
  }[];
}

// 导出选项类型
export interface ExportOptions {
  format: 'json' | 'csv' | 'xlsx' | 'pdf';
  includeMetadata: boolean;
  dateRange?: {
    start: string;
    end: string;
  };
  filters?: Record<string, any>;
}

// 导入选项类型
export interface ImportOptions {
  overwrite: boolean;
  validateOnly: boolean;
  mapping?: Record<string, string>;
  defaultValues?: Record<string, any>;
}

// 重新导出API类型（避免重复定义）
export type { ApiResponse, PaginatedData as PaginatedResponse } from '@/lib/types/api';

/**
 * 共享状态管理Store
 * 🎯 核心价值：features间共享的状态管理
 * 📦 功能：全局配置、用户偏好、系统状态
 * ⚡ 类型安全：完整的TypeScript支持
 */

import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';
import type { SystemConfig, UserPreferences, AppState } from '../types';

interface SharedState {
  // 系统配置
  systemConfig: SystemConfig | null;
  
  // 用户偏好
  userPreferences: UserPreferences;
  
  // 应用状态
  appState: AppState;
  
  // 加载状态
  isLoading: boolean;
  
  // 错误状态
  error: string | null;
  
  // 操作方法
  setSystemConfig: (config: SystemConfig) => void;
  updateUserPreferences: (preferences: Partial<UserPreferences>) => void;
  setAppState: (state: Partial<AppState>) => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  reset: () => void;
}

// 默认用户偏好
const defaultUserPreferences: UserPreferences = {
  language: 'zh-CN',
  theme: 'light',
  timezone: 'Asia/Shanghai',
  dateFormat: 'YYYY-MM-DD',
  timeFormat: '24h',
  notifications: {
    enabled: true,
    email: true,
    push: false,
    sound: true,
  },
  accessibility: {
    highContrast: false,
    largeText: false,
    reduceMotion: false,
  },
};

// 默认应用状态
const defaultAppState: AppState = {
  isOnline: true,
  lastActivity: new Date().toISOString(),
  activeFeatures: [],
  currentProject: null,
  sidebarCollapsed: false,
  fullscreen: false,
};

// 初始状态
const initialState = {
  systemConfig: null,
  userPreferences: defaultUserPreferences,
  appState: defaultAppState,
  isLoading: false,
  error: null,
};

export const useSharedStore = create<SharedState>()(
  devtools(
    persist(
      (set, get) => ({
        ...initialState,
        
        setSystemConfig: (config: SystemConfig) => {
          set({ systemConfig: config });
        },
        
        updateUserPreferences: (preferences: Partial<UserPreferences>) => {
          set((state) => ({
            userPreferences: {
              ...state.userPreferences,
              ...preferences,
            },
          }));
        },
        
        setAppState: (appState: Partial<AppState>) => {
          set((state) => ({
            appState: {
              ...state.appState,
              ...appState,
            },
          }));
        },
        
        setLoading: (loading: boolean) => {
          set({ isLoading: loading });
        },
        
        setError: (error: string | null) => {
          set({ error });
        },
        
        reset: () => {
          set(initialState);
        },
      }),
      {
        name: 'shared-store',
        version: 1,
        partialize: (state) => ({
          userPreferences: state.userPreferences,
          appState: {
            sidebarCollapsed: state.appState.sidebarCollapsed,
            currentProject: state.appState.currentProject,
          },
        }),
        migrate: (persistedState: any, version: number) => {
          // 处理状态迁移
          if (version === 0) {
            // 从版本0迁移到版本1，确保所有必需字段存在
            return {
              userPreferences: persistedState.userPreferences || defaultUserPreferences,
              appState: {
                ...defaultAppState,
                ...(persistedState.appState || {}),
              },
            };
          }
          return persistedState;
        },
      }
    ),
    {
      name: 'shared-store',
    }
  )
);

// 便捷的选择器hooks
export const useSystemConfig = () => useSharedStore((state) => state.systemConfig);
export const useUserPreferences = () => useSharedStore((state) => state.userPreferences);
export const useAppState = () => useSharedStore((state) => state.appState);
export const useSharedLoading = () => useSharedStore((state) => state.isLoading);
export const useSharedError = () => useSharedStore((state) => state.error);

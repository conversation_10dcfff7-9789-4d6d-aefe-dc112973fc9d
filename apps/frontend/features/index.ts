/**
 * Features业务模块统一导出
 * 🎯 核心价值：按业务功能分组的模块化架构
 * 📦 架构设计：每个feature包含components、hooks、api、store、types
 */

// 网格系统业务模块 - 方案A重构版本
export * from './grid-system'

// 样式管理业务模块
export * from './style-management'

// 共享模块
export * from './shared'

// Features配置
export const FEATURES = {
  GRID_SYSTEM: 'grid-system',
  STYLE_MANAGEMENT: 'style-management',
  SHARED: 'shared',
} as const

export type FeatureName = typeof FEATURES[keyof typeof FEATURES]

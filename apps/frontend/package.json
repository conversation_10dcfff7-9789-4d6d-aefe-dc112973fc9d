{"name": "frontend-nextjs", "version": "0.1.1", "private": true, "scripts": {"dev": "next dev -H 0.0.0.0 -p 4096", "dev:port": "node scripts/dev-with-port.js", "build": "prisma generate && next build", "start": "next start -H 0.0.0.0 -p 4096", "lint": "next lint", "lint:fix": "next lint --fix", "type-check": "tsc --noEmit", "format": "prettier --write .", "format:check": "prettier --check .", "analyze": "pnpm run build && npx @next/bundle-analyzer", "pre-commit": "pnpm run lint:fix && pnpm run type-check && pnpm run naming:check", "ci": "pnpm run lint && pnpm run type-check && pnpm run build && pnpm run naming:check", "naming:check": "node scripts/check-naming-conventions.js", "naming:check:verbose": "node scripts/check-naming-conventions.js --verbose", "naming:fix": "node scripts/fix-naming-conventions.js", "naming:fix:preview": "node scripts/fix-naming-conventions.js --dry-run", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:studio": "prisma studio", "db:seed": "tsx prisma/seed.ts", "db:reset": "prisma migrate reset", "postinstall": "prisma generate", "dev:setup": "node scripts/dev-setup.js", "dev:full": "pnpm run dev:setup && pnpm run dev", "env:setup": "node scripts/setup-env.js", "env:dev": "node scripts/setup-env.js --env development", "env:prod": "node scripts/setup-env.js --env production", "build:prod": "node scripts/build-production.js", "deploy:vercel": "pnpm run env:prod && vercel --prod", "api:generate": "node scripts/generate-api-types.js", "api:generate-cli": "npx openapi-typescript-codegen --input ./api-schema.json --output ./types/api-generated --client fetch", "cicd:setup": "node scripts/setup-cicd.js", "ci:test": "node scripts/ci-test.js", "ci:local": "pnpm run lint && pnpm run type-check && pnpm run build", "deploy:preview": "vercel", "deploy:production": "vercel --prod", "start:quick": "node scripts/quick-start.js", "demo": "pnpm run start:quick", "cache:clear": "rm -rf .next/cache", "cache:clear-webpack": "rm -rf .next/cache/webpack", "dev:clean": "pnpm run cache:clear && pnpm run dev", "generate:api": "node scripts/generate-api-types.js", "generate:types": "pnpm run generate:api", "test": "vitest", "test:run": "vitest run", "test:e2e": "node scripts/run-e2e-tests.js", "test:e2e:ui": "playwright test --ui", "test:e2e:headed": "playwright test --headed", "test:e2e:debug": "playwright test --debug", "test:e2e:report": "playwright show-report", "test:e2e:ci": "playwright test --config=playwright-ci.config.ts"}, "dependencies": {"@heroicons/react": "^2.2.0", "@prisma/client": "^6.11.1", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-tooltip": "^1.2.7", "@tailwindcss/postcss": "^4.1.11", "@tanstack/react-query": "^5.83.0", "@tanstack/react-query-devtools": "^5.83.0", "@vercel/analytics": "^1.5.0", "@vercel/speed-insights": "^1.2.0", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "immer": "^10.1.1", "lucide-react": "^0.525.0", "next": "15.1.0", "prisma": "^6.11.1", "react": "^18.3.1", "react-dom": "^18.3.1", "react-hot-toast": "^2.5.2", "tailwind-merge": "^3.3.1", "tailwind-variants": "^1.0.0", "tailwindcss-animate": "^1.0.7", "zod": "^3.25.74", "zustand": "^5.0.6"}, "devDependencies": {"@playwright/test": "^1.54.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/jest": "^30.0.0", "@types/node": "^20.19.7", "@types/react": "^18.3.23", "@types/react-dom": "^18", "@typescript-eslint/eslint-plugin": "^8.36.0", "@typescript-eslint/parser": "^8.36.0", "eslint": "^8", "eslint-config-next": "^15.3.5", "eslint-plugin-storybook": "^9.0.18", "glob": "^11.0.3", "jscpd": "^4.0.5", "jsdom": "^26.1.0", "openapi-typescript-codegen": "^0.29.0", "postcss": "^8", "prettier": "^3.3.3", "tailwindcss": "^3.4.17", "tsx": "^4.20.3", "typescript": "^5.8.3", "vitest": "^3.2.4"}}
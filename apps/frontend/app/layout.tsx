import type { Metadata } from 'next';
import '../styles/globals.css';
import { QueryProvider } from '@/lib/query/provider';
export const metadata: Metadata = {
    title: 'cube',
    description: '@pokeby',
    metadataBase: new URL('http://localhost:3003/'),
    openGraph: {
        title: 'cube',
        description: '@pokeby',
        url: 'http://localhost:3003',
        siteName: 'cube',
        images: [
            {
                url: '/images/cube.png',
                width: 1200,
                height: 630,
                alt: 'cube',
            },
        ],

        type: 'website',
    },
};
export default function RootLayout({ children }: Readonly<{ children: React.ReactNode }>) {
    return (
        <html lang="en" style={{ overscrollBehaviorX: 'none' }}>
            <body className="rounded-none" style={{ overscrollBehaviorX: 'none' }}>
                <QueryProvider>
                    {children}
                </QueryProvider>
            </body>
        </html>
    );
}

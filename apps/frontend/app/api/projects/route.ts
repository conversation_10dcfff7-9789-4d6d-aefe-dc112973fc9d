/**
 * 项目管理API路由 - /api/projects
 * 🎯 核心功能：路由代理，委托给处理器
 * 🔄 RESTful设计：GET, POST
 * ⚡ 架构优化：消除重复，使用处理器模式
 */

import { NextRequest } from 'next/server';
import { withErrorHandling } from '@/lib/api/utils';
import { getAllProjects, createProject } from '@/lib/api/handlers';

// GET /api/projects - 获取项目列表
export const GET = withErrorHandling(async (request: NextRequest) => {
  return getAllProjects(request);
});

// POST /api/projects - 创建新项目
export const POST = withErrorHandling(async (request: NextRequest) => {
  return createProject(request);
});

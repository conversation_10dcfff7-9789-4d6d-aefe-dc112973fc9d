/**
 * 单个项目API路由 - /api/projects/[projectId]
 * 🎯 核心功能：路由代理，委托给处理器
 * 🔄 RESTful设计：GET, PUT, DELETE
 * ⚡ 架构优化：消除重复，使用处理器模式
 */

import { NextRequest } from 'next/server';
import { withErrorHandling } from '@/lib/api/utils';
import { getProjectById, updateProject, deleteProject } from '@/lib/api/handlers';

// GET /api/projects/[projectId] - 获取特定项目
export const GET = withErrorHandling(async (
  request: NextRequest,
  { params }: { params: Promise<{ projectId: string }> }
) => {
  const { projectId } = await params;
  return getProjectById(request, projectId);
});

// PUT /api/projects/[projectId] - 更新项目
export const PUT = withErrorHandling(async (
  request: NextRequest,
  { params }: { params: Promise<{ projectId: string }> }
) => {
  const { projectId } = await params;
  return updateProject(request, projectId);
});

// DELETE /api/projects/[projectId] - 删除项目
export const DELETE = withErrorHandling(async (
  request: NextRequest,
  { params }: { params: Promise<{ projectId: string }> }
) => {
  const { projectId } = await params;
  return deleteProject(request, projectId);
});

'use client';

import React, { useState, useCallback, useMemo } from 'react';
import { GridMatrix } from '@/components/grid-system';
import { StylePanel } from '@/features/style-management';
import type { BaseDisplayMode } from '@/components/grid-system';
import { useGridConfigStore, useColorModeEnabled, useGridConfigActions } from '@/stores/gridConfigStore';

// 强制动态渲染，避免静态生成时的Context错误
export const dynamic = 'force-dynamic';

export default function Page() {
    // 使用网格配置存储
    const { baseDisplayMode } = useGridConfigStore();
    const { setDisplayMode } = useGridConfigStore();
    const colorModeEnabled = useColorModeEnabled();
    const { setColorModeEnabled } = useGridConfigActions();



    // 悬停信息状态
    const [hoverInfo, setHoverInfo] = useState<string>('将鼠标悬停在格子上查看详细信息');

    // 单元格点击处理
    const handleCellClick = useCallback((cell: any, _event?: React.MouseEvent) => {
        process.env.NODE_ENV === 'development' && console.log('Cell clicked:', cell);
    }, []);

    // 基础显示模式变更处理
    const handleBaseDisplayModeChange = useCallback((mode: BaseDisplayMode) => {
        setDisplayMode(mode);
    }, [setDisplayMode]);

    // 颜色模式切换处理
    const handleColorModeToggle = useCallback((enabled: boolean) => {
        setColorModeEnabled(enabled);
    }, []);



    // 构建网格配置对象
    const gridConfig = useMemo(() => {
        const config = {
            size: 33, // 恢复正常大小
            cellShape: 'rounded' as const,
            displayMode: baseDisplayMode as 'value' | 'coordinates' | 'color',
            gap: 1,
            padding: 0,
            fontSize: 12,
            scale: {
                enabled: true,
                factor: 1.05,
            },
            animation: {
                enabled: true,
                duration: 200,
            },
        };
        return config;
    }, [baseDisplayMode]);

    return (
        <div className="h-screen flex bg-gray-50 overflow-hidden">
            {/* 主要内容区域 - 网格系统 */}
            <div className="flex-1 flex flex-col">
                {/* 网格容器 - 使用新的GridMatrix组件，支持滚动查看 */}
                <div className="flex-1 flex items-center justify-center p-4">
                    <div
                        className="overflow-auto scrollbar-thin"
                        style={{
                            scrollBehavior: 'smooth',
                            maxWidth: 'min(100vh - 2rem, 100vw - 2rem)',
                            maxHeight: 'min(100vh - 2rem, 100vw - 2rem)',
                            width: 'fit-content',
                            height: 'fit-content'
                        }}
                    >
                        <div className="w-full h-full flex items-center justify-center">
                            <GridMatrix
                                config={gridConfig}
                                onCellClick={handleCellClick}
                                onCellHover={(cell) => {
                                    if (cell) {
                                        // 生成悬停信息
                                        const info = `坐标: (${cell.x}, ${cell.y})${cell.color ? `, 颜色: ${cell.color}` : ''}${cell.level ? `, 级别: ${cell.level}` : ''}${cell.group ? `, 组别: ${cell.group}` : ''}`;
                                        setHoverInfo(info);
                                    } else {
                                        setHoverInfo('将鼠标悬停在格子上查看详细信息');
                                    }
                                }}
                                className="flex-shrink-0"
                            />
                        </div>
                    </div>
                </div>
            </div>

            {/* 右侧控制面板 - 使用新的StylePanel组件 */}
            <div className="w-80 bg-white border-l border-gray-200 p-4 overflow-y-auto">
                <div className="space-y-4">
                    {/* 新的样式管理面板 */}
                    <StylePanel
                        className="mb-4"
                        initialBaseDisplayMode={baseDisplayMode}
                        initialColorModeEnabled={colorModeEnabled}
                        onBaseDisplayModeChange={handleBaseDisplayModeChange}
                        onColorModeToggle={handleColorModeToggle}
                    />
                    {/* 悬停信息显示 - 统一黑白灰设计 */}
                    <div className="border rounded-lg bg-gray-50 border-gray-200">
                        <div className="p-3">
                            <div className="text-sm font-medium text-gray-800">悬停信息</div>
                            <div className="text-xs text-gray-600 mt-1">
                                {hoverInfo}
                            </div>
                        </div>
                    </div>


                </div>
            </div>


        </div>
    );
}
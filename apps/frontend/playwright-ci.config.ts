import { defineConfig, devices } from '@playwright/test';

/**
 * CI 环境专用的 Playwright 配置
 * 解决 --no-sandbox 警告并优化 CI 性能
 * @see https://playwright.dev/docs/test-configuration
 */
export default defineConfig({
  testDir: './tests/e2e',
  fullyParallel: false, // CI 环境中禁用并行以提高稳定性
  forbidOnly: true,
  retries: 3, // CI 环境中增加重试次数
  workers: 1, // CI 环境中使用单个 worker
  reporter: [
    ['html'],
    ['junit', { outputFile: 'test-results/junit.xml' }],
    ['github'],
  ],
  
  use: {
    baseURL: 'http://localhost:4096',
    trace: 'retain-on-failure',
    screenshot: 'only-on-failure',
    video: 'retain-on-failure',
    
    // CI 环境优化设置
    actionTimeout: 30000,
    navigationTimeout: 30000,
  },

  projects: [
    {
      name: 'chromium-ci',
      use: { 
        ...devices['Desktop Chrome'],
        launchOptions: {
          args: [
            '--no-sandbox',
            '--disable-setuid-sandbox',
            '--disable-dev-shm-usage',
            '--disable-gpu',
            '--no-first-run',
            '--no-zygote',
            '--single-process',
            '--disable-background-timer-throttling',
            '--disable-backgrounding-occluded-windows',
            '--disable-renderer-backgrounding',
            '--disable-features=TranslateUI',
            '--disable-ipc-flooding-protection',
          ],
        },
      },
    },
  ],

  webServer: {
    command: 'pnpm run dev',
    url: 'http://localhost:4096',
    reuseExistingServer: false,
    timeout: 180 * 1000, // 3 分钟超时
  },
});

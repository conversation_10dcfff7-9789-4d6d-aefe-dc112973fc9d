import { defineConfig } from 'vitest/config';
import path from 'path';

export default defineConfig({
  test: {
    environment: 'jsdom',
    globals: true,
    setupFiles: ['./vitest.setup.ts'],
  },
  resolve: {
    alias: {
      '@': path.resolve(__dirname, '.'),
      '@/components': path.resolve(__dirname, './components'),
      '@/lib': path.resolve(__dirname, './lib'),
      '@/utils': path.resolve(__dirname, './lib/utils'),
      '@/types': path.resolve(__dirname, './types'),
      '@/stores': path.resolve(__dirname, './stores'),
      '@/features': path.resolve(__dirname, './features'),
      '@/api': path.resolve(__dirname, './lib/api'),
      '@/app': path.resolve(__dirname, './app'),
    },
  },
});
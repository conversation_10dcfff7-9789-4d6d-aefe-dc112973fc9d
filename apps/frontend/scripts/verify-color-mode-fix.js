/**
 * 验证颜色模式修复的简化脚本 - 统一状态管理版本
 *
 * 在浏览器控制台中运行此脚本来验证修复效果
 * 现在颜色模式状态由 gridConfigStore 统一管理
 */

console.log('🧪 开始验证颜色模式修复（统一状态管理版本）...');

// 查找颜色模式开关
function findColorModeToggle() {
  // 查找包含"颜色模式"文本的开关
  const labels = document.querySelectorAll('label, span, div');
  for (const label of labels) {
    if (label.textContent && label.textContent.includes('颜色模式')) {
      const toggle = label.querySelector('input[type="checkbox"]') || 
                    label.parentElement?.querySelector('input[type="checkbox"]') ||
                    label.nextElementSibling?.querySelector('input[type="checkbox"]');
      if (toggle) {
        return toggle;
      }
    }
  }
  return null;
}

// 获取格子背景颜色统计
function analyzeGridColors() {
  const cells = document.querySelectorAll('[data-testid="grid-matrix"] > div');
  const colorCounts = {};
  let totalCells = 0;
  
  cells.forEach(cell => {
    const style = window.getComputedStyle(cell);
    const bgColor = style.backgroundColor;
    if (bgColor && bgColor !== 'rgba(0, 0, 0, 0)' && bgColor !== 'transparent') {
      colorCounts[bgColor] = (colorCounts[bgColor] || 0) + 1;
      totalCells++;
    }
  });
  
  const defaultGray = 'rgb(243, 244, 246)'; // #f3f4f6
  const grayCount = colorCounts[defaultGray] || 0;
  const coloredCount = totalCells - grayCount;
  
  return {
    totalCells,
    colorCounts,
    uniqueColors: Object.keys(colorCounts).length,
    grayCount,
    coloredCount,
    hasMultipleColors: Object.keys(colorCounts).length > 1,
    isMainlyGray: grayCount > (totalCells * 0.8)
  };
}

// 主验证函数
async function verifyFix() {
  try {
    // 查找颜色模式开关
    const toggle = findColorModeToggle();
    if (!toggle) {
      console.error('❌ 未找到颜色模式开关');
      return false;
    }
    
    console.log('✅ 找到颜色模式开关');
    
    // 测试关闭状态
    if (toggle.checked) {
      toggle.click();
      await new Promise(resolve => setTimeout(resolve, 300));
    }
    
    const disabledState = analyzeGridColors();
    console.log('\n📊 颜色模式关闭状态:');
    console.log(`   总格子数: ${disabledState.totalCells}`);
    console.log(`   颜色种类: ${disabledState.uniqueColors}`);
    console.log(`   灰色格子: ${disabledState.grayCount}`);
    console.log(`   彩色格子: ${disabledState.coloredCount}`);
    
    // 测试开启状态
    toggle.click();
    await new Promise(resolve => setTimeout(resolve, 300));
    
    const enabledState = analyzeGridColors();
    console.log('\n📊 颜色模式开启状态:');
    console.log(`   总格子数: ${enabledState.totalCells}`);
    console.log(`   颜色种类: ${enabledState.uniqueColors}`);
    console.log(`   灰色格子: ${enabledState.grayCount}`);
    console.log(`   彩色格子: ${enabledState.coloredCount}`);
    
    // 验证修复效果
    const isFixWorking = 
      disabledState.isMainlyGray && 
      enabledState.hasMultipleColors && 
      enabledState.coloredCount > disabledState.coloredCount;
    
    console.log('\n🔍 验证结果:');
    if (isFixWorking) {
      console.log('✅ 修复成功！');
      console.log('   - 颜色模式关闭时主要显示灰色');
      console.log('   - 颜色模式开启时显示多种颜色');
      console.log('   - 包括之前 isActive: false 的格子也能显示颜色');
    } else {
      console.log('❌ 修复可能存在问题:');
      if (!disabledState.isMainlyGray) {
        console.log('   - 关闭时没有主要显示灰色');
      }
      if (!enabledState.hasMultipleColors) {
        console.log('   - 开启时没有显示多种颜色');
      }
      if (enabledState.coloredCount <= disabledState.coloredCount) {
        console.log('   - 开启时彩色格子数量没有增加');
      }
    }
    
    return isFixWorking;
    
  } catch (error) {
    console.error('❌ 验证过程中出现错误:', error);
    return false;
  }
}

// 自动运行验证
setTimeout(verifyFix, 1000);

// 导出函数供手动调用
window.verifyColorModeFix = verifyFix;

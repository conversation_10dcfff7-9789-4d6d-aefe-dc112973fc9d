#!/usr/bin/env node

/**
 * 命名规范修正脚本
 * 🎯 核心价值：自动修正项目中不符合命名规范的文件和目录
 * 📦 功能：文件重命名、导入更新、路径修正
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// 配置
const CONFIG = {
  dryRun: process.argv.includes('--dry-run'),
  verbose: process.argv.includes('--verbose'),
  force: process.argv.includes('--force'),
};

// 日志工具
const log = {
  info: (msg) => console.log(`ℹ️  ${msg}`),
  success: (msg) => console.log(`✅ ${msg}`),
  warning: (msg) => console.log(`⚠️  ${msg}`),
  error: (msg) => console.log(`❌ ${msg}`),
  verbose: (msg) => CONFIG.verbose && console.log(`🔍 ${msg}`),
};

// 需要修正的文件映射
const FILE_RENAMES = [
  {
    from: 'lib/hooks/CellDataManager.ts',
    to: 'lib/hooks/useCellDataManager.ts',
    type: 'hook',
    description: '钩子文件添加 use 前缀'
  },
  {
    from: 'lib/hooks/LogManager.ts',
    to: 'lib/hooks/useLogManager.ts',
    type: 'hook',
    description: '钩子文件添加 use 前缀'
  }
];

// 需要修正的目录映射
const DIRECTORY_RENAMES = [
  {
    from: 'components/ControlPanel',
    to: 'components/control-panel',
    type: 'component-directory',
    description: '功能目录使用 kebab-case'
  }
];

// 导入更新规则
const IMPORT_UPDATES = [
  {
    pattern: /from ['"].*\/CellDataManager['"];?/g,
    replacement: (match) => match.replace('CellDataManager', 'useCellDataManager'),
    description: 'CellDataManager 导入更新'
  },
  {
    pattern: /import.*useCellDataManager.*from/g,
    replacement: (match) => match.replace('CellDataManager', 'useCellDataManager'),
    description: 'CellDataManager 导入语句更新'
  },
  {
    pattern: /from ['"].*\/LogManager['"];?/g,
    replacement: (match) => match.replace('LogManager', 'useLogManager'),
    description: 'LogManager 导入更新'
  },
  {
    pattern: /import.*useLogManager.*from/g,
    replacement: (match) => match.replace('LogManager', 'useLogManager'),
    description: 'LogManager 导入语句更新'
  },
  {
    pattern: /from ['"].*\/ControlPanel/g,
    replacement: (match) => match.replace('/ControlPanel', '/control-panel'),
    description: 'ControlPanel 路径更新'
  }
];

/**
 * 检查文件是否存在
 */
function fileExists(filePath) {
  try {
    return fs.existsSync(filePath);
  } catch (error) {
    return false;
  }
}

/**
 * 安全地重命名文件
 */
function renameFile(fromPath, toPath, description) {
  const fullFromPath = path.resolve(fromPath);
  const fullToPath = path.resolve(toPath);
  
  if (!fileExists(fullFromPath)) {
    log.warning(`源文件不存在: ${fromPath}`);
    return false;
  }
  
  if (fileExists(fullToPath) && !CONFIG.force) {
    log.warning(`目标文件已存在: ${toPath} (使用 --force 强制覆盖)`);
    return false;
  }
  
  if (CONFIG.dryRun) {
    log.info(`[DRY RUN] 将重命名: ${fromPath} → ${toPath}`);
    return true;
  }
  
  try {
    // 确保目标目录存在
    const targetDir = path.dirname(fullToPath);
    if (!fs.existsSync(targetDir)) {
      fs.mkdirSync(targetDir, { recursive: true });
    }
    
    fs.renameSync(fullFromPath, fullToPath);
    log.success(`重命名完成: ${fromPath} → ${toPath}`);
    log.verbose(`描述: ${description}`);
    return true;
  } catch (error) {
    log.error(`重命名失败: ${error.message}`);
    return false;
  }
}

/**
 * 安全地重命名目录
 */
function renameDirectory(fromPath, toPath, description) {
  const fullFromPath = path.resolve(fromPath);
  const fullToPath = path.resolve(toPath);
  
  if (!fs.existsSync(fullFromPath)) {
    log.warning(`源目录不存在: ${fromPath}`);
    return false;
  }
  
  if (fs.existsSync(fullToPath) && !CONFIG.force) {
    log.warning(`目标目录已存在: ${toPath} (使用 --force 强制覆盖)`);
    return false;
  }
  
  if (CONFIG.dryRun) {
    log.info(`[DRY RUN] 将重命名目录: ${fromPath} → ${toPath}`);
    return true;
  }
  
  try {
    fs.renameSync(fullFromPath, fullToPath);
    log.success(`目录重命名完成: ${fromPath} → ${toPath}`);
    log.verbose(`描述: ${description}`);
    return true;
  } catch (error) {
    log.error(`目录重命名失败: ${error.message}`);
    return false;
  }
}

/**
 * 更新文件中的导入语句
 */
function updateImportsInFile(filePath) {
  if (!fileExists(filePath)) {
    return false;
  }
  
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let hasChanges = false;
    
    IMPORT_UPDATES.forEach(({ pattern, replacement, description }) => {
      const originalContent = content;
      content = content.replace(pattern, replacement);
      
      if (content !== originalContent) {
        hasChanges = true;
        log.verbose(`在 ${filePath} 中应用: ${description}`);
      }
    });
    
    if (hasChanges) {
      if (CONFIG.dryRun) {
        log.info(`[DRY RUN] 将更新导入: ${filePath}`);
      } else {
        fs.writeFileSync(filePath, content, 'utf8');
        log.success(`导入更新完成: ${filePath}`);
      }
      return true;
    }
    
    return false;
  } catch (error) {
    log.error(`更新导入失败 ${filePath}: ${error.message}`);
    return false;
  }
}

/**
 * 递归查找并更新所有 TypeScript/JavaScript 文件
 */
function updateAllImports(directory = '.') {
  const extensions = ['.ts', '.tsx', '.js', '.jsx'];
  let updatedFiles = 0;
  
  function processDirectory(dir) {
    try {
      const items = fs.readdirSync(dir);
      
      items.forEach(item => {
        const itemPath = path.join(dir, item);
        const stat = fs.statSync(itemPath);
        
        if (stat.isDirectory() && !item.startsWith('.') && item !== 'node_modules') {
          processDirectory(itemPath);
        } else if (stat.isFile() && extensions.some(ext => item.endsWith(ext))) {
          if (updateImportsInFile(itemPath)) {
            updatedFiles++;
          }
        }
      });
    } catch (error) {
      log.verbose(`跳过目录 ${dir}: ${error.message}`);
    }
  }
  
  processDirectory(directory);
  return updatedFiles;
}

/**
 * 主执行函数
 */
function main() {
  log.info('🚀 开始命名规范修正...');
  
  if (CONFIG.dryRun) {
    log.info('🔍 运行模式: 预览模式 (不会实际修改文件)');
  }
  
  let totalChanges = 0;
  
  // 第一阶段：重命名文件
  log.info('\n📁 第一阶段：重命名文件');
  FILE_RENAMES.forEach(({ from, to, description }) => {
    if (renameFile(from, to, description)) {
      totalChanges++;
    }
  });
  
  // 第二阶段：重命名目录
  log.info('\n📂 第二阶段：重命名目录');
  DIRECTORY_RENAMES.forEach(({ from, to, description }) => {
    if (renameDirectory(from, to, description)) {
      totalChanges++;
    }
  });
  
  // 第三阶段：更新导入语句
  log.info('\n🔄 第三阶段：更新导入语句');
  const updatedImports = updateAllImports();
  totalChanges += updatedImports;
  
  // 总结
  log.info('\n📊 修正完成总结:');
  log.info(`总计修改: ${totalChanges} 项`);
  
  if (CONFIG.dryRun) {
    log.info('\n💡 这是预览模式，没有实际修改文件');
    log.info('   要执行实际修改，请运行: node scripts/fix-naming-conventions.js');
  } else {
    log.success('\n🎉 命名规范修正完成！');
    log.info('   建议运行测试确保所有功能正常');
  }
}

// 显示帮助信息
if (process.argv.includes('--help') || process.argv.includes('-h')) {
  console.log(`
命名规范修正脚本

用法:
  node scripts/fix-naming-conventions.js [选项]

选项:
  --dry-run    预览模式，不实际修改文件
  --verbose    显示详细日志
  --force      强制覆盖已存在的文件
  --help, -h   显示此帮助信息

示例:
  node scripts/fix-naming-conventions.js --dry-run    # 预览修改
  node scripts/fix-naming-conventions.js --verbose    # 详细模式
  node scripts/fix-naming-conventions.js --force      # 强制模式
`);
  process.exit(0);
}

// 执行主函数
try {
  main();
} catch (error) {
  log.error(`脚本执行失败: ${error.message}`);
  process.exit(1);
}

#!/usr/bin/env node
/**
 * 依赖清理脚本
 * 🎯 核心价值：清理未使用的依赖，优化项目结构
 * 🔧 功能：检测未使用的依赖、清理过时配置、优化package.json
 * ⚡ 特性：安全清理、依赖分析、性能优化
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

class DependencyCleanup {
  constructor() {
    this.packageJsonPath = path.join(process.cwd(), 'package.json');
    this.packageJson = JSON.parse(fs.readFileSync(this.packageJsonPath, 'utf8'));
    this.unusedDeps = [];
    this.cleanupActions = [];
  }

  /**
   * 检查依赖是否在代码中被使用
   */
  isDependencyUsed(depName) {
    const searchPatterns = [
      `import.*from.*['"]${depName}['"]`,
      `import.*['"]${depName}['"]`,
      `require\\(['"]${depName}['"]\\)`,
      `from.*['"]${depName}['"]`,
    ];

    const extensions = ['.ts', '.tsx', '.js', '.jsx'];
    const excludeDirs = ['node_modules', '.next', 'dist', 'build'];

    try {
      for (const pattern of searchPatterns) {
        const grepCommand = `grep -r "${pattern}" . --include="*.ts" --include="*.tsx" --include="*.js" --include="*.jsx" --exclude-dir=node_modules --exclude-dir=.next --exclude-dir=dist --exclude-dir=build`;
        
        try {
          execSync(grepCommand, { stdio: 'pipe' });
          return true; // 找到使用
        } catch (error) {
          // grep 没找到匹配，继续下一个模式
        }
      }
      return false;
    } catch (error) {
      console.warn(`检查依赖 ${depName} 时出错:`, error.message);
      return true; // 出错时保守处理，认为被使用
    }
  }

  /**
   * 分析未使用的依赖
   */
  analyzeUnusedDependencies() {
    console.log('🔍 分析未使用的依赖...\n');

    const allDeps = {
      ...this.packageJson.dependencies,
      ...this.packageJson.devDependencies
    };

    // 跳过检查的依赖（通常是工具或运行时需要的）
    const skipCheck = [
      'next',
      'react',
      'react-dom',
      'typescript',
      'eslint',
      'prettier',
      'tailwindcss',
      'postcss',
      '@types/node',
      '@types/react',
      '@types/react-dom',
      'prisma',
      '@prisma/client',
    ];

    for (const [depName, version] of Object.entries(allDeps)) {
      if (skipCheck.includes(depName)) {
        console.log(`⏭️  跳过检查: ${depName} (核心依赖)`);
        continue;
      }

      if (!this.isDependencyUsed(depName)) {
        this.unusedDeps.push({ name: depName, version });
        console.log(`❌ 未使用: ${depName}@${version}`);
      } else {
        console.log(`✅ 使用中: ${depName}`);
      }
    }
  }

  /**
   * 清理package.json脚本
   */
  cleanupScripts() {
    console.log('\n🧹 清理package.json脚本...\n');

    const scripts = this.packageJson.scripts || {};
    const unusedScripts = [];

    // 检查可能未使用的脚本
    const potentiallyUnused = [
      'quality:check',
      'quality:report',
    ];

    potentiallyUnused.forEach(scriptName => {
      if (scripts[scriptName]) {
        // 检查脚本文件是否存在
        const scriptPath = scripts[scriptName].split(' ')[1]; // 获取脚本路径
        if (scriptPath && scriptPath.startsWith('scripts/')) {
          const fullPath = path.join(process.cwd(), scriptPath);
          if (!fs.existsSync(fullPath)) {
            unusedScripts.push(scriptName);
            console.log(`❌ 脚本文件不存在: ${scriptName} -> ${scriptPath}`);
          }
        }
      }
    });

    if (unusedScripts.length > 0) {
      this.cleanupActions.push({
        type: 'remove_scripts',
        scripts: unusedScripts
      });
    }
  }

  /**
   * 优化依赖版本
   */
  optimizeDependencyVersions() {
    console.log('\n📦 检查依赖版本...\n');

    // 检查是否有重复的类型定义
    const deps = this.packageJson.dependencies || {};
    const devDeps = this.packageJson.devDependencies || {};

    // 检查类型定义重复
    Object.keys(deps).forEach(depName => {
      const typesName = `@types/${depName}`;
      if (devDeps[typesName]) {
        console.log(`⚠️  发现类型定义重复: ${depName} 和 ${typesName}`);
      }
    });

    // 检查版本一致性
    const duplicates = [];
    Object.keys(deps).forEach(depName => {
      if (devDeps[depName] && deps[depName] !== devDeps[depName]) {
        duplicates.push({
          name: depName,
          prodVersion: deps[depName],
          devVersion: devDeps[depName]
        });
      }
    });

    if (duplicates.length > 0) {
      console.log('\n⚠️  发现版本不一致的依赖:');
      duplicates.forEach(dup => {
        console.log(`  ${dup.name}: 生产环境 ${dup.prodVersion} vs 开发环境 ${dup.devVersion}`);
      });
    }
  }

  /**
   * 生成清理建议
   */
  generateCleanupSuggestions() {
    console.log('\n💡 清理建议:\n');

    if (this.unusedDeps.length > 0) {
      console.log('📦 可以移除的依赖:');
      this.unusedDeps.forEach(dep => {
        console.log(`  pnpm remove ${dep.name}`);
      });
      console.log();
    }

    // 建议的优化
    const suggestions = [
      '🔧 运行 pnpm audit 检查安全漏洞',
      '📊 运行 pnpm outdated 检查过时依赖',
      '🧹 定期清理 node_modules 和 .next 目录',
      '📝 更新 README.md 中的依赖说明',
      '🔒 考虑使用 pnpm-lock.yaml 锁定版本',
    ];

    console.log('🎯 其他优化建议:');
    suggestions.forEach(suggestion => {
      console.log(`  ${suggestion}`);
    });
  }

  /**
   * 执行清理操作
   */
  executeCleanup(dryRun = true) {
    if (dryRun) {
      console.log('\n🔍 预览模式 - 不会实际修改文件\n');
      return;
    }

    console.log('\n🚀 执行清理操作...\n');

    // 移除未使用的依赖
    if (this.unusedDeps.length > 0) {
      const depNames = this.unusedDeps.map(dep => dep.name).join(' ');
      try {
        console.log(`移除依赖: ${depNames}`);
        execSync(`pnpm remove ${depNames}`, { stdio: 'inherit' });
      } catch (error) {
        console.error('移除依赖时出错:', error.message);
      }
    }

    // 执行其他清理操作
    this.cleanupActions.forEach(action => {
      switch (action.type) {
        case 'remove_scripts':
          action.scripts.forEach(scriptName => {
            delete this.packageJson.scripts[scriptName];
            console.log(`移除脚本: ${scriptName}`);
          });
          break;
      }
    });

    // 保存修改后的package.json
    if (this.cleanupActions.length > 0) {
      fs.writeFileSync(
        this.packageJsonPath,
        JSON.stringify(this.packageJson, null, 2) + '\n',
        'utf8'
      );
      console.log('✅ package.json 已更新');
    }
  }

  /**
   * 运行清理
   */
  run(options = {}) {
    const { dryRun = true } = options;

    console.log('🧹 开始依赖清理分析...\n');

    this.analyzeUnusedDependencies();
    this.cleanupScripts();
    this.optimizeDependencyVersions();
    this.generateCleanupSuggestions();
    this.executeCleanup(dryRun);

    console.log('\n✨ 清理分析完成！');
    
    if (dryRun) {
      console.log('\n💡 要执行实际清理，请运行: node scripts/cleanup-dependencies.js --execute');
    }
  }
}

// 运行清理
if (require.main === module) {
  const dryRun = !process.argv.includes('--execute');
  const cleanup = new DependencyCleanup();
  cleanup.run({ dryRun });
}

module.exports = DependencyCleanup;

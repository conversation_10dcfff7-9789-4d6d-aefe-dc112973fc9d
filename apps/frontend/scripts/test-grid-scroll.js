/**
 * 测试GridMatrix滚动功能的脚本
 * 
 * 这个脚本用于验证GridMatrix在缩小时是否能够正确显示滚动条
 * 并且用户可以通过滚动来查看矩阵中的每一个格子
 */

// 等待页面加载完成
function waitForElement(selector, timeout = 5000) {
    return new Promise((resolve, reject) => {
        const startTime = Date.now();
        
        function check() {
            const element = document.querySelector(selector);
            if (element) {
                resolve(element);
            } else if (Date.now() - startTime > timeout) {
                reject(new Error(`Element ${selector} not found within ${timeout}ms`));
            } else {
                setTimeout(check, 100);
            }
        }
        
        check();
    });
}

// 测试滚动功能
async function testGridScroll() {
    try {
        console.log('🧪 开始测试GridMatrix滚动功能...');
        
        // 等待GridMatrix容器加载
        const scrollContainer = await waitForElement('.overflow-auto.scrollbar-thin');
        console.log('✅ 找到滚动容器:', scrollContainer);
        
        // 等待GridMatrix组件加载
        const gridMatrix = await waitForElement('.grid-matrix');
        console.log('✅ 找到GridMatrix组件:', gridMatrix);
        
        // 检查容器尺寸
        const containerRect = scrollContainer.getBoundingClientRect();
        const gridRect = gridMatrix.getBoundingClientRect();
        
        console.log('📏 容器尺寸:', {
            width: containerRect.width,
            height: containerRect.height
        });
        
        console.log('📏 网格尺寸:', {
            width: gridRect.width,
            height: gridRect.height
        });
        
        // 检查是否需要滚动
        const needsHorizontalScroll = gridRect.width > containerRect.width;
        const needsVerticalScroll = gridRect.height > containerRect.height;
        
        console.log('🔍 滚动需求:', {
            horizontal: needsHorizontalScroll,
            vertical: needsVerticalScroll
        });
        
        // 检查滚动条是否存在
        const hasHorizontalScrollbar = scrollContainer.scrollWidth > scrollContainer.clientWidth;
        const hasVerticalScrollbar = scrollContainer.scrollHeight > scrollContainer.clientHeight;
        
        console.log('📜 滚动条状态:', {
            horizontal: hasHorizontalScrollbar,
            vertical: hasVerticalScrollbar
        });
        
        // 测试滚动功能
        if (hasHorizontalScrollbar || hasVerticalScrollbar) {
            console.log('🎯 开始测试滚动功能...');
            
            // 记录初始滚动位置
            const initialScrollLeft = scrollContainer.scrollLeft;
            const initialScrollTop = scrollContainer.scrollTop;
            
            console.log('📍 初始滚动位置:', {
                left: initialScrollLeft,
                top: initialScrollTop
            });
            
            // 测试水平滚动
            if (hasHorizontalScrollbar) {
                scrollContainer.scrollLeft = 100;
                await new Promise(resolve => setTimeout(resolve, 100));
                console.log('➡️ 水平滚动测试:', scrollContainer.scrollLeft === 100 ? '✅ 成功' : '❌ 失败');
            }
            
            // 测试垂直滚动
            if (hasVerticalScrollbar) {
                scrollContainer.scrollTop = 100;
                await new Promise(resolve => setTimeout(resolve, 100));
                console.log('⬇️ 垂直滚动测试:', scrollContainer.scrollTop === 100 ? '✅ 成功' : '❌ 失败');
            }
            
            // 恢复初始位置
            scrollContainer.scrollLeft = initialScrollLeft;
            scrollContainer.scrollTop = initialScrollTop;
            
            console.log('🎉 滚动功能测试完成！');
        } else {
            console.log('ℹ️ 当前网格尺寸不需要滚动');
        }
        
        // 检查网格单元格数量
        const cells = gridMatrix.querySelectorAll('[role="gridcell"], .grid-cell, [data-x]');
        console.log('🔢 网格单元格数量:', cells.length);
        
        // 检查是否所有单元格都可见（通过滚动）
        if (cells.length > 0) {
            const firstCell = cells[0];
            const lastCell = cells[cells.length - 1];
            
            console.log('🎯 第一个单元格位置:', firstCell.getBoundingClientRect());
            console.log('🎯 最后一个单元格位置:', lastCell.getBoundingClientRect());
        }
        
        return {
            success: true,
            containerSize: { width: containerRect.width, height: containerRect.height },
            gridSize: { width: gridRect.width, height: gridRect.height },
            scrollbars: { horizontal: hasHorizontalScrollbar, vertical: hasVerticalScrollbar },
            cellCount: cells.length
        };
        
    } catch (error) {
        console.error('❌ 测试失败:', error);
        return { success: false, error: error.message };
    }
}

// 如果在浏览器环境中运行，自动执行测试
if (typeof window !== 'undefined') {
    // 等待DOM加载完成
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', testGridScroll);
    } else {
        // 延迟执行，确保React组件已渲染
        setTimeout(testGridScroll, 1000);
    }
}

// 导出测试函数供手动调用
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { testGridScroll };
}

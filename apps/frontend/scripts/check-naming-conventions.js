#!/usr/bin/env node

/**
 * 命名规范检查脚本
 * 🎯 核心价值：自动检查项目文件命名是否符合规范
 * 📦 功能：文件名验证、目录名验证、生成检查报告
 * 🔧 用途：可用于 Git hooks、CI/CD 流程、开发时检查
 */

const fs = require('fs');
const path = require('path');

// 配置
const CONFIG = {
  verbose: process.argv.includes('--verbose'),
  fix: process.argv.includes('--fix'),
  exitOnError: !process.argv.includes('--no-exit'),
};

// 日志工具
const log = {
  info: (msg) => console.log(`ℹ️  ${msg}`),
  success: (msg) => console.log(`✅ ${msg}`),
  warning: (msg) => console.log(`⚠️  ${msg}`),
  error: (msg) => console.log(`❌ ${msg}`),
  verbose: (msg) => CONFIG.verbose && console.log(`🔍 ${msg}`),
};

// 命名规范规则
const NAMING_RULES = {
  // React 组件文件
  components: {
    pattern: /^[A-Z][a-zA-Z0-9]*\.tsx?$/,
    description: 'React组件应使用PascalCase命名 (例: GridContainer.tsx)',
    paths: ['**/components/**/*.tsx', '**/components/**/*.ts'],
    exclude: ['**/components/**/index.ts', '**/components/**/types.ts']
  },
  
  // 钩子文件
  hooks: {
    pattern: /^use[A-Z][a-zA-Z0-9]*\.ts$/,
    description: '钩子文件应以use开头并使用camelCase (例: useGridState.ts)',
    paths: ['**/hooks/**/*.ts'],
    exclude: ['**/hooks/**/index.ts', '**/hooks/**/types.ts']
  },
  
  // 存储文件
  stores: {
    pattern: /^[a-z][a-zA-Z0-9]*Store\.ts$/,
    description: '存储文件应使用camelCase并以Store结尾 (例: gridConfigStore.ts)',
    paths: ['**/stores/**/*.ts'],
    exclude: ['**/stores/**/index.ts', '**/stores/**/types.ts']
  },
  
  // 服务文件
  services: {
    pattern: /^[A-Z][a-zA-Z0-9]*Service\.ts$/,
    description: '服务文件应使用PascalCase并以Service结尾 (例: ColorMappingService.ts)',
    paths: ['**/services/**/*.ts'],
    exclude: ['**/services/**/index.ts', '**/services/**/types.ts']
  },
  
  // 工具函数文件
  utils: {
    pattern: /^[a-z][a-zA-Z0-9]*\.ts$/,
    description: '工具函数文件应使用camelCase (例: matrixUtils.ts)',
    paths: ['**/utils/**/*.ts', '**/lib/**/*.ts'],
    exclude: ['**/utils/**/index.ts', '**/lib/**/index.ts', '**/lib/**/types.ts']
  },
  
  // 类型定义文件
  types: {
    pattern: /^[a-z][a-zA-Z0-9]*\.ts$|^types\.ts$|^index\.ts$/,
    description: '类型文件应使用camelCase或特殊名称 (例: gridTypes.ts, types.ts)',
    paths: ['**/types/**/*.ts', '**/*types.ts'],
    exclude: []
  }
};

// 目录命名规范
const DIRECTORY_RULES = {
  // 功能目录 (kebab-case)
  features: {
    pattern: /^[a-z]+(-[a-z]+)*$/,
    description: '功能目录应使用kebab-case (例: grid-system, style-management)',
    paths: ['**/components/*', '**/features/*'],
    exclude: ['ui', 'shared', 'common']
  },
  
  // 技术目录 (camelCase)
  technical: {
    pattern: /^[a-z][a-zA-Z0-9]*$/,
    description: '技术目录应使用camelCase (例: components, hooks, stores)',
    paths: ['lib', 'hooks', 'stores', 'services', 'utils', 'types'],
    exclude: []
  }
};

/**
 * 检查文件名是否符合规范
 */
function checkFileName(filePath, rule) {
  const fileName = path.basename(filePath);
  const isValid = rule.pattern.test(fileName);
  
  return {
    isValid,
    fileName,
    filePath,
    rule: rule.description,
    suggestion: isValid ? null : generateFileNameSuggestion(fileName, rule)
  };
}

/**
 * 生成文件名修正建议
 */
function generateFileNameSuggestion(fileName, rule) {
  const baseName = fileName.replace(/\.(ts|tsx|js|jsx)$/, '');
  const extension = fileName.match(/\.(ts|tsx|js|jsx)$/)?.[0] || '.ts';
  
  // 根据规则类型生成建议
  if (rule === NAMING_RULES.hooks) {
    if (!baseName.startsWith('use')) {
      return `use${baseName.charAt(0).toUpperCase()}${baseName.slice(1)}${extension}`;
    }
  } else if (rule === NAMING_RULES.components) {
    return `${baseName.charAt(0).toUpperCase()}${baseName.slice(1)}${extension}`;
  } else if (rule === NAMING_RULES.stores) {
    if (!baseName.endsWith('Store')) {
      return `${baseName}Store${extension}`;
    }
  } else if (rule === NAMING_RULES.services) {
    if (!baseName.endsWith('Service')) {
      return `${baseName.charAt(0).toUpperCase()}${baseName.slice(1)}Service${extension}`;
    }
  }
  
  return null;
}

/**
 * 检查目录名是否符合规范
 */
function checkDirectoryName(dirPath, rule) {
  const dirName = path.basename(dirPath);
  const isValid = rule.pattern.test(dirName) || rule.exclude.includes(dirName);
  
  return {
    isValid,
    dirName,
    dirPath,
    rule: rule.description,
    suggestion: isValid ? null : generateDirectoryNameSuggestion(dirName, rule)
  };
}

/**
 * 生成目录名修正建议
 */
function generateDirectoryNameSuggestion(dirName, rule) {
  if (rule === DIRECTORY_RULES.features) {
    // 转换为 kebab-case
    return dirName
      .replace(/([A-Z])/g, '-$1')
      .toLowerCase()
      .replace(/^-/, '');
  } else if (rule === DIRECTORY_RULES.technical) {
    // 转换为 camelCase
    return dirName
      .split('-')
      .map((part, index) => 
        index === 0 ? part.toLowerCase() : 
        part.charAt(0).toUpperCase() + part.slice(1).toLowerCase()
      )
      .join('');
  }
  
  return null;
}

/**
 * 递归扫描目录并检查文件
 */
function scanDirectory(directory = '.') {
  const results = {
    files: [],
    directories: [],
    errors: 0,
    warnings: 0
  };
  
  function processDirectory(dir) {
    try {
      const items = fs.readdirSync(dir);
      
      items.forEach(item => {
        const itemPath = path.join(dir, item);
        const stat = fs.statSync(itemPath);
        
        if (stat.isDirectory()) {
          // 跳过特殊目录
          if (item.startsWith('.') || item === 'node_modules' || item === 'dist' || item === 'build') {
            return;
          }
          
          // 检查目录命名
          checkDirectoryNaming(itemPath, results);
          
          // 递归处理子目录
          processDirectory(itemPath);
        } else if (stat.isFile()) {
          // 检查文件命名
          checkFileNaming(itemPath, results);
        }
      });
    } catch (error) {
      log.verbose(`跳过目录 ${dir}: ${error.message}`);
    }
  }
  
  processDirectory(directory);
  return results;
}

/**
 * 检查文件命名
 */
function checkFileNaming(filePath, results) {
  const relativePath = path.relative('.', filePath);
  
  // 检查每个命名规则
  Object.entries(NAMING_RULES).forEach(([ruleName, rule]) => {
    const shouldCheck = rule.paths.some(pattern => {
      const regex = new RegExp(pattern.replace(/\*\*/g, '.*').replace(/\*/g, '[^/]*'));
      return regex.test(relativePath);
    });
    
    const isExcluded = rule.exclude.some(pattern => {
      const regex = new RegExp(pattern.replace(/\*\*/g, '.*').replace(/\*/g, '[^/]*'));
      return regex.test(relativePath);
    });
    
    if (shouldCheck && !isExcluded) {
      const result = checkFileName(filePath, rule);
      result.ruleName = ruleName;
      results.files.push(result);
      
      if (!result.isValid) {
        results.errors++;
      }
    }
  });
}

/**
 * 检查目录命名
 */
function checkDirectoryNaming(dirPath, results) {
  const relativePath = path.relative('.', dirPath);
  
  // 检查功能目录
  if (relativePath.includes('components/') || relativePath.includes('features/')) {
    const result = checkDirectoryName(dirPath, DIRECTORY_RULES.features);
    result.ruleName = 'features';
    results.directories.push(result);
    
    if (!result.isValid) {
      results.warnings++;
    }
  }
}

/**
 * 生成检查报告
 */
function generateReport(results) {
  log.info('\n📊 命名规范检查报告');
  log.info('='.repeat(50));
  
  // 统计信息
  const totalFiles = results.files.length;
  const validFiles = results.files.filter(f => f.isValid).length;
  const totalDirs = results.directories.length;
  const validDirs = results.directories.filter(d => d.isValid).length;
  
  log.info(`\n📈 统计信息:`);
  log.info(`  文件检查: ${validFiles}/${totalFiles} 通过`);
  log.info(`  目录检查: ${validDirs}/${totalDirs} 通过`);
  log.info(`  错误数量: ${results.errors}`);
  log.info(`  警告数量: ${results.warnings}`);
  
  // 文件问题
  const invalidFiles = results.files.filter(f => !f.isValid);
  if (invalidFiles.length > 0) {
    log.error(`\n❌ 文件命名问题 (${invalidFiles.length}个):`);
    invalidFiles.forEach(file => {
      log.error(`  ${file.filePath}`);
      log.info(`     规则: ${file.rule}`);
      if (file.suggestion) {
        log.info(`     建议: ${file.suggestion}`);
      }
    });
  }
  
  // 目录问题
  const invalidDirs = results.directories.filter(d => !d.isValid);
  if (invalidDirs.length > 0) {
    log.warning(`\n⚠️  目录命名问题 (${invalidDirs.length}个):`);
    invalidDirs.forEach(dir => {
      log.warning(`  ${dir.dirPath}`);
      log.info(`     规则: ${dir.rule}`);
      if (dir.suggestion) {
        log.info(`     建议: ${dir.suggestion}`);
      }
    });
  }
  
  // 成功信息
  if (results.errors === 0 && results.warnings === 0) {
    log.success('\n🎉 所有文件和目录命名都符合规范！');
  }
  
  return results.errors === 0;
}

/**
 * 主执行函数
 */
function main() {
  log.info('🔍 开始检查命名规范...');
  
  const results = scanDirectory('.');
  const isValid = generateReport(results);
  
  if (CONFIG.fix && (results.errors > 0 || results.warnings > 0)) {
    log.info('\n🔧 要修复这些问题，请运行:');
    log.info('   node scripts/fix-naming-conventions.js --dry-run  # 预览修改');
    log.info('   node scripts/fix-naming-conventions.js           # 执行修改');
  }
  
  if (!isValid && CONFIG.exitOnError) {
    process.exit(1);
  }
  
  return isValid;
}

// 显示帮助信息
if (process.argv.includes('--help') || process.argv.includes('-h')) {
  console.log(`
命名规范检查脚本

用法:
  node scripts/check-naming-conventions.js [选项]

选项:
  --verbose     显示详细日志
  --fix         显示修复建议
  --no-exit     检查失败时不退出进程
  --help, -h    显示此帮助信息

示例:
  node scripts/check-naming-conventions.js              # 基本检查
  node scripts/check-naming-conventions.js --verbose    # 详细模式
  node scripts/check-naming-conventions.js --fix        # 显示修复建议
`);
  process.exit(0);
}

// 执行主函数
if (require.main === module) {
  try {
    main();
  } catch (error) {
    log.error(`脚本执行失败: ${error.message}`);
    process.exit(1);
  }
}

module.exports = { main, checkFileName, checkDirectoryName };

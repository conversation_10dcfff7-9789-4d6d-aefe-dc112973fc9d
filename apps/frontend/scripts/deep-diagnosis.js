/**
 * 深度诊断脚本 - 分析网格显示问题的所有可能原因
 */

console.log('=== 深度诊断：网格显示问题分析 ===');

// 模拟数据结构检查
const GROUP_A_DATA = {
  red: {
    1: [[8, 0]],
    2: [[4, 0]],
    3: [[2, 0], [6, 0], [4, 2], [4, -2]],
    4: [[1, 0], [3, 0], [5, 0], [7, 0], [2, 1], [2, -1], [3, 2], [3, -2], [4, 1], [4, 3], [4, -1], [4, -3], [6, 1], [6, -1], [5, 2], [5, -2]]
  },
  orange: {
    1: [[-4, 4]],
    3: [[6, 2], [2, -2], [-2, -6]],
    4: [[-3, -5], [-1, -7], [-1, -5], [1, -1], [1, -3], [3, -3], [3, -1], [5, 3], [7, 1], [5, 1]]
  },
  yellow: {
    1: [[0, -8]],
    2: [[0, -4]],
    3: [[0, -2], [0, -6], [2, -4], [-2, -4]],
    4: [[0, -1], [-1, -2], [0, -3], [1, -2], [-2, -3], [-3, -4], [-2, -5], [-1, -4], [2, -3], [1, -4], [2, -5], [3, -4], [0, -5], [-1, -6], [0, -7], [1, -6]]
  },
  green: {
    1: [[-4, -4]],
    3: [[-6, 2], [-2, -2], [2, -6]],
    4: [[-5, 3], [-7, 1], [-5, 1], [-1, -1], [-3, -1], [-3, -3], [-1, -3], [3, -5], [1, -5], [1, -7]]
  },
  cyan: {
    1: [[-8, 0]],
    2: [[-4, 0]],
    3: [[-2, 0], [-6, 0], [-4, 2], [-4, -2]],
    4: [[-7, 0], [-5, 0], [-3, 0], [-1, 0], [-6, 1], [-6, -1], [-5, 2], [-5, -2], [-4, 1], [-4, 3], [-4, -1], [-4, -3], [-2, 1], [-2, -1], [-3, 2], [-3, -2]]
  },
  blue: {
    1: [[-4, 4]],
    3: [[2, 6], [-2, 2], [-6, -2]],
    4: [[1, 7], [3, 5], [1, 5], [-3, 3], [-1, 3], [-1, 1], [-3, 1], [-7, -1], [-5, -1], [-5, -3]]
  },
  purple: {
    1: [[0, 8]],
    2: [[0, 4]],
    3: [[0, 2], [0, 6], [2, 4], [-2, 4]],
    4: [[0, 1], [0, 3], [0, 5], [0, 7], [1, 2], [1, 6], [-1, 2], [-1, 6], [2, 3], [2, 5], [-2, 3], [-2, 5]]
  },
  pink: {
    1: [[4, 4]],
    3: [[-2, 6], [2, 2], [6, -2]],
    4: [[-1, 7], [-1, 5], [-3, 5], [3, 3], [3, 1], [1, 1], [1, 3], [7, -1], [5, -3], [5, -1]]
  },
  black: {
    1: [[0, 0]]
  }
};

const DEFAULT_COLOR_VALUES = {
  black: { name: '黑色', hex: '#000000', rgb: [0, 0, 0], hsl: [0, 0, 0] },
  red: { name: '红色', hex: '#ef4444', rgb: [239, 68, 68], hsl: [0, 84, 60], mappingValue: 1 },
  cyan: { name: '青色', hex: '#06b6d4', rgb: [6, 182, 212], hsl: [189, 94, 43], mappingValue: 5 },
  yellow: { name: '黄色', hex: '#eab308', rgb: [234, 179, 8], hsl: [45, 93, 47], mappingValue: 3 },
  purple: { name: '紫色', hex: '#a855f7', rgb: [168, 85, 247], hsl: [271, 91, 65], mappingValue: 7 },
  orange: { name: '橙色', hex: '#f97316', rgb: [249, 115, 22], hsl: [25, 95, 53], mappingValue: 2 },
  green: { name: '绿色', hex: '#22c55e', rgb: [34, 197, 94], hsl: [142, 71, 45], mappingValue: 4 },
  blue: { name: '蓝色', hex: '#3b82f6', rgb: [59, 130, 246], hsl: [217, 91, 60], mappingValue: 6 },
  pink: { name: '粉色', hex: '#ec4899', rgb: [236, 72, 153], hsl: [327, 82, 60], mappingValue: 8 },
};

console.log('\n🔍 问题分析维度:');

console.log('\n1. 数据源完整性检查:');
Object.keys(GROUP_A_DATA).forEach(color => {
  const colorData = GROUP_A_DATA[color];
  const colorValue = DEFAULT_COLOR_VALUES[color];
  console.log(`  ${color}:`);
  console.log(`    - 可用级别: ${Object.keys(colorData).join(', ')}`);
  console.log(`    - 颜色值: ${colorValue?.hex || '未定义'}`);
  console.log(`    - 映射值: ${colorValue?.mappingValue || '无'}`);
  
  Object.keys(colorData).forEach(level => {
    const coords = colorData[level];
    console.log(`    - 级别${level}: ${coords.length}个坐标`);
  });
});

console.log('\n2. 可能的问题原因分析:');

console.log('\n🚨 原因1: 颜色可见性配置问题');
console.log('  - 文件: apps/frontend/lib/utils/matrixHelpers.ts');
console.log('  - 问题: colorVisibility[color].showCells 可能为false');
console.log('  - 检查: generateDefaultColorVisibility()函数');
console.log('  - 影响: 如果showCells为false，颜色不会显示');

console.log('\n🚨 原因2: 组可见性配置问题');
console.log('  - 文件: apps/frontend/lib/utils/matrixHelpers.ts');
console.log('  - 问题: groupVisibility[group] 可能为false');
console.log('  - 检查: generateDefaultGroupVisibility()函数');
console.log('  - 影响: 如果组不可见，该组的所有颜色都不显示');

console.log('\n🚨 原因3: 级别可见性配置问题');
console.log('  - 文件: useGridData.ts 第42行');
console.log('  - 问题: colorVisibility[color][showLevel${level}] 可能为false');
console.log('  - 检查: levelVisibilityKey的计算和值');
console.log('  - 影响: 特定级别的颜色不显示');

console.log('\n🚨 原因4: 数据生成问题');
console.log('  - 文件: apps/frontend/lib/utils/matrixUtils.ts');
console.log('  - 问题: generateMatrixData()可能没有正确生成数据');
console.log('  - 检查: matrixData.byCoordinate是否包含所有颜色的坐标');
console.log('  - 影响: 数据源为空导致无法显示');

console.log('\n🚨 原因5: 坐标映射问题');
console.log('  - 文件: useGridData.ts coordinateMap生成');
console.log('  - 问题: coordinateMap可能没有正确映射颜色坐标');
console.log('  - 检查: map.set()是否正确执行');
console.log('  - 影响: isCellActive返回false，getCellColor返回null');

console.log('\n🚨 原因6: 组偏移计算问题');
console.log('  - 文件: apps/frontend/lib/utils/matrixUtils.ts calculateGroupCoordinates');
console.log('  - 问题: GROUP_OFFSET_CONFIGS可能配置错误');
console.log('  - 检查: 偏移计算是否正确应用');
console.log('  - 影响: 坐标计算错误导致颜色显示在错误位置或不显示');

console.log('\n🚨 原因7: GridCell渲染逻辑问题');
console.log('  - 文件: apps/frontend/components/grid-system/GridCell.tsx');
console.log('  - 问题: backgroundColor计算逻辑可能有误');
console.log('  - 检查: isActive判断和cell.color使用');
console.log('  - 影响: 即使数据正确，渲染时也不显示颜色');

console.log('\n🚨 原因8: 状态更新时序问题');
console.log('  - 文件: 多个组件的useEffect和useMemo依赖');
console.log('  - 问题: 状态更新顺序导致数据不一致');
console.log('  - 检查: 依赖数组和更新时机');
console.log('  - 影响: 颜色先显示后消失');

console.log('\n🚨 原因9: 存储持久化问题');
console.log('  - 文件: apps/frontend/stores/basicDataStore.ts');
console.log('  - 问题: Zustand persist可能导致状态不同步');
console.log('  - 检查: 存储的初始化和恢复');
console.log('  - 影响: 页面刷新后配置丢失或错误');

console.log('\n🚨 原因10: 类型不匹配问题');
console.log('  - 文件: 类型定义文件');
console.log('  - 问题: BasicColorType和实际字符串不匹配');
console.log('  - 检查: 类型转换和字符串比较');
console.log('  - 影响: 条件判断失败导致逻辑错误');

console.log('\n📋 建议的调试步骤:');
console.log('1. 检查浏览器控制台的错误信息');
console.log('2. 在useGridData中添加console.log检查coordinateMap内容');
console.log('3. 在GridCell中添加console.log检查cell.color和isActive值');
console.log('4. 检查colorVisibility和groupVisibility的实际值');
console.log('5. 验证matrixData.byCoordinate是否包含预期的坐标');
console.log('6. 检查组偏移计算是否正确');
console.log('7. 验证颜色值映射是否正确');
console.log('8. 检查React DevTools中的组件状态');

console.log('\n=== 诊断完成 ===');

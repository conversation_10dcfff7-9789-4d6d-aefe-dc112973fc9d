/**
 * 调试网格数据生成脚本
 * 用于验证颜色数据和映射是否正确生成
 */

// 模拟导入（在Node.js环境中）
const GROUP_A_DATA = {
  black: {
    1: [[0, 0]]
  },
  red: {
    1: [[8, 0]],
    2: [[4, 0]],
    3: [[2, 0], [6, 0], [4, 2], [4, -2]],
    4: [[1, 0], [3, 0], [5, 0], [7, 0], [2, 1], [2, -1], [3, 2], [3, -2], [4, 1], [4, 3], [4, -1], [4, -3], [6, 1], [6, -1], [5, 2], [5, -2]]
  },
  orange: {
    1: [[-4, 4]],
    3: [[6, 2], [2, -2], [-2, -6]],
    4: [[-3, -5], [-1, -7], [-1, -5], [1, -1], [1, -3], [3, -3], [3, -1], [5, 3], [7, 1], [5, 1]]
  },
  yellow: {
    1: [[0, -8]],
    2: [[0, -4]],
    3: [[0, -2], [0, -6], [2, -4], [-2, -4]],
    4: [[0, -1], [-1, -2], [0, -3], [1, -2], [-2, -3], [-3, -4], [-2, -5], [-1, -4], [2, -3], [1, -4], [2, -5], [3, -4], [0, -5], [-1, -6], [0, -7], [1, -6]]
  },
  green: {
    1: [[-4, -4]],
    3: [[-6, 2], [-2, -2], [2, -6]],
    4: [[-5, 3], [-7, 1], [-5, 1], [-1, -1], [-3, -1], [-3, -3], [-1, -3], [3, -5], [1, -5], [1, -7]]
  },
  cyan: {
    1: [[-8, 0]],
    2: [[-4, 0]],
    3: [[-2, 0], [-6, 0], [-4, 2], [-4, -2]],
    4: [[-7, 0], [-5, 0], [-3, 0], [-1, 0], [-6, 1], [-6, -1], [-5, 2], [-5, -2], [-4, 1], [-4, 3], [-4, -1], [-4, -3], [-2, 1], [-2, -1], [-3, 2], [-3, -2]]
  },
  blue: {
    1: [[-4, 4]],
    3: [[2, 6], [-2, 2], [-6, -2]],
    4: [[1, 7], [3, 5], [1, 5], [-3, 3], [-1, 3], [-1, 1], [-3, 1], [-7, -1], [-5, -1], [-5, -3]]
  },
  purple: {
    1: [[0, 8]],
    2: [[0, 4]],
    3: [[0, 2], [0, 6], [2, 4], [-2, 4]],
    4: [[0, 1], [0, 3], [0, 5], [0, 7], [1, 2], [1, 6], [-1, 2], [-1, 6], [2, 3], [2, 5], [-2, 3], [-2, 5]]
  },
  pink: {
    1: [[4, 4]],
    3: [[-2, 6], [2, 2], [6, -2]],
    4: [[-1, 7], [-1, 5], [-3, 5], [3, 3], [3, 1], [1, 1], [1, 3], [7, -1], [5, -3], [5, -1]]
  }
};

const DEFAULT_COLOR_VALUES = {
  black: { name: '黑色', hex: '#000000', rgb: [0, 0, 0], hsl: [0, 0, 0] },
  red: { name: '红色', hex: '#ef4444', rgb: [239, 68, 68], hsl: [0, 84, 60], mappingValue: 1 },
  cyan: { name: '青色', hex: '#06b6d4', rgb: [6, 182, 212], hsl: [189, 94, 43], mappingValue: 5 },
  yellow: { name: '黄色', hex: '#eab308', rgb: [234, 179, 8], hsl: [45, 93, 47], mappingValue: 3 },
  purple: { name: '紫色', hex: '#a855f7', rgb: [168, 85, 247], hsl: [271, 91, 65], mappingValue: 7 },
  orange: { name: '橙色', hex: '#f97316', rgb: [249, 115, 22], hsl: [25, 95, 53], mappingValue: 2 },
  green: { name: '绿色', hex: '#22c55e', rgb: [34, 197, 94], hsl: [142, 71, 45], mappingValue: 4 },
  blue: { name: '蓝色', hex: '#3b82f6', rgb: [59, 130, 246], hsl: [217, 91, 60], mappingValue: 6 },
  pink: { name: '粉色', hex: '#ec4899', rgb: [236, 72, 153], hsl: [327, 82, 60], mappingValue: 8 },
};

// 调试函数
function debugColorData() {
  console.log('=== 调试颜色数据 ===');
  
  // 检查每种颜色的数据
  Object.keys(GROUP_A_DATA).forEach(color => {
    const colorData = GROUP_A_DATA[color];
    const colorValue = DEFAULT_COLOR_VALUES[color];
    
    console.log(`\n颜色: ${color}`);
    console.log(`  名称: ${colorValue?.name || '未定义'}`);
    console.log(`  十六进制: ${colorValue?.hex || '未定义'}`);
    console.log(`  映射值: ${colorValue?.mappingValue || '无'}`);
    console.log(`  可用级别: ${Object.keys(colorData).join(', ')}`);
    
    // 检查每个级别的坐标数量
    Object.keys(colorData).forEach(level => {
      const coords = colorData[level];
      console.log(`    级别${level}: ${coords.length}个坐标`);
    });
  });
}

// 检查映射值
function debugMappingValues() {
  console.log('\n=== 调试映射值 ===');
  
  Object.keys(DEFAULT_COLOR_VALUES).forEach(color => {
    const colorValue = DEFAULT_COLOR_VALUES[color];
    if (colorValue.mappingValue !== undefined) {
      console.log(`${color}: ${colorValue.mappingValue}`);
    } else {
      console.log(`${color}: 无映射值`);
    }
  });
}

// 运行调试
debugColorData();
debugMappingValues();

console.log('\n=== 调试完成 ===');

#!/usr/bin/env node

/**
 * 开发服务器启动脚本 - 支持自定义端口
 * 使用方法：
 * node scripts/dev-with-port.js [端口号]
 * 例如：node scripts/dev-with-port.js 3123
 */

const { spawn } = require('child_process');
const path = require('path');

// 获取端口参数，默认为4096
const port = process.argv[2] || '4096';

// 验证端口号
const portNum = parseInt(port, 10);
if (isNaN(portNum) || portNum < 1024 || portNum > 65535) {
  console.error('❌ 错误：端口号必须是1024-65535之间的数字');
  process.exit(1);
}

console.log(`🚀 启动开发服务器，端口：${port}`);
console.log(`📍 本地地址：http://localhost:${port}`);
console.log(`🌐 网络地址：http://0.0.0.0:${port}`);
console.log('');

// 启动Next.js开发服务器
const nextProcess = spawn('npx', ['next', 'dev', '-H', '0.0.0.0', '-p', port], {
  stdio: 'inherit',
  cwd: process.cwd()
});

// 处理进程退出
nextProcess.on('close', (code) => {
  if (code !== 0) {
    console.error(`❌ 开发服务器退出，代码：${code}`);
  }
  process.exit(code);
});

// 处理中断信号
process.on('SIGINT', () => {
  console.log('\n🛑 正在停止开发服务器...');
  nextProcess.kill('SIGINT');
});

process.on('SIGTERM', () => {
  console.log('\n🛑 正在停止开发服务器...');
  nextProcess.kill('SIGTERM');
});

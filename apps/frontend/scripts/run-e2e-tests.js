#!/usr/bin/env node

/**
 * E2E 测试运行脚本
 * 自动检测环境并使用适当的配置运行 Playwright 测试
 */

const { spawn } = require('child_process');
const path = require('path');

// 检测是否在 CI 环境中
const isCI = process.env.CI === 'true' || 
             process.env.GITHUB_ACTIONS === 'true' || 
             process.env.GITLAB_CI === 'true' ||
             process.env.JENKINS_URL ||
             process.env.BUILDKITE ||
             process.env.CIRCLECI;

// 检测操作系统
const isLinux = process.platform === 'linux';
const isDocker = process.env.DOCKER === 'true' || 
                 process.env.CONTAINER === 'true';

console.log('🔍 环境检测:');
console.log(`  - CI 环境: ${isCI ? '是' : '否'}`);
console.log(`  - Linux 系统: ${isLinux ? '是' : '否'}`);
console.log(`  - Docker 容器: ${isDocker ? '是' : '否'}`);

// 选择配置文件
const configFile = (isCI || isLinux || isDocker) ? 
  'playwright-ci.config.ts' : 
  'playwright.config.ts';

console.log(`📋 使用配置文件: ${configFile}`);

// 构建命令参数
const args = ['playwright', 'test', `--config=${configFile}`];

// 添加命令行参数
const additionalArgs = process.argv.slice(2);
args.push(...additionalArgs);

console.log(`🚀 运行命令: npx ${args.join(' ')}`);

// 运行测试
const child = spawn('npx', args, {
  stdio: 'inherit',
  cwd: process.cwd(),
  env: {
    ...process.env,
    // 确保在 CI 环境中设置正确的环境变量
    ...(isCI && {
      CI: 'true',
      PLAYWRIGHT_BROWSERS_PATH: '0',
      NODE_ENV: 'test',
    }),
  },
});

child.on('close', (code) => {
  console.log(`\n📊 测试完成，退出码: ${code}`);
  process.exit(code);
});

child.on('error', (error) => {
  console.error('❌ 测试运行失败:', error);
  process.exit(1);
});

/**
 * 测试颜色模式修复的脚本
 * 
 * 这个脚本用于验证颜色模式开关是否能正确控制格子背景颜色的显示
 */

// 等待页面加载完成
function waitForPageLoad() {
  return new Promise((resolve) => {
    if (document.readyState === 'complete') {
      resolve();
    } else {
      window.addEventListener('load', resolve);
    }
  });
}

// 等待元素出现
function waitForElement(selector, timeout = 5000) {
  return new Promise((resolve, reject) => {
    const element = document.querySelector(selector);
    if (element) {
      resolve(element);
      return;
    }

    const observer = new MutationObserver(() => {
      const element = document.querySelector(selector);
      if (element) {
        observer.disconnect();
        resolve(element);
      }
    });

    observer.observe(document.body, {
      childList: true,
      subtree: true
    });

    setTimeout(() => {
      observer.disconnect();
      reject(new Error(`Element ${selector} not found within ${timeout}ms`));
    }, timeout);
  });
}

// 获取格子的背景颜色
function getCellBackgroundColors() {
  const cells = document.querySelectorAll('[data-testid="grid-matrix"] > div');
  const colors = new Set();
  const colorCounts = {};

  cells.forEach(cell => {
    const style = window.getComputedStyle(cell);
    const bgColor = style.backgroundColor;
    if (bgColor && bgColor !== 'rgba(0, 0, 0, 0)' && bgColor !== 'transparent') {
      colors.add(bgColor);
      colorCounts[bgColor] = (colorCounts[bgColor] || 0) + 1;
    }
  });

  return {
    colors: Array.from(colors),
    colorCounts,
    totalCells: cells.length
  };
}

// 查找颜色模式开关
function findColorModeToggle() {
  // 查找包含"颜色模式"文本的开关
  const labels = document.querySelectorAll('label, span, div');
  for (const label of labels) {
    if (label.textContent && label.textContent.includes('颜色模式')) {
      // 查找相关的开关元素
      const toggle = label.querySelector('input[type="checkbox"]') || 
                    label.parentElement?.querySelector('input[type="checkbox"]') ||
                    label.nextElementSibling?.querySelector('input[type="checkbox"]');
      if (toggle) {
        return toggle;
      }
    }
  }
  
  // 备用查找方法：查找所有checkbox
  const checkboxes = document.querySelectorAll('input[type="checkbox"]');
  for (const checkbox of checkboxes) {
    const parent = checkbox.closest('div');
    if (parent && parent.textContent && parent.textContent.includes('颜色模式')) {
      return checkbox;
    }
  }
  
  return null;
}

// 主测试函数
async function testColorModeFix() {
  console.log('🧪 开始测试颜色模式修复...');
  
  try {
    // 等待页面加载
    await waitForPageLoad();
    console.log('✅ 页面加载完成');
    
    // 等待网格出现
    await waitForElement('[data-testid="grid-matrix"]');
    console.log('✅ 网格组件已加载');
    
    // 查找颜色模式开关
    const colorToggle = findColorModeToggle();
    if (!colorToggle) {
      throw new Error('未找到颜色模式开关');
    }
    console.log('✅ 找到颜色模式开关');
    
    // 测试1: 检查初始状态（颜色模式关闭）
    console.log('\n📋 测试1: 检查初始状态（颜色模式关闭）');
    const initialResult = getCellBackgroundColors();
    console.log('初始背景颜色数量:', initialResult.colors.length);
    console.log('初始背景颜色:', initialResult.colors);
    console.log('初始颜色分布:', initialResult.colorCounts);

    // 测试2: 开启颜色模式
    console.log('\n📋 测试2: 开启颜色模式');
    if (!colorToggle.checked) {
      colorToggle.click();
      // 等待状态更新
      await new Promise(resolve => setTimeout(resolve, 500));
    }

    const enabledResult = getCellBackgroundColors();
    console.log('开启后背景颜色数量:', enabledResult.colors.length);
    console.log('开启后背景颜色:', enabledResult.colors);
    console.log('开启后颜色分布:', enabledResult.colorCounts);

    // 测试3: 关闭颜色模式
    console.log('\n📋 测试3: 关闭颜色模式');
    if (colorToggle.checked) {
      colorToggle.click();
      // 等待状态更新
      await new Promise(resolve => setTimeout(resolve, 500));
    }

    const disabledResult = getCellBackgroundColors();
    console.log('关闭后背景颜色数量:', disabledResult.colors.length);
    console.log('关闭后背景颜色:', disabledResult.colors);
    console.log('关闭后颜色分布:', disabledResult.colorCounts);
    
    // 验证结果
    console.log('\n🔍 验证结果:');

    // 检查颜色模式开启时是否有多种颜色
    const hasMultipleColorsWhenEnabled = enabledResult.colors.length > 1;

    // 检查颜色模式关闭时是否主要是默认颜色
    const defaultGrayColor = 'rgb(243, 244, 246)'; // #f3f4f6 的 RGB 值
    const hasMainlyDefaultColorWhenDisabled = disabledResult.colorCounts[defaultGrayColor] > (disabledResult.totalCells * 0.8);

    // 检查是否修复了 isActive: false 的问题
    const coloredCellsWhenEnabled = Object.values(enabledResult.colorCounts).reduce((sum, count) => sum + count, 0) - (enabledResult.colorCounts[defaultGrayColor] || 0);
    const hasColoredInactiveCells = coloredCellsWhenEnabled > 0;

    const isFixWorking = hasMultipleColorsWhenEnabled && hasMainlyDefaultColorWhenDisabled && hasColoredInactiveCells;

    if (isFixWorking) {
      console.log('✅ 修复成功！颜色模式开关能够正确控制格子背景颜色');
      console.log(`   - 颜色模式开启时有 ${enabledResult.colors.length} 种背景颜色`);
      console.log(`   - 颜色模式关闭时有 ${disabledResult.colors.length} 种背景颜色`);
      console.log(`   - 颜色模式开启时有 ${coloredCellsWhenEnabled} 个格子显示彩色（包括 isActive: false 的格子）`);
      console.log('✅ 已修复 isActive: false 导致格子背景颜色仍然是默认灰色的问题');
    } else {
      console.log('❌ 修复失败！存在以下问题：');
      if (!hasMultipleColorsWhenEnabled) {
        console.log('   - 颜色模式开启时没有显示多种颜色');
      }
      if (!hasMainlyDefaultColorWhenDisabled) {
        console.log('   - 颜色模式关闭时没有主要显示默认颜色');
      }
      if (!hasColoredInactiveCells) {
        console.log('   - isActive: false 的格子仍然没有显示颜色');
      }
    }

    return isFixWorking;
    
  } catch (error) {
    console.error('❌ 测试过程中出现错误:', error);
    return false;
  }
}

// 如果在浏览器环境中运行，自动开始测试
if (typeof window !== 'undefined') {
  // 延迟执行，确保页面完全加载
  setTimeout(testColorModeFix, 1000);
}

// 导出测试函数供手动调用
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { testColorModeFix };
}

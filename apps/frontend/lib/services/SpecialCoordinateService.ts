/**
 * SpecialCoordinateService - 坐标到字符的映射查询服务
 * 提供特殊坐标与字符之间的双向查询功能
 */

import { SPECIAL_COORDINATES } from '@/stores/constants/matrix';

export interface SpecialCoordinateError {
  type: 'invalid_coordinates' | 'coordinate_not_found' | 'invalid_character';
  message: string;
  input: unknown;
}

export interface CoordinateInfo {
  coords: [number, number];
  letter: string;
}

export class SpecialCoordinateService {
  /**
   * 根据坐标获取对应的字符
   * @param x X坐标
   * @param y Y坐标
   * @returns 对应的字符，如果坐标不存在则返回null
   */
  static getCharacter(x: number, y: number): string | null {
    try {
      if (!Number.isInteger(x) || !Number.isInteger(y)) {
        return null;
      }

      const key = `${x},${y}`;
      return SPECIAL_COORDINATES.get(key) ?? null;
    } catch (error) {
      process.env.NODE_ENV === 'development' && console.warn('SpecialCoordinateService.getCharacter error:', error);
      return null;
    }
  }

  /**
   * 根据字符获取对应的坐标
   * @param character 字符
   * @returns 对应的坐标，如果字符不存在则返回null
   */
  static getCoordinates(character: string): [number, number] | null {
    try {
      if (!character || typeof character !== 'string') {
        return null;
      }

      const normalizedChar = character.toUpperCase().trim();

      for (const [coords, letter] of SPECIAL_COORDINATES.entries()) {
        if (letter.toUpperCase() === normalizedChar) {
          const [x, y] = coords.split(',').map(Number);
          return [x, y];
        }
      }

      return null;
    } catch (error) {
      process.env.NODE_ENV === 'development' && console.warn('SpecialCoordinateService.getCoordinates error:', error);
      return null;
    }
  }

  /**
   * 检查指定坐标是否为特殊坐标
   * @param x X坐标
   * @param y Y坐标
   * @returns 是否为特殊坐标
   */
  static isSpecialCoordinate(x: number, y: number): boolean {
    return this.getCharacter(x, y) !== null;
  }

  /**
   * 检查指定字符是否为有效的特殊字符
   * @param character 字符
   * @returns 是否为有效的特殊字符
   */
  static isValidSpecialCharacter(character: string): boolean {
    return this.getCoordinates(character) !== null;
  }

  /**
   * 获取所有特殊坐标信息
   * @returns 所有特殊坐标的信息数组
   */
  static getAllSpecialCoordinates(): CoordinateInfo[] {
    try {
      return Array.from(SPECIAL_COORDINATES.entries()).map(([coords, letter]) => {
        const [x, y] = coords.split(',').map(Number);
        return { coords: [x, y] as [number, number], letter };
      });
    } catch (error) {
      process.env.NODE_ENV === 'development' && console.warn('SpecialCoordinateService.getAllSpecialCoordinates error:', error);
      return [];
    }
  }

  /**
   * 获取所有有效的特殊字符
   * @returns 所有有效特殊字符的数组
   */
  static getAllSpecialCharacters(): string[] {
    try {
      return Array.from(SPECIAL_COORDINATES.values()).sort();
    } catch (error) {
      process.env.NODE_ENV === 'development' && console.warn('SpecialCoordinateService.getAllSpecialCharacters error:', error);
      return [];
    }
  }

  /**
   * 获取指定范围内的特殊坐标
   * @param minX 最小X坐标
   * @param maxX 最大X坐标
   * @param minY 最小Y坐标
   * @param maxY 最大Y坐标
   * @returns 范围内的特殊坐标信息数组
   */
  static getSpecialCoordinatesInRange(
    minX: number,
    maxX: number,
    minY: number,
    maxY: number
  ): CoordinateInfo[] {
    try {
      return this.getAllSpecialCoordinates().filter(({ coords }) => {
        const [x, y] = coords;
        return x >= minX && x <= maxX && y >= minY && y <= maxY;
      });
    } catch (error) {
      process.env.NODE_ENV === 'development' && console.warn('SpecialCoordinateService.getSpecialCoordinatesInRange error:', error);
      return [];
    }
  }

  /**
   * 验证坐标并获取字符，提供详细的错误信息
   * @param x X坐标
   * @param y Y坐标
   * @returns 成功时返回字符，失败时返回错误信息
   */
  static validateAndGetCharacter(
    x: unknown,
    y: unknown
  ): { success: true; character: string } | { success: false; error: SpecialCoordinateError } {
    // 验证坐标类型
    if (typeof x !== 'number' || typeof y !== 'number') {
      return {
        success: false,
        error: {
          type: 'invalid_coordinates',
          message: `Coordinates must be numbers. Received: x=${typeof x}, y=${typeof y}`,
          input: { x, y }
        }
      };
    }

    // 验证坐标是否为整数
    if (!Number.isInteger(x) || !Number.isInteger(y)) {
      return {
        success: false,
        error: {
          type: 'invalid_coordinates',
          message: `Coordinates must be integers. Received: x=${x}, y=${y}`,
          input: { x, y }
        }
      };
    }

    const character = this.getCharacter(x, y);
    if (character === null) {
      return {
        success: false,
        error: {
          type: 'coordinate_not_found',
          message: `No special character found for coordinates (${x}, ${y})`,
          input: { x, y }
        }
      };
    }

    return { success: true, character };
  }

  /**
   * 验证字符并获取坐标，提供详细的错误信息
   * @param character 字符
   * @returns 成功时返回坐标，失败时返回错误信息
   */
  static validateAndGetCoordinates(
    character: unknown
  ): { success: true; coordinates: [number, number] } | { success: false; error: SpecialCoordinateError } {
    // 验证字符类型
    if (typeof character !== 'string') {
      return {
        success: false,
        error: {
          type: 'invalid_character',
          message: `Character must be a string. Received: ${typeof character}`,
          input: character
        }
      };
    }

    // 验证字符是否为空
    if (!character.trim()) {
      return {
        success: false,
        error: {
          type: 'invalid_character',
          message: 'Character cannot be empty',
          input: character
        }
      };
    }

    const coordinates = this.getCoordinates(character);
    if (coordinates === null) {
      const validCharacters = this.getAllSpecialCharacters();
      return {
        success: false,
        error: {
          type: 'coordinate_not_found',
          message: `No coordinates found for character '${character}'. Valid characters are: ${validCharacters.join(', ')}`,
          input: character
        }
      };
    }

    return { success: true, coordinates };
  }

  /**
   * 获取特殊坐标的统计信息
   * @returns 特殊坐标的统计信息
   */
  static getStatistics() {
    const allCoordinates = this.getAllSpecialCoordinates();
    const xCoords = allCoordinates.map(({ coords }) => coords[0]);
    const yCoords = allCoordinates.map(({ coords }) => coords[1]);

    return {
      totalCount: allCoordinates.length,
      characters: this.getAllSpecialCharacters(),
      xRange: {
        min: Math.min(...xCoords),
        max: Math.max(...xCoords)
      },
      yRange: {
        min: Math.min(...yCoords),
        max: Math.max(...yCoords)
      }
    };
  }

  /**
   * 根据坐标获取对应的字符（别名方法，与getCharacter相同）
   * @param x X坐标
   * @param y Y坐标
   * @returns 对应的字符，如果坐标不存在则返回null
   */
  static getCharacterForCoordinate(x: number, y: number): string | null {
    return this.getCharacter(x, y);
  }

  /**
   * 根据字符获取对应的数值
   * @param character 字符
   * @returns 对应的数值，A=1, B=2, ..., M=13，如果字符无效则返回0
   */
  static getNumericValueForCharacter(character: string): number {
    try {
      if (!character || typeof character !== 'string') {
        return 0;
      }

      const normalizedChar = character.toUpperCase().trim();

      // A=1, B=2, ..., M=13
      const charCode = normalizedChar.charCodeAt(0);
      if (charCode >= 65 && charCode <= 77) { // A-M
        return charCode - 64; // A=1, B=2, etc.
      }

      return 0;
    } catch (error) {
      process.env.NODE_ENV === 'development' && console.warn('SpecialCoordinateService.getNumericValueForCharacter error:', error);
      return 0;
    }
  }
}
/**
 * ColorCoordinateService - 颜色坐标映射查询服务
 * 基于 GROUP_A_DATA 提供高效的颜色坐标查找功能
 * 支持灰度模式下的选择性颜色显示功能
 */

import { GROUP_A_DATA, SPECIAL_COORDINATES } from '@/stores/constants/matrix';
import type { BasicColorType } from '@/lib/types/matrix';

export interface ColorCoordinateError {
  type: 'invalid_coordinates' | 'coordinate_not_found' | 'invalid_color_type' | 'invalid_level';
  message: string;
  input: unknown;
}

export interface ColorCoordinateInfo {
  color: BasicColorType;
  level: 1 | 2 | 3 | 4;
  coords: [number, number];
}

/**
 * 颜色坐标映射服务类
 * 提供基于 GROUP_A_DATA 的高效颜色坐标查找功能
 */
export class ColorCoordinateService {
  // 坐标到颜色的映射缓存 - 格式: "x,y" -> color
  private static coordinateToColorMap: Map<string, BasicColorType> | null = null;
  
  // 颜色到坐标集合的映射缓存 - 格式: color -> Set<"x,y">
  private static colorToCoordinatesMap: Map<BasicColorType, Set<string>> | null = null;

  /**
   * 初始化坐标映射缓存
   * 基于 GROUP_A_DATA 构建高效的查找映射
   */
  private static initializeMappings(): void {
    if (this.coordinateToColorMap && this.colorToCoordinatesMap) {
      return; // 已初始化
    }

    this.coordinateToColorMap = new Map();
    this.colorToCoordinatesMap = new Map();

    try {
      // 遍历 GROUP_A_DATA 构建映射
      Object.entries(GROUP_A_DATA).forEach(([colorKey, levels]) => {
        const color = colorKey as BasicColorType;
        const coordinateSet = new Set<string>();

        Object.entries(levels).forEach(([levelKey, coordinates]) => {
          coordinates.forEach(([x, y]) => {
            const coordKey = `${x},${y}`;
            this.coordinateToColorMap!.set(coordKey, color);
            coordinateSet.add(coordKey);
          });
        });

        this.colorToCoordinatesMap!.set(color, coordinateSet);
      });
    } catch (error) {
      process.env.NODE_ENV === 'development' && console.error('ColorCoordinateService: Failed to initialize mappings', error);
      // 重置为空映射以防止部分初始化状态
      this.coordinateToColorMap = new Map();
      this.colorToCoordinatesMap = new Map();
    }
  }

  /**
   * 根据坐标获取对应的颜色
   * @param x X坐标
   * @param y Y坐标
   * @returns 对应的颜色类型，如果坐标不存在则返回null
   */
  static getCellColorByCoordinate(x: number, y: number): BasicColorType | null {
    try {
      // 验证输入参数
      if (!Number.isInteger(x) || !Number.isInteger(y)) {
        return null;
      }

      const coordKey = `${x},${y}`;

      // 首先检查黑色的特殊坐标
      if (SPECIAL_COORDINATES.has(coordKey)) {
        return 'black';
      }

      this.initializeMappings();

      return this.coordinateToColorMap!.get(coordKey) ?? null;
    } catch (error) {
      process.env.NODE_ENV === 'development' && console.warn('ColorCoordinateService.getCellColorByCoordinate error:', error);
      return null;
    }
  }

  /**
   * 检查指定坐标是否包含颜色数据
   * @param x X坐标
   * @param y Y坐标
   * @returns 是否包含颜色数据
   */
  static hasColorAtCoordinate(x: number, y: number): boolean {
    return this.getCellColorByCoordinate(x, y) !== null;
  }

  /**
   * 根据颜色获取所有对应的坐标
   * @param color 颜色类型
   * @returns 对应的坐标数组，如果颜色不存在则返回空数组
   */
  static getCoordinatesByColor(color: BasicColorType): [number, number][] {
    try {
      if (!this.isValidColorType(color)) {
        return [];
      }

      this.initializeMappings();
      
      const coordinateSet = this.colorToCoordinatesMap!.get(color);
      if (!coordinateSet) {
        return [];
      }

      return Array.from(coordinateSet).map(coordKey => {
        const [x, y] = coordKey.split(',').map(Number);
        return [x, y] as [number, number];
      });
    } catch (error) {
      process.env.NODE_ENV === 'development' && console.warn('ColorCoordinateService.getCoordinatesByColor error:', error);
      return [];
    }
  }

  /**
   * 根据颜色和级别获取对应的坐标
   * @param color 颜色类型
   * @param level 级别 (1-4)
   * @returns 对应的坐标数组，如果颜色或级别不存在则返回空数组
   */
  static getCoordinatesByColorAndLevel(
    color: BasicColorType, 
    level: 1 | 2 | 3 | 4
  ): [number, number][] {
    try {
      if (!this.isValidColorType(color) || !this.isValidLevel(level)) {
        return [];
      }

      const colorData = GROUP_A_DATA[color as keyof typeof GROUP_A_DATA];
      if (!colorData) {
        return [];
      }

      const levelData = (colorData as any)[level];
      return levelData ? [...levelData] : [];
    } catch (error) {
      process.env.NODE_ENV === 'development' && console.warn('ColorCoordinateService.getCoordinatesByColorAndLevel error:', error);
      return [];
    }
  }

  /**
   * 获取指定坐标的详细颜色信息
   * @param x X坐标
   * @param y Y坐标
   * @returns 颜色信息，如果坐标不存在则返回null
   */
  static getColorInfoByCoordinate(x: number, y: number): ColorCoordinateInfo | null {
    try {
      const color = this.getCellColorByCoordinate(x, y);
      if (!color) {
        return null;
      }

      // 查找对应的级别
      const colorData = GROUP_A_DATA[color as keyof typeof GROUP_A_DATA];
      if (!colorData) {
        return null;
      }

      for (const [levelKey, coordinates] of Object.entries(colorData)) {
        const level = parseInt(levelKey) as 1 | 2 | 3 | 4;
        const hasCoordinate = coordinates.some(([cx, cy]) => cx === x && cy === y);
        
        if (hasCoordinate) {
          return {
            color,
            level,
            coords: [x, y]
          };
        }
      }

      return null;
    } catch (error) {
      process.env.NODE_ENV === 'development' && console.warn('ColorCoordinateService.getColorInfoByCoordinate error:', error);
      return null;
    }
  }

  /**
   * 获取所有颜色的坐标统计信息
   * @returns 颜色坐标统计信息
   */
  static getColorCoordinateStatistics() {
    try {
      this.initializeMappings();
      
      const stats = {
        totalCoordinates: this.coordinateToColorMap!.size,
        byColor: {} as Record<BasicColorType, number>,
        byLevel: {} as Record<number, number>,
        coordinateRanges: {
          x: { min: Infinity, max: -Infinity },
          y: { min: Infinity, max: -Infinity }
        }
      };

      // 统计每种颜色的坐标数量
      for (const color of this.colorToCoordinatesMap!.keys()) {
        const coordinates = this.getCoordinatesByColor(color);
        stats.byColor[color] = coordinates.length;

        // 更新坐标范围
        coordinates.forEach(([x, y]) => {
          stats.coordinateRanges.x.min = Math.min(stats.coordinateRanges.x.min, x);
          stats.coordinateRanges.x.max = Math.max(stats.coordinateRanges.x.max, x);
          stats.coordinateRanges.y.min = Math.min(stats.coordinateRanges.y.min, y);
          stats.coordinateRanges.y.max = Math.max(stats.coordinateRanges.y.max, y);
        });
      }

      // 统计每个级别的坐标数量
      Object.entries(GROUP_A_DATA).forEach(([colorKey, levels]) => {
        Object.entries(levels).forEach(([levelKey, coordinates]) => {
          const level = parseInt(levelKey);
          stats.byLevel[level] = (stats.byLevel[level] || 0) + coordinates.length;
        });
      });

      return stats;
    } catch (error) {
      process.env.NODE_ENV === 'development' && console.warn('ColorCoordinateService.getColorCoordinateStatistics error:', error);
      return {
        totalCoordinates: 0,
        byColor: {} as Record<BasicColorType, number>,
        byLevel: {} as Record<number, number>,
        coordinateRanges: {
          x: { min: 0, max: 0 },
          y: { min: 0, max: 0 }
        }
      };
    }
  }

  /**
   * 获取指定范围内的颜色坐标
   * @param minX 最小X坐标
   * @param maxX 最大X坐标
   * @param minY 最小Y坐标
   * @param maxY 最大Y坐标
   * @returns 范围内的颜色坐标信息数组
   */
  static getColorCoordinatesInRange(
    minX: number,
    maxX: number,
    minY: number,
    maxY: number
  ): ColorCoordinateInfo[] {
    try {
      this.initializeMappings();
      
      const result: ColorCoordinateInfo[] = [];

      for (const [coordKey, color] of this.coordinateToColorMap!.entries()) {
        const [x, y] = coordKey.split(',').map(Number);
        
        if (x >= minX && x <= maxX && y >= minY && y <= maxY) {
          const colorInfo = this.getColorInfoByCoordinate(x, y);
          if (colorInfo) {
            result.push(colorInfo);
          }
        }
      }

      return result.sort((a, b) => {
        // 按颜色排序，然后按级别排序
        if (a.color !== b.color) {
          return a.color.localeCompare(b.color);
        }
        return a.level - b.level;
      });
    } catch (error) {
      process.env.NODE_ENV === 'development' && console.warn('ColorCoordinateService.getColorCoordinatesInRange error:', error);
      return [];
    }
  }

  /**
   * 验证颜色类型是否有效
   * @param color 要验证的颜色类型
   * @returns 是否为有效的颜色类型
   */
  static isValidColorType(color: unknown): color is BasicColorType {
    return typeof color === 'string' && color in GROUP_A_DATA;
  }

  /**
   * 验证级别是否有效
   * @param level 要验证的级别
   * @returns 是否为有效的级别
   */
  static isValidLevel(level: unknown): level is 1 | 2 | 3 | 4 {
    return typeof level === 'number' && [1, 2, 3, 4].includes(level);
  }

  /**
   * 验证坐标并获取颜色，提供详细的错误信息
   * @param x X坐标
   * @param y Y坐标
   * @returns 成功时返回颜色，失败时返回错误信息
   */
  static validateAndGetColor(
    x: unknown,
    y: unknown
  ): { success: true; color: BasicColorType } | { success: false; error: ColorCoordinateError } {
    // 验证坐标类型
    if (typeof x !== 'number' || typeof y !== 'number') {
      return {
        success: false,
        error: {
          type: 'invalid_coordinates',
          message: `Coordinates must be numbers. Received: x=${typeof x}, y=${typeof y}`,
          input: { x, y }
        }
      };
    }

    // 验证坐标是否为整数
    if (!Number.isInteger(x) || !Number.isInteger(y)) {
      return {
        success: false,
        error: {
          type: 'invalid_coordinates',
          message: `Coordinates must be integers. Received: x=${x}, y=${y}`,
          input: { x, y }
        }
      };
    }

    const color = this.getCellColorByCoordinate(x, y);
    if (color === null) {
      return {
        success: false,
        error: {
          type: 'coordinate_not_found',
          message: `No color found for coordinates (${x}, ${y})`,
          input: { x, y }
        }
      };
    }

    return { success: true, color };
  }

  /**
   * 验证颜色并获取坐标，提供详细的错误信息
   * @param color 颜色类型
   * @returns 成功时返回坐标数组，失败时返回错误信息
   */
  static validateAndGetCoordinates(
    color: unknown
  ): { success: true; coordinates: [number, number][] } | { success: false; error: ColorCoordinateError } {
    if (!this.isValidColorType(color)) {
      const validColors = Object.keys(GROUP_A_DATA);
      return {
        success: false,
        error: {
          type: 'invalid_color_type',
          message: `Invalid color type: ${color}. Valid colors are: ${validColors.join(', ')}`,
          input: color
        }
      };
    }

    const coordinates = this.getCoordinatesByColor(color);
    return { success: true, coordinates };
  }

  /**
   * 清除缓存映射（用于测试或重新初始化）
   */
  static clearCache(): void {
    this.coordinateToColorMap = null;
    this.colorToCoordinatesMap = null;
  }

  /**
   * 获取缓存状态信息（用于调试）
   */
  static getCacheInfo() {
    return {
      isInitialized: this.coordinateToColorMap !== null && this.colorToCoordinatesMap !== null,
      coordinateMapSize: this.coordinateToColorMap?.size ?? 0,
      colorMapSize: this.colorToCoordinatesMap?.size ?? 0
    };
  }
}
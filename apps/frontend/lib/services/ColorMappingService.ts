/**
 * ColorMappingService - 颜色到mappingValue的映射查询服务
 * 提供颜色类型与映射值之间的双向查询功能
 */

import { DEFAULT_COLOR_VALUES } from '@/stores/constants/matrix';
import type { BasicColorType, ColorValue } from '@/lib/types/matrix';

export interface ColorMappingError {
  type: 'invalid_color_type' | 'invalid_hex' | 'invalid_mapping_value';
  message: string;
  input: unknown;
}

export class ColorMappingService {
  /**
   * 根据颜色类型获取映射值
   * @param colorType 颜色类型
   * @returns 映射值，如果颜色类型无效或没有映射值则返回null
   */
  static getMappingValue(colorType: BasicColorType): number | null {
    try {
      const colorValue = DEFAULT_COLOR_VALUES[colorType];
      if (!colorValue) {
        return null;
      }
      
      // 黑色没有mappingValue
      if (colorType === 'black') {
        return null;
      }
      
      return colorValue.mappingValue ?? null;
    } catch (error) {
      process.env.NODE_ENV === 'development' && console.warn('ColorMappingService.getMappingValue error:', error);
      return null;
    }
  }

  /**
   * 根据十六进制颜色值获取颜色类型
   * @param hex 十六进制颜色值 (如: '#ef4444')
   * @returns 颜色类型，如果找不到匹配的颜色则返回null
   */
  static getColorTypeFromHex(hex: string): BasicColorType | null {
    try {
      if (!hex || typeof hex !== 'string') {
        return null;
      }

      // 标准化hex格式
      const normalizedHex = hex.toLowerCase().trim();
      
      for (const [colorType, colorValue] of Object.entries(DEFAULT_COLOR_VALUES)) {
        if (colorValue.hex.toLowerCase() === normalizedHex) {
          return colorType as BasicColorType;
        }
      }
      
      return null;
    } catch (error) {
      process.env.NODE_ENV === 'development' && console.warn('ColorMappingService.getColorTypeFromHex error:', error);
      return null;
    }
  }

  /**
   * 根据映射值获取颜色类型
   * @param mappingValue 映射值
   * @returns 颜色类型，如果找不到匹配的映射值则返回null
   */
  static getColorTypeFromMappingValue(mappingValue: number): BasicColorType | null {
    try {
      if (typeof mappingValue !== 'number' || !Number.isInteger(mappingValue)) {
        return null;
      }

      for (const [colorType, colorValue] of Object.entries(DEFAULT_COLOR_VALUES)) {
        if (colorValue.mappingValue === mappingValue) {
          return colorType as BasicColorType;
        }
      }
      
      return null;
    } catch (error) {
      process.env.NODE_ENV === 'development' && console.warn('ColorMappingService.getColorTypeFromMappingValue error:', error);
      return null;
    }
  }

  /**
   * 验证映射值是否有效
   * @param value 要验证的值
   * @returns 是否为有效的映射值
   */
  static isValidMappingValue(value: unknown): value is number {
    if (typeof value !== 'number' || !Number.isInteger(value)) {
      return false;
    }

    // 检查是否存在于已定义的映射值中
    const validMappingValues = Object.values(DEFAULT_COLOR_VALUES)
      .map(colorValue => colorValue.mappingValue)
      .filter(mappingValue => mappingValue !== undefined);

    return validMappingValues.includes(value);
  }

  /**
   * 验证颜色类型是否有效
   * @param colorType 要验证的颜色类型
   * @returns 是否为有效的颜色类型
   */
  static isValidColorType(colorType: unknown): colorType is BasicColorType {
    return typeof colorType === 'string' && colorType in DEFAULT_COLOR_VALUES;
  }

  /**
   * 获取所有有效的映射值
   * @returns 所有有效映射值的数组
   */
  static getAllMappingValues(): number[] {
    return Object.values(DEFAULT_COLOR_VALUES)
      .map(colorValue => colorValue.mappingValue)
      .filter((mappingValue): mappingValue is number => mappingValue !== undefined)
      .sort((a, b) => a - b);
  }

  /**
   * 获取所有颜色类型到映射值的映射
   * @returns 颜色类型到映射值的映射对象
   */
  static getAllColorMappings(): Record<BasicColorType, number | null> {
    const mappings: Record<string, number | null> = {};
    
    for (const colorType of Object.keys(DEFAULT_COLOR_VALUES) as BasicColorType[]) {
      mappings[colorType] = this.getMappingValue(colorType);
    }
    
    return mappings as Record<BasicColorType, number | null>;
  }

  /**
   * 验证并获取映射值，提供详细的错误信息
   * @param colorType 颜色类型
   * @returns 成功时返回映射值，失败时返回错误信息
   */
  static validateAndGetMappingValue(colorType: unknown): { success: true; value: number } | { success: false; error: ColorMappingError } {
    if (!this.isValidColorType(colorType)) {
      return {
        success: false,
        error: {
          type: 'invalid_color_type',
          message: `Invalid color type: ${colorType}. Valid types are: ${Object.keys(DEFAULT_COLOR_VALUES).join(', ')}`,
          input: colorType
        }
      };
    }

    const mappingValue = this.getMappingValue(colorType);
    if (mappingValue === null) {
      return {
        success: false,
        error: {
          type: 'invalid_mapping_value',
          message: `Color type '${colorType}' does not have a mapping value`,
          input: colorType
        }
      };
    }

    return { success: true, value: mappingValue };
  }

  /**
   * 根据颜色类型和级别获取数值（mappingValue）
   * @param colorType 颜色类型
   * @param level 级别
   * @returns 数值，如果无效则返回0
   */
  static getNumericValue(colorType: BasicColorType, level: number): number {
    try {
      const mappingValue = this.getMappingValue(colorType);
      return mappingValue ?? 0;
    } catch (error) {
      process.env.NODE_ENV === 'development' && console.warn('ColorMappingService.getNumericValue error:', error);
      return 0;
    }
  }

  /**
   * 根据颜色类型、级别和颜色值配置获取十六进制颜色
   * @param colorType 颜色类型
   * @param level 级别
   * @param colorValues 颜色值配置
   * @returns 十六进制颜色值，如果无效则返回null
   */
  static getColorForValue(
    colorType: BasicColorType,
    level: number,
    colorValues: Record<BasicColorType, ColorValue>
  ): string | null {
    try {
      const colorValue = colorValues[colorType];
      if (!colorValue) {
        return null;
      }
      return colorValue.hex;
    } catch (error) {
      process.env.NODE_ENV === 'development' && console.warn('ColorMappingService.getColorForValue error:', error);
      return null;
    }
  }

  /**
   * 根据颜色类型和级别获取显示值
   * @param colorType 颜色类型
   * @param level 级别
   * @returns 显示值字符串，如果无效则返回null
   */
  static getDisplayValue(colorType: BasicColorType, level: number): string | null {
    try {
      const mappingValue = this.getMappingValue(colorType);
      if (mappingValue === null) {
        return null;
      }
      return mappingValue.toString();
    } catch (error) {
      process.env.NODE_ENV === 'development' && console.warn('ColorMappingService.getDisplayValue error:', error);
      return null;
    }
  }
}
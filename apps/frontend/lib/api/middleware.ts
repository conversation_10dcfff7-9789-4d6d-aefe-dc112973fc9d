/**
 * API中间件 - 统一的错误处理和请求处理
 * 🎯 核心价值：消除API路由中的重复代码模式
 * ⚡ 性能优化：统一的错误处理、请求验证、响应格式化
 * 📊 功能范围：错误处理、数据验证、响应标准化
 * 🔄 架构设计：中间件模式、类型安全、可扩展性
 * 📦 重构：从lib/api-middleware.ts移动到根目录统一管理
 */

import { NextRequest, NextResponse } from 'next/server';
import { ZodSchema, ZodError } from 'zod';
import { logger } from '@/lib/hooks/useLogManager';
import { ApiResponse, ApiError } from '@/lib/types/api';

// 本地类型定义
export type ApiHandler<T = any> = (
  request: NextRequest,
  context?: { params?: any }
) => Promise<NextResponse<T>>;

export interface MiddlewareConfig {
  timeout?: number;
  enableLogging?: boolean;
  enableCors?: boolean;
  requireAuth?: boolean;
  validateSchema?: ZodSchema;
  rateLimit?: {
    windowMs: number;
    max: number;
  };
  validation?: {
    body?: ZodSchema;
    query?: ZodSchema;
  };
}

/**
 * 统一的API错误处理中间件
 */
export function withErrorHandling<T = any>(
  handler: ApiHandler<T>,
  config: MiddlewareConfig = {}
): ApiHandler<T> {
  return async (request: NextRequest, context?: { params?: any }): Promise<NextResponse<T>> => {
    const requestId = generateRequestId();
    const startTime = Date.now();

    try {
      // 记录请求开始
      if (config.enableLogging !== false) {
        logger.debug('api', `API请求开始: ${request.method} ${request.url}`);
      }

      // CORS处理
      if (config.enableCors) {
        const corsHeaders = getCorsHeaders(request);
        if (request.method === 'OPTIONS') {
          return new NextResponse(null, { status: 200, headers: corsHeaders });
        }
      }

      // 请求体验证
      if (config.validateSchema && ['POST', 'PUT', 'PATCH'].includes(request.method)) {
        await validateRequestBody(request, config.validateSchema);
      }

      // 身份验证
      if (config.requireAuth) {
        await validateAuthentication(request);
      }

      // 执行处理器
      const response = await handler(request, context);

      // 记录请求完成
      if (config.enableLogging !== false) {
        const duration = Date.now() - startTime;
        logger.debug('api', `API请求完成: ${request.method} ${request.url} (${duration}ms)`);
      }

      // 添加通用响应头
      const headers = new Headers(response.headers);
      headers.set('X-Request-ID', requestId);
      headers.set('X-Response-Time', `${Date.now() - startTime}ms`);

      if (config.enableCors) {
        const corsHeaders = getCorsHeaders(request);
        corsHeaders.forEach((value, key) => headers.set(key, value));
      }

      return new NextResponse(response.body, {
        status: response.status,
        statusText: response.statusText,
        headers,
      });

    } catch (error) {
      // 统一错误处理
      return handleApiError(error, requestId, request) as NextResponse<T>;
    }
  };
}

/**
 * 统一的错误处理函数
 */
function handleApiError(
  error: unknown,
  requestId: string,
  request: NextRequest
): NextResponse<ApiResponse> {
  let apiError: ApiError;

  if (error instanceof ApiError) {
    apiError = error;
  } else if (error instanceof ZodError) {
    apiError = new ApiError(
      'validation',
      '请求数据验证失败',
      400,
      error.errors
    );
  } else if (error instanceof Error) {
    apiError = new ApiError(
      'server_error',
      error.message || '服务器内部错误',
      500
    );
  } else {
    apiError = new ApiError(
      'server_error',
      '未知错误',
      500
    );
  }

  // 记录错误
  logger.error('api', `API错误: ${request.method} ${request.url} - ${apiError.message}`);

  const response: ApiResponse = {
    success: false,
    error: apiError.message,
    timestamp: Date.now(),
  };

  return NextResponse.json(response, { 
    status: apiError.statusCode,
    headers: {
      'X-Request-ID': requestId,
    }
  });
}

/**
 * 生成请求ID
 */
function generateRequestId(): string {
  return `req_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
}

/**
 * 获取CORS头
 */
function getCorsHeaders(request: NextRequest): Headers {
  const headers = new Headers();
  
  const origin = request.headers.get('origin');
  const allowedOrigins = process.env.ALLOWED_ORIGINS?.split(',') || ['*'];
  
  if (allowedOrigins.includes('*') || (origin && allowedOrigins.includes(origin))) {
    headers.set('Access-Control-Allow-Origin', origin || '*');
  }
  
  headers.set('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  headers.set('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With');
  headers.set('Access-Control-Allow-Credentials', 'true');
  headers.set('Access-Control-Max-Age', '86400');
  
  return headers;
}

/**
 * 验证请求体
 */
async function validateRequestBody(request: NextRequest, schema: ZodSchema): Promise<void> {
  try {
    const body = await request.json();
    schema.parse(body);
  } catch (error) {
    if (error instanceof ZodError) {
      throw error;
    }
    throw new ApiError('validation', '请求体格式错误', 400);
  }
}

/**
 * 验证身份认证（已移除）
 * 认证功能已被移除
 */
async function validateAuthentication(_request: NextRequest): Promise<void> {
  // 认证验证已被移除，直接通过
  return;
}

/**
 * 速率限制中间件
 */
export function withRateLimit(
  handler: ApiHandler,
  options: { windowMs: number; max: number } = { windowMs: 60000, max: 100 }
): ApiHandler {
  const requests = new Map<string, { count: number; resetTime: number }>();
  
  return async (request: NextRequest, context?: { params?: any }) => {
    const clientId = getClientId(request);
    const now = Date.now();
    
    // 清理过期记录
    for (const [key, value] of requests.entries()) {
      if (now > value.resetTime) {
        requests.delete(key);
      }
    }
    
    const clientData = requests.get(clientId) || { count: 0, resetTime: now + options.windowMs };
    
    if (clientData.count >= options.max) {
      throw new ApiError('rate_limit', '请求过于频繁，请稍后再试', 429);
    }
    
    clientData.count++;
    requests.set(clientId, clientData);
    
    return handler(request, context);
  };
}

/**
 * 获取客户端ID（用于速率限制）
 */
function getClientId(request: NextRequest): string {
  // 优先使用IP地址
  const forwarded = request.headers.get('x-forwarded-for');
  const ip = forwarded ? forwarded.split(',')[0] : request.headers.get('x-real-ip') || 'unknown';
  
  // 可以结合用户ID或其他标识符
  const userAgent = request.headers.get('user-agent') || '';
  
  return `${ip}_${userAgent.slice(0, 50)}`;
}

/**
 * 组合多个中间件
 */
export function compose(...middlewares: Array<(handler: ApiHandler) => ApiHandler>) {
  return (handler: ApiHandler): ApiHandler => {
    return middlewares.reduceRight((acc, middleware) => middleware(acc), handler);
  };
}

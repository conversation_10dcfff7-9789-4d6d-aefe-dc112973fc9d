/**
 * 统一API配置管理
 * 🎯 核心价值：集中管理所有API配置，简化维护和扩展
 * ⚡ 性能优化：统一配置减少重复代码，提高可维护性
 * 🔄 标准化：遵循HTTP规范，统一错误处理和重试机制
 * 🕒 更新时间：2025年7月12日
 */

// === 环境配置 ===
export const ENV_CONFIG = {
  // API基础URL配置
  API_BASE_URL: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000/api/v1',
  BACKEND_URL: process.env.NEXT_PUBLIC_BACKEND_URL || 'http://localhost:8000',
  
  // 环境检测
  IS_DEVELOPMENT: process.env.NODE_ENV === 'development',
  IS_PRODUCTION: process.env.NODE_ENV === 'production',
  
  // 特性开关
  ENABLE_API_LOGGING: process.env.NEXT_PUBLIC_ENABLE_API_LOGGING === 'true',
  ENABLE_API_CACHE: process.env.NEXT_PUBLIC_ENABLE_API_CACHE !== 'false',
} as const;

// === HTTP配置 ===
export const HTTP_CONFIG = {
  // 超时配置
  DEFAULT_TIMEOUT: 10000, // 10秒
  UPLOAD_TIMEOUT: 60000,  // 60秒
  
  // 重试配置
  DEFAULT_RETRIES: 3,
  RETRY_DELAY_BASE: 1000, // 1秒基础延迟
  
  // 请求头配置
  DEFAULT_HEADERS: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  },
  
  // CORS配置
  CORS_HEADERS: {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-Requested-With',
  },
} as const;

// === API端点配置 ===
export const API_ENDPOINTS = {
  // 系统端点
  HEALTH: '/health',
  
  // 认证端点（已移除）
  // AUTH: { ... },
  
  // 用户管理
  USERS: {
    BASE: '/users',
    BY_ID: (id: string) => `/users/${id}`,
  },
  
  // 项目管理
  PROJECTS: {
    BASE: '/projects',
    BY_ID: (id: string) => `/projects/${id}`,
    SETTINGS: (id: string) => `/projects/${id}/settings`,
  },
  
  // 数据管理
  DATA: {
    COLOR: '/data/color',
    GRID: '/data/grid',
    MIGRATION: '/data/migration',
  },
} as const;

// === 错误配置 ===
export const ERROR_CONFIG = {
  // HTTP状态码映射
  STATUS_MESSAGES: {
    400: '请求参数错误',
    401: '未授权访问',
    403: '禁止访问',
    404: '资源不存在',
    409: '资源冲突',
    429: '请求过于频繁',
    500: '服务器内部错误',
    502: '网关错误',
    503: '服务不可用',
    504: '网关超时',
  },
  
  // 错误类型
  ERROR_TYPES: {
    NETWORK: 'network_error',
    TIMEOUT: 'timeout_error',
    VALIDATION: 'validation_error',
    AUTH: 'auth_error',
    SERVER: 'server_error',
    UNKNOWN: 'unknown_error',
  },
  
  // 可重试的错误状态码
  RETRYABLE_STATUS_CODES: [408, 429, 500, 502, 503, 504],
} as const;

// === 缓存配置 ===
export const CACHE_CONFIG = {
  // 缓存策略
  STRATEGIES: {
    NO_CACHE: 'no-cache',
    CACHE_FIRST: 'cache-first',
    NETWORK_FIRST: 'network-first',
    STALE_WHILE_REVALIDATE: 'stale-while-revalidate',
  },
  
  // 默认TTL（秒）
  DEFAULT_TTL: 300, // 5分钟
  
  // 端点特定缓存配置
  ENDPOINT_CONFIG: {
    [API_ENDPOINTS.HEALTH]: { ttl: 30, strategy: 'network-first' },
  },
} as const;

// === 请求配置类型 ===
export interface RequestConfig {
  timeout?: number;
  retries?: number;
  cache?: boolean;
  cacheStrategy?: keyof typeof CACHE_CONFIG.STRATEGIES;
  cacheTtl?: number;
  headers?: Record<string, string>;
  signal?: AbortSignal;
}

// === API配置类型 ===
export interface ApiConfig {
  baseUrl: string;
  timeout: number;
  retries: number;
  headers: Record<string, string>;
  enableLogging: boolean;
  enableCache: boolean;
}

// === 默认API配置 ===
export const DEFAULT_API_CONFIG: ApiConfig = {
  baseUrl: ENV_CONFIG.API_BASE_URL,
  timeout: HTTP_CONFIG.DEFAULT_TIMEOUT,
  retries: HTTP_CONFIG.DEFAULT_RETRIES,
  headers: HTTP_CONFIG.DEFAULT_HEADERS,
  enableLogging: ENV_CONFIG.ENABLE_API_LOGGING,
  enableCache: ENV_CONFIG.ENABLE_API_CACHE,
};

// === 工具函数 ===

/**
 * 构建完整的API URL
 */
export function buildApiUrl(endpoint: string, baseUrl?: string): string {
  const base = baseUrl || ENV_CONFIG.API_BASE_URL;
  return `${base}${endpoint}`;
}

/**
 * 获取端点的缓存配置
 */
export function getCacheConfig(endpoint: string) {
  return (CACHE_CONFIG.ENDPOINT_CONFIG as any)[endpoint] || {
    ttl: CACHE_CONFIG.DEFAULT_TTL,
    strategy: CACHE_CONFIG.STRATEGIES.NETWORK_FIRST,
  };
}

/**
 * 检查状态码是否可重试
 */
export function isRetryableError(status: number): boolean {
  return ERROR_CONFIG.RETRYABLE_STATUS_CODES.includes(status as any);
}

/**
 * 获取错误消息
 */
export function getErrorMessage(status: number): string {
  return ERROR_CONFIG.STATUS_MESSAGES[status as keyof typeof ERROR_CONFIG.STATUS_MESSAGES] || '未知错误';
}

/**
 * 检查是否使用FastAPI后端
 */
export function isUsingFastAPI(): boolean {
  return ENV_CONFIG.API_BASE_URL.includes('8000') || ENV_CONFIG.API_BASE_URL.includes('localhost:8000');
}

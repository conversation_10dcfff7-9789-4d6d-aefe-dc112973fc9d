/**
 * API统一导出
 * 🎯 核心价值：统一API逻辑管理，提供一站式API访问
 * 📦 重构：API逻辑统一管理
 */

// 客户端API导出
export * from './client';

// 生成的API客户端导出（暂时注释掉，文件不存在）
// export * from './generated-client';

// 中间件导出
export { withErrorHandling, withRateLimit, compose } from './middleware';

// 工具函数导出
export * from './utils';

// API处理器导出
export * from './handlers';

// 数据迁移导出已移除

// Prisma客户端导出
export { default as prisma } from './prisma';
export * from './prisma';

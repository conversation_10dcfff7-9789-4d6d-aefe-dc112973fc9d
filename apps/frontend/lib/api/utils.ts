/**
 * API工具函数 - 全栈架构
 * 🎯 核心价值：统一API响应处理，错误管理，验证逻辑
 * 🔄 标准化：一致的响应格式和错误处理
 * ⚡ 类型安全：完整的TypeScript支持
 * 📦 重构：从lib/api-utils.ts移动到根目录统一管理
 */

import { NextRequest, NextResponse } from 'next/server';
import { ApiResponse } from '@/lib/types/api';

// === API响应工具函数 ===

// 成功响应
export function successResponse<T>(data: T, message?: string): NextResponse<ApiResponse<T>> {
  return NextResponse.json({
    success: true,
    data,
    message,
    timestamp: Date.now(),
  });
}

// 错误响应
export function errorResponse(error: string, status: number = 400): NextResponse<ApiResponse> {
  return NextResponse.json({
    success: false,
    error,
    timestamp: Date.now(),
  }, { status });
}

// 验证错误响应
export function validationErrorResponse(errors: string[]): NextResponse<ApiResponse> {
  return NextResponse.json({
    success: false,
    error: '数据验证失败',
    errors,
    timestamp: Date.now(),
  }, { status: 400 });
}

// 未找到响应
export function notFoundResponse(resource: string = '资源'): NextResponse<ApiResponse> {
  return NextResponse.json({
    success: false,
    error: `${resource}未找到`,
    timestamp: Date.now(),
  }, { status: 404 });
}

// 未授权响应
export function unauthorizedResponse(message: string = '未授权访问'): NextResponse<ApiResponse> {
  return NextResponse.json({
    success: false,
    error: message,
    timestamp: Date.now(),
  }, { status: 401 });
}

// 禁止访问响应
export function forbiddenResponse(message: string = '禁止访问'): NextResponse<ApiResponse> {
  return NextResponse.json({
    success: false,
    error: message,
    timestamp: Date.now(),
  }, { status: 403 });
}

// 冲突响应
export function conflictResponse(message: string = '资源冲突'): NextResponse<ApiResponse> {
  return NextResponse.json({
    success: false,
    error: message,
    timestamp: Date.now(),
  }, { status: 409 });
}

// 服务器错误响应
export function serverErrorResponse(message: string = '服务器内部错误'): NextResponse<ApiResponse> {
  return NextResponse.json({
    success: false,
    error: message,
    timestamp: Date.now(),
  }, { status: 500 });
}

// === 请求处理工具 ===

// 解析请求体
export async function parseRequestBody<T>(request: NextRequest): Promise<T> {
  try {
    const body = await request.text();
    if (!body) {
      throw new Error('请求体为空');
    }
    return JSON.parse(body) as T;
  } catch (error) {
    throw new Error('请求体格式错误');
  }
}

// 解析查询参数
export function parseQueryParams(request: NextRequest): Record<string, string | string[]> {
  const { searchParams } = new URL(request.url);
  const params: Record<string, string | string[]> = {};
  
  for (const [key, value] of searchParams.entries()) {
    if (params[key]) {
      if (Array.isArray(params[key])) {
        (params[key] as string[]).push(value);
      } else {
        params[key] = [params[key] as string, value];
      }
    } else {
      params[key] = value;
    }
  }
  
  return params;
}

// 解析分页参数
export function parsePaginationParams(request: NextRequest): { page: number; limit: number; offset: number } {
  const { searchParams } = new URL(request.url);
  
  const page = Math.max(1, parseInt(searchParams.get('page') || '1', 10));
  const limit = Math.min(100, Math.max(1, parseInt(searchParams.get('limit') || '10', 10)));
  const offset = (page - 1) * limit;
  
  return { page, limit, offset };
}

// 验证必需字段
export function validateRequiredFields<T extends Record<string, any>>(
  data: T,
  requiredFields: (keyof T)[]
): string[] {
  const errors: string[] = [];
  
  for (const field of requiredFields) {
    if (data[field] === undefined || data[field] === null || data[field] === '') {
      errors.push(`字段 ${String(field)} 是必需的`);
    }
  }
  
  return errors;
}

// 验证邮箱格式
export function validateEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

// 验证手机号格式
export function validatePhone(phone: string): boolean {
  const phoneRegex = /^1[3-9]\d{9}$/;
  return phoneRegex.test(phone);
}

// 验证字符串长度
export function validateStringLength(
  value: string,
  fieldName: string,
  minLength: number,
  maxLength: number
): string[] {
  const errors: string[] = [];

  if (value.length < minLength) {
    errors.push(`${fieldName} 长度不能少于 ${minLength} 个字符`);
  }

  if (value.length > maxLength) {
    errors.push(`${fieldName} 长度不能超过 ${maxLength} 个字符`);
  }

  return errors;
}

// 验证必需字段（旧版本兼容）
export function validateRequired<T extends Record<string, any>>(
  data: T,
  requiredFields: (keyof T)[]
): string[] {
  return validateRequiredFields(data, requiredFields);
}

// 验证枚举值
export function validateEnum(
  value: any,
  fieldName: string,
  allowedValues: unknown[]
): string[] {
  const errors: string[] = [];

  if (!allowedValues.includes(value)) {
    errors.push(`${fieldName} 必须是以下值之一: ${allowedValues.join(', ')}`);
  }

  return errors;
}

// 验证数字范围
export function validateNumberRange(
  value: number,
  fieldName: string,
  min?: number,
  max?: number
): string[] {
  const errors: string[] = [];

  if (typeof value !== 'number' || isNaN(value)) {
    errors.push(`${fieldName} 必须是有效数字`);
    return errors;
  }

  if (min !== undefined && value < min) {
    errors.push(`${fieldName} 不能小于 ${min}`);
  }

  if (max !== undefined && value > max) {
    errors.push(`${fieldName} 不能大于 ${max}`);
  }

  return errors;
}

// 验证坐标格式
export function validateCoordinates(coordinates: string): string[] {
  const errors: string[] = [];

  // 坐标格式应该是 "A1", "B2" 等
  const coordinateRegex = /^[A-Z]\d+$/;

  if (!coordinateRegex.test(coordinates)) {
    errors.push('坐标格式无效，应该是字母+数字的格式（如：A1, B2）');
  }

  return errors;
}

// 获取查询参数（旧版本兼容）
export function getQueryParams(request: NextRequest): URLSearchParams {
  const { searchParams } = new URL(request.url);
  return searchParams;
}

// 验证密码强度
export function validatePassword(password: string): { valid: boolean; errors: string[] } {
  const errors: string[] = [];
  
  if (password.length < 8) {
    errors.push('密码长度至少8位');
  }
  
  if (!/[A-Z]/.test(password)) {
    errors.push('密码必须包含大写字母');
  }
  
  if (!/[a-z]/.test(password)) {
    errors.push('密码必须包含小写字母');
  }
  
  if (!/\d/.test(password)) {
    errors.push('密码必须包含数字');
  }
  
  if (!/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
    errors.push('密码必须包含特殊字符');
  }
  
  return {
    valid: errors.length === 0,
    errors,
  };
}

// === 错误处理工具 ===

// 包装异步API处理函数
export function withErrorHandling<T extends any[], R>(
  handler: (...args: T) => Promise<NextResponse<R>>
) {
  return async (...args: T): Promise<NextResponse<R | ApiResponse>> => {
    try {
      return await handler(...args);
    } catch (error) {
      process.env.NODE_ENV === 'development' && console.error('API处理错误:', error);
      
      if (error instanceof Error) {
        return serverErrorResponse(error.message);
      }
      
      return serverErrorResponse();
    }
  };
}

// 数据库错误处理
export function handleDatabaseError(error: unknown): NextResponse<ApiResponse> {
  process.env.NODE_ENV === 'development' && console.error('数据库错误:', error);

  // 类型保护：检查是否是对象且有相关属性
  const errorObj = error as any;

  // Prisma特定错误处理
  if (errorObj?.code === 'P2002') {
    return errorResponse('数据已存在，违反唯一性约束', 409);
  }

  if (errorObj?.code === 'P2025') {
    return notFoundResponse('记录');
  }

  if (errorObj?.code === 'P2003') {
    return errorResponse('外键约束违反', 400);
  }

  return serverErrorResponse('数据库操作失败');
}

// === 分页工具 ===

// 计算分页参数
export function calculatePagination(page: number, limit: number, total: number) {
  const totalPages = Math.ceil(total / limit);
  const offset = (page - 1) * limit;
  
  return {
    page,
    limit,
    total,
    totalPages,
    offset,
    hasNext: page < totalPages,
    hasPrev: page > 1,
  };
}

// 创建分页响应
export function createPaginatedResponse<T>(
  data: T[],
  pagination: ReturnType<typeof calculatePagination>
): NextResponse<ApiResponse<{ items: T[]; pagination: typeof pagination }>> {
  return successResponse({
    items: data,
    pagination,
  });
}

// === 缓存工具 ===

// 简单内存缓存
const cache = new Map<string, { data: any; expiry: number }>();

// 设置缓存
export function setCache(key: string, data: any, ttlMs: number = 300000): void {
  cache.set(key, {
    data,
    expiry: Date.now() + ttlMs,
  });
}

// 获取缓存
export function getCache<T>(key: string): T | null {
  const item = cache.get(key);
  
  if (!item) {
    return null;
  }
  
  if (Date.now() > item.expiry) {
    cache.delete(key);
    return null;
  }
  
  return item.data as T;
}

// 清除缓存
export function clearCache(key?: string): void {
  if (key) {
    cache.delete(key);
  } else {
    cache.clear();
  }
}

// 清理过期缓存
export function cleanupExpiredCache(): void {
  const now = Date.now();
  for (const [key, item] of cache.entries()) {
    if (now > item.expiry) {
      cache.delete(key);
    }
  }
}

// 定期清理过期缓存
setInterval(cleanupExpiredCache, 60000); // 每分钟清理一次

/**
 * Prisma客户端配置和工具函数
 * 🎯 核心价值：统一的数据库访问和连接管理
 * 📦 功能范围：Prisma客户端初始化、连接管理、错误处理
 * 🔄 架构设计：单例模式，支持连接池和事务管理
 */

import { PrismaClient } from '@prisma/client';

// 全局Prisma客户端实例
declare global {
  // eslint-disable-next-line no-var
  var __prisma: PrismaClient | undefined;
}

/**
 * 创建Prisma客户端实例
 */
function createPrismaClient(): PrismaClient {
  return new PrismaClient({
    log: process.env.NODE_ENV === 'development' ? ['query', 'error', 'warn'] : ['error'],
    errorFormat: 'pretty',
  });
}

/**
 * 获取Prisma客户端实例（单例模式）
 */
export const prisma = globalThis.__prisma || createPrismaClient();

// 在开发环境中保存实例到全局变量，避免热重载时重复创建
if (process.env.NODE_ENV === 'development') {
  globalThis.__prisma = prisma;
}

/**
 * 数据库连接状态检查
 */
export async function checkDatabaseConnection(): Promise<boolean> {
  try {
    await prisma.$queryRaw`SELECT 1`;
    return true;
  } catch (error) {
    process.env.NODE_ENV === 'development' && console.error('数据库连接失败:', error);
    return false;
  }
}

/**
 * 数据库健康检查
 */
export async function getDatabaseHealth(): Promise<{
  connected: boolean;
  version?: string;
  uptime?: number;
  error?: string;
}> {
  try {
    // 检查连接
    await prisma.$queryRaw`SELECT 1`;
    
    // 获取数据库版本
    const versionResult = await prisma.$queryRaw<Array<{ version: string }>>`SELECT version()`;
    const version = versionResult[0]?.version;
    
    return {
      connected: true,
      version,
      uptime: process.uptime(),
    };
  } catch (error) {
    return {
      connected: false,
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}

/**
 * 执行数据库事务
 */
export async function executeTransaction<T>(
  callback: (tx: Omit<PrismaClient, '$connect' | '$disconnect' | '$on' | '$transaction' | '$use' | '$extends'>) => Promise<T>
): Promise<T> {
  return await prisma.$transaction(callback);
}

/**
 * 批量操作工具
 */
export class BatchOperations {
  private operations: Array<() => Promise<any>> = [];
  
  /**
   * 添加操作到批次
   */
  add(operation: () => Promise<any>): void {
    this.operations.push(operation);
  }
  
  /**
   * 执行所有批次操作
   */
  async execute(): Promise<any[]> {
    return await Promise.all(this.operations.map(op => op()));
  }
  
  /**
   * 清空批次操作
   */
  clear(): void {
    this.operations = [];
  }
  
  /**
   * 获取操作数量
   */
  get count(): number {
    return this.operations.length;
  }
}

/**
 * 数据库迁移工具
 */
export async function runMigrations(): Promise<void> {
  try {
    // 在生产环境中，迁移应该通过部署脚本运行
    if (process.env.NODE_ENV === 'production') {
      console.warn('生产环境不应该自动运行迁移');
      return;
    }
    
    // 开发环境可以自动运行迁移
    process.env.NODE_ENV === 'development' && console.log('开始数据库迁移...');
    // 这里可以添加具体的迁移逻辑
    process.env.NODE_ENV === 'development' && console.log('数据库迁移完成');
  } catch (error) {
    process.env.NODE_ENV === 'development' && console.error('数据库迁移失败:', error);
    throw error;
  }
}

/**
 * 数据库种子数据
 */
export async function seedDatabase(): Promise<void> {
  try {
    process.env.NODE_ENV === 'development' && console.log('开始种子数据初始化...');
    
    // 检查是否已有数据 - 暂时跳过user表检查，因为schema中可能没有定义
    // const userCount = await prisma.user?.count() || 0;
    const userCount = 0; // 临时设置为0，允许种子数据初始化
    if (userCount > 0) {
      process.env.NODE_ENV === 'development' && console.log('数据库已有数据，跳过种子数据初始化');
      return;
    }
    
    // 这里可以添加种子数据的创建逻辑
    // 例如：创建默认用户、配置等
    
    process.env.NODE_ENV === 'development' && console.log('种子数据初始化完成');
  } catch (error) {
    process.env.NODE_ENV === 'development' && console.error('种子数据初始化失败:', error);
    throw error;
  }
}

/**
 * 清理数据库连接
 */
export async function disconnectDatabase(): Promise<void> {
  try {
    await prisma.$disconnect();
    process.env.NODE_ENV === 'development' && console.log('数据库连接已断开');
  } catch (error) {
    process.env.NODE_ENV === 'development' && console.error('断开数据库连接失败:', error);
  }
}

/**
 * 数据库查询性能监控
 */
export function createQueryMonitor() {
  const queryTimes: Array<{ query: string; duration: number; timestamp: number }> = [];
  
  return {
    /**
     * 记录查询时间
     */
    recordQuery(query: string, duration: number): void {
      queryTimes.push({
        query,
        duration,
        timestamp: Date.now(),
      });
      
      // 保持最近100条记录
      if (queryTimes.length > 100) {
        queryTimes.shift();
      }
    },
    
    /**
     * 获取查询统计
     */
    getStats() {
      if (queryTimes.length === 0) {
        return {
          totalQueries: 0,
          averageDuration: 0,
          slowestQuery: null,
          fastestQuery: null,
        };
      }
      
      const durations = queryTimes.map(q => q.duration);
      const totalDuration = durations.reduce((sum, d) => sum + d, 0);
      const averageDuration = totalDuration / durations.length;
      
      const slowestQuery = queryTimes.reduce((slowest, current) =>
        current.duration > slowest.duration ? current : slowest
      );
      
      const fastestQuery = queryTimes.reduce((fastest, current) =>
        current.duration < fastest.duration ? current : fastest
      );
      
      return {
        totalQueries: queryTimes.length,
        averageDuration,
        slowestQuery,
        fastestQuery,
      };
    },
    
    /**
     * 清空统计数据
     */
    clear(): void {
      queryTimes.length = 0;
    },
  };
}

/**
 * 数据库错误处理工具
 */
export function handlePrismaError(error: unknown): {
  message: string;
  code?: string;
  statusCode: number;
} {
  // 类型保护：检查是否是对象且有相关属性
  const errorObj = error as any;

  // Prisma错误处理
  if (errorObj?.code) {
    switch (errorObj.code) {
      case 'P2002':
        return {
          message: '数据已存在，违反唯一约束',
          code: errorObj.code,
          statusCode: 409,
        };
      case 'P2025':
        return {
          message: '记录未找到',
          code: errorObj.code,
          statusCode: 404,
        };
      case 'P2003':
        return {
          message: '外键约束失败',
          code: errorObj.code,
          statusCode: 400,
        };
      default:
        return {
          message: errorObj.message || '数据库操作失败',
          code: errorObj.code,
          statusCode: 500,
        };
    }
  }

  return {
    message: errorObj?.message || '未知数据库错误',
    statusCode: 500,
  };
}

/**
 * 健康检查函数
 */
export async function healthCheck(): Promise<{
  status: 'healthy' | 'unhealthy';
  database: boolean;
  timestamp: number;
}> {
  const isConnected = await checkDatabaseConnection();
  return {
    status: isConnected ? 'healthy' : 'unhealthy',
    database: isConnected,
    timestamp: Date.now(),
  };
}

// 导出默认实例
export default prisma;

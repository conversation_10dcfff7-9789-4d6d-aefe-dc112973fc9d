/**
 * API处理器工具函数
 * 🎯 核心价值：统一的API请求处理、错误处理、响应格式化
 * 📦 功能范围：请求处理、响应格式化、错误处理、中间件
 * 🔄 架构设计：函数式设计，支持中间件和装饰器模式
 */

import { NextRequest, NextResponse } from 'next/server';
import { handlePrismaError } from './prisma';
import { ApiResponse } from '@/lib/types/api';

// API错误接口
export interface ApiError {
  message: string;
  code?: string;
  statusCode: number;
  details?: any;
}

// 请求处理器类型
export type RequestHandler<T = any> = (
  request: NextRequest,
  context?: any
) => Promise<T>;

// 中间件类型
export type Middleware = (
  request: NextRequest,
  context: any,
  next: () => Promise<any>
) => Promise<any>;

/**
 * 创建成功响应
 */
export function createSuccessResponse<T>(
  data: T,
  message?: string,
  statusCode = 200
): NextResponse {
  const response: ApiResponse<T> = {
    success: true,
    data,
    message,
    timestamp: Date.now(),
  };

  return NextResponse.json(response, { status: statusCode });
}

/**
 * 创建错误响应
 */
export function createErrorResponse(
  error: string | ApiError,
  statusCode = 500
): NextResponse {
  const errorObj = typeof error === 'string' 
    ? { message: error, statusCode }
    : error;

  const response: ApiResponse = {
    success: false,
    error: errorObj.message,
    timestamp: Date.now(),
  };

  return NextResponse.json(response, { status: errorObj.statusCode });
}

/**
 * API处理器包装器
 */
export function withApiHandler<T>(
  handler: RequestHandler<T>,
  options: {
    methods?: string[];
    middlewares?: Middleware[];
    requireAuth?: boolean;
  } = {}
) {
  return async (request: NextRequest, context?: any) => {
    try {
      // 检查HTTP方法
      if (options.methods && !options.methods.includes(request.method)) {
        return createErrorResponse('方法不允许', 405);
      }

      // 执行中间件
      let middlewareContext = { ...context };
      if (options.middlewares) {
        for (const middleware of options.middlewares) {
          await middleware(request, middlewareContext, async () => {});
        }
      }

      // 身份验证检查
      if (options.requireAuth) {
        const authResult = await checkAuthentication(request);
        if (!authResult.success) {
          return createErrorResponse('未授权访问', 401);
        }
        middlewareContext.user = authResult.user;
      }

      // 执行处理器
      const result = await handler(request, middlewareContext);
      
      // 返回成功响应
      return createSuccessResponse(result);
    } catch (error) {
      process.env.NODE_ENV === 'development' && console.error('API处理器错误:', error);
      
      // 处理Prisma错误
      if (error && typeof error === 'object' && 'code' in error) {
        const prismaError = handlePrismaError(error);
        return createErrorResponse(prismaError, prismaError.statusCode);
      }
      
      // 处理其他错误
      const message = error instanceof Error ? error.message : '内部服务器错误';
      return createErrorResponse(message, 500);
    }
  };
}

/**
 * 身份验证检查
 */
async function checkAuthentication(request: NextRequest): Promise<{
  success: boolean;
  user?: any;
  error?: string;
}> {
  try {
    // 从请求头获取token
    const authorization = request.headers.get('authorization');
    if (!authorization || !authorization.startsWith('Bearer ')) {
      return { success: false, error: '缺少授权令牌' };
    }

    const token = authorization.substring(7);
    
    // 验证token（这里应该实现实际的token验证逻辑）
    // const user = await verifyToken(token);
    
    // 临时返回成功（实际项目中应该实现真实的验证）
    return { 
      success: true, 
      user: { id: 'user_1', username: 'test_user' } 
    };
  } catch (error) {
    return { 
      success: false, 
      error: error instanceof Error ? error.message : '身份验证失败' 
    };
  }
}

/**
 * 请求体解析中间件
 */
export const parseJsonMiddleware: Middleware = async (request, context, next) => {
  if (request.method === 'POST' || request.method === 'PUT' || request.method === 'PATCH') {
    try {
      const body = await request.json();
      context.body = body;
    } catch (error) {
      throw new Error('无效的JSON格式');
    }
  }
  await next();
};

/**
 * 请求验证中间件
 */
export function createValidationMiddleware(schema: unknown): Middleware {
  return async (request, context, next) => {
    if (context.body) {
      // 这里应该使用实际的验证库（如zod、joi等）
      // const result = schema.safeParse(context.body);
      // if (!result.success) {
      //   throw new Error('请求数据验证失败');
      // }
    }
    await next();
  };
}

/**
 * 速率限制中间件
 */
export function createRateLimitMiddleware(
  maxRequests: number,
  windowMs: number
): Middleware {
  const requests = new Map<string, { count: number; resetTime: number }>();

  return async (request, context, next) => {
    const clientIp = (request as any).ip || request.headers.get('x-forwarded-for') || 'unknown';
    const now = Date.now();
    
    const clientData = requests.get(clientIp);
    
    if (!clientData || now > clientData.resetTime) {
      // 重置或初始化计数器
      requests.set(clientIp, {
        count: 1,
        resetTime: now + windowMs,
      });
    } else {
      // 增加请求计数
      clientData.count++;
      
      if (clientData.count > maxRequests) {
        throw new Error('请求过于频繁，请稍后再试');
      }
    }
    
    await next();
  };
}

/**
 * 日志记录中间件
 */
export const loggingMiddleware: Middleware = async (request, context, next) => {
  const startTime = Date.now();
  const method = request.method;
  const url = request.url;
  
  process.env.NODE_ENV === 'development' && console.log(`[${new Date().toISOString()}] ${method} ${url} - 开始处理`);
  
  try {
    await next();
    const duration = Date.now() - startTime;
    process.env.NODE_ENV === 'development' && console.log(`[${new Date().toISOString()}] ${method} ${url} - 完成 (${duration}ms)`);
  } catch (error) {
    const duration = Date.now() - startTime;
    process.env.NODE_ENV === 'development' && console.error(`[${new Date().toISOString()}] ${method} ${url} - 错误 (${duration}ms):`, error);
    throw error;
  }
};

/**
 * CORS中间件
 */
export function createCorsMiddleware(options: {
  origin?: string | string[];
  methods?: string[];
  headers?: string[];
} = {}): Middleware {
  return async (request, context, next) => {
    const origin = request.headers.get('origin');
    const allowedOrigins = Array.isArray(options.origin) 
      ? options.origin 
      : options.origin ? [options.origin] : ['*'];
    
    if (allowedOrigins.includes('*') || (origin && allowedOrigins.includes(origin))) {
      context.corsHeaders = {
        'Access-Control-Allow-Origin': origin || '*',
        'Access-Control-Allow-Methods': (options.methods || ['GET', 'POST', 'PUT', 'DELETE']).join(', '),
        'Access-Control-Allow-Headers': (options.headers || ['Content-Type', 'Authorization']).join(', '),
      };
    }
    
    await next();
  };
}

/**
 * 缓存中间件
 */
export function createCacheMiddleware(
  ttl: number = 300 // 5分钟默认缓存时间
): Middleware {
  const cache = new Map<string, { data: any; expiresAt: number }>();

  return async (request, context, next) => {
    if (request.method !== 'GET') {
      await next();
      return;
    }

    const cacheKey = request.url;
    const now = Date.now();
    const cached = cache.get(cacheKey);

    if (cached && now < cached.expiresAt) {
      context.cachedData = cached.data;
      return;
    }

    await next();

    // 缓存响应数据
    if (context.responseData) {
      cache.set(cacheKey, {
        data: context.responseData,
        expiresAt: now + ttl * 1000,
      });
    }
  };
}

/**
 * 创建CRUD处理器
 */
export function createCrudHandlers<T>(options: {
  model: string;
  createSchema?: any;
  updateSchema?: any;
  querySchema?: any;
}) {
  return {
    // GET /api/resource
    list: withApiHandler(async (request) => {
      // 实现列表查询逻辑
      return { items: [], total: 0 };
    }),

    // GET /api/resource/:id
    get: withApiHandler(async (request, context) => {
      const id = context.params?.id;
      if (!id) {
        throw new Error('缺少资源ID');
      }
      // 实现单个资源查询逻辑
      return { id, data: {} };
    }),

    // POST /api/resource
    create: withApiHandler(async (request, context) => {
      const data = context.body;
      // 实现创建逻辑
      return { id: 'new_id', ...data };
    }, {
      methods: ['POST'],
      middlewares: [parseJsonMiddleware],
    }),

    // PUT /api/resource/:id
    update: withApiHandler(async (request, context) => {
      const id = context.params?.id;
      const data = context.body;
      if (!id) {
        throw new Error('缺少资源ID');
      }
      // 实现更新逻辑
      return { id, ...data };
    }, {
      methods: ['PUT'],
      middlewares: [parseJsonMiddleware],
    }),

    // DELETE /api/resource/:id
    delete: withApiHandler(async (request, context) => {
      const id = context.params?.id;
      if (!id) {
        throw new Error('缺少资源ID');
      }
      // 实现删除逻辑
      return { id, deleted: true };
    }, {
      methods: ['DELETE'],
    }),
  };
}

// 项目相关处理器
export const getAllProjects = withApiHandler(async (request) => {
  // 实现获取所有项目的逻辑
  return {
    items: [],
    total: 0,
    page: 1,
    limit: 10,
  };
});

export const createProject = withApiHandler(async (request, context) => {
  const data = context.body;
  // 实现创建项目的逻辑
  return {
    id: `project_${Date.now()}`,
    name: data.name,
    description: data.description,
    status: 'active',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    ...data,
  };
}, {
  methods: ['POST'],
  middlewares: [parseJsonMiddleware],
});

export const getProjectById = withApiHandler(async (request, context) => {
  const id = context.params?.id;
  if (!id) {
    throw new Error('缺少项目ID');
  }
  // 实现获取单个项目的逻辑
  return {
    id,
    name: `项目 ${id}`,
    description: '项目描述',
    status: 'active',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  };
});

export const updateProject = withApiHandler(async (request, context) => {
  const id = context.params?.id;
  const data = context.body;
  if (!id) {
    throw new Error('缺少项目ID');
  }
  // 实现更新项目的逻辑
  return {
    id,
    ...data,
    updatedAt: new Date().toISOString(),
  };
}, {
  methods: ['PUT'],
  middlewares: [parseJsonMiddleware],
});

export const deleteProject = withApiHandler(async (request, context) => {
  const id = context.params?.id;
  if (!id) {
    throw new Error('缺少项目ID');
  }
  // 实现删除项目的逻辑
  return {
    id,
    deleted: true,
    deletedAt: new Date().toISOString(),
  };
}, {
  methods: ['DELETE'],
});

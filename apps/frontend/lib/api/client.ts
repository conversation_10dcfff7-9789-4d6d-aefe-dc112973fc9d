/**
 * 统一API客户端 - 简化架构版本
 * 🎯 核心价值：统一API调用接口，错误处理，类型安全
 * 🔄 自动重试：网络错误自动重试机制
 * ⚡ 缓存支持：智能缓存和失效策略
 * 📦 重构：简化架构，减少重复代码，统一配置管理
 * 🕒 更新时间：2025年7月12日
 */

import {
  ApiResponse,
  ApiError,
  RequestConfig,
  PaginatedData as PaginatedResponse,
  Project,
  ColorData,
  GridData,
  ProjectSettings,
  CreateProjectRequest,
  ColorDataRequest,
  GridDataRequest,
  ProjectResponse,
  ColorDataResponse,
  GridDataResponse,
  ProjectSettingsRequest,
  ProjectSettingsResponse,
} from '@/lib/types';

import {
  ENV_CONFIG,
  HTTP_CONFIG,
  API_ENDPOINTS,
  ERROR_CONFIG,
  buildApiUrl,
  isRetryableError,
  getErrorMessage,
  isUsingFastAPI,
} from './config';

// 移除认证相关导入
// import { addAuthHeader, isAuthError, authManager } from '@/utils/auth';

// === 核心请求函数 ===
async function request<T>(
  endpoint: string,
  options: RequestInit & RequestConfig = {}
): Promise<T> {
  const {
    timeout = HTTP_CONFIG.DEFAULT_TIMEOUT,
    retries = HTTP_CONFIG.DEFAULT_RETRIES,
    cache = false,
    ...fetchOptions
  } = options;

  const url = buildApiUrl(endpoint);

  // 设置默认headers（已移除认证头）
  const headers = {
    ...HTTP_CONFIG.DEFAULT_HEADERS,
    ...fetchOptions.headers,
  };

  const config: RequestInit = {
    ...fetchOptions,
    headers,
  };

  // 添加超时控制
  const controller = new AbortController();
  const timeoutId = setTimeout(() => controller.abort(), timeout);
  config.signal = controller.signal;

  let lastError: Error;

  // 重试机制
  for (let attempt = 0; attempt <= retries; attempt++) {
    try {
      const response = await fetch(url, config);
      clearTimeout(timeoutId);

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));

        // 认证错误处理已移除

        throw ApiError.fromStatus(
          errorData.error || errorData.detail || getErrorMessage(response.status),
          response.status,
          errorData
        );
      }

      const data = await response.json();

      // 检查是否是标准的ApiResponse格式
      if (data && typeof data === 'object' && 'success' in data) {
        if (!data.success) {
          throw ApiError.fromStatus(data.error || '请求失败', 400, data);
        }
        return data.data as T;
      } else {
        // 直接返回数据（FastAPI端点的情况）
        return data as T;
      }
    } catch (error) {
      lastError = error as Error;

      // 检查是否可重试
      const isRetryable = error instanceof TypeError ||
        (error instanceof ApiError && isRetryableError(error.statusCode));

      if (attempt === retries || !isRetryable) {
        break;
      }

      // 指数退避重试
      const delay = HTTP_CONFIG.RETRY_DELAY_BASE * Math.pow(2, attempt);
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }

  clearTimeout(timeoutId);
  throw lastError!;
}

// === 统一HTTP方法 ===
export const httpClient = {
  get: async <T>(endpoint: string, config?: RequestConfig): Promise<T> => {
    return request<T>(endpoint, { method: 'GET', ...config });
  },

  post: async <T>(endpoint: string, data?: any, config?: RequestConfig): Promise<T> => {
    return request<T>(endpoint, {
      method: 'POST',
      body: data ? JSON.stringify(data) : undefined,
      ...config,
    });
  },

  put: async <T>(endpoint: string, data?: any, config?: RequestConfig): Promise<T> => {
    return request<T>(endpoint, {
      method: 'PUT',
      body: data ? JSON.stringify(data) : undefined,
      ...config,
    });
  },

  delete: async <T>(endpoint: string, config?: RequestConfig): Promise<T> => {
    return request<T>(endpoint, { method: 'DELETE', ...config });
  },

  patch: async <T>(endpoint: string, data?: any, config?: RequestConfig): Promise<T> => {
    return request<T>(endpoint, {
      method: 'PATCH',
      body: data ? JSON.stringify(data) : undefined,
      ...config,
    });
  },
};

// === 认证API（已移除） ===
// 认证相关功能已被移除

// === 用户API（已移除） ===
// 用户相关功能已被移除，因为后端不再支持用户管理

// === 项目API ===
export const projectApi = {
  getProjects: (): Promise<PaginatedResponse<ProjectResponse[]>> =>
    httpClient.get<PaginatedResponse<ProjectResponse[]>>('/projects'),

  getProject: (id: string): Promise<ProjectResponse> =>
    httpClient.get<ProjectResponse>(`/projects/${id}`),

  createProject: (data: CreateProjectRequest): Promise<ProjectResponse> =>
    httpClient.post<ProjectResponse>('/projects', data),

  updateProject: (id: string, data: Partial<CreateProjectRequest>): Promise<ProjectResponse> =>
    httpClient.put<ProjectResponse>(`/projects/${id}`, data),

  deleteProject: (id: string): Promise<void> =>
    httpClient.delete<void>(`/projects/${id}`),
};

// === 颜色数据API ===
export const colorDataApi = {
  getColorData: (projectId: string): Promise<ColorDataResponse> =>
    httpClient.get<ColorDataResponse>(`/data/colors?project_id=${projectId}`),

  updateColorData: (projectId: string, data: ColorDataRequest): Promise<ColorDataResponse> =>
    httpClient.put<ColorDataResponse>(`/data/colors`, { ...data, project_id: projectId }),
};

// === 网格数据API ===
export const gridDataApi = {
  getGridData: (projectId: string): Promise<GridDataResponse> =>
    httpClient.get<GridDataResponse>(`/data/grid?project_id=${projectId}`),

  updateGridData: (projectId: string, data: GridDataRequest): Promise<GridDataResponse> =>
    httpClient.put<GridDataResponse>(`/data/grid`, { ...data, project_id: projectId }),
};

// === 项目设置API ===
export const projectSettingsApi = {
  getSettings: (projectId: string): Promise<ProjectSettingsResponse> =>
    httpClient.get<ProjectSettingsResponse>(`/data/settings?project_id=${projectId}`),

  updateSettings: (projectId: string, data: ProjectSettingsRequest): Promise<ProjectSettingsResponse> =>
    httpClient.put<ProjectSettingsResponse>(`/data/settings`, { ...data, project_id: projectId }),
};



// === 工具函数 ===

// 检查网络连接
export async function checkConnection(): Promise<boolean> {
  try {
    if (isUsingFastAPI()) {
      // FastAPI后端健康检查
      const response = await fetch(`${ENV_CONFIG.BACKEND_URL}/health`, {
        method: 'GET',
        headers: { 'Content-Type': 'application/json' }
      });
      const data = await response.json();
      return data.status === 'healthy';
    } else {
      // Next.js API健康检查
      await request('/health', { timeout: 5000 });
      return true;
    }
  } catch (error) {
    console.warn('API连接检查失败:', error);
    return false;
  }
}

// 批量请求
export async function batchRequest<T>(
  requests: Array<() => Promise<T>>,
  options: { concurrency?: number; failFast?: boolean } = {}
): Promise<T[]> {
  const { concurrency = 5, failFast = false } = options;
  const results: T[] = [];
  const errors: Error[] = [];
  
  for (let i = 0; i < requests.length; i += concurrency) {
    const batch = requests.slice(i, i + concurrency);
    
    try {
      const batchResults = await Promise.allSettled(
        batch.map(request => request())
      );
      
      for (const result of batchResults) {
        if (result.status === 'fulfilled') {
          results.push(result.value);
        } else {
          errors.push(result.reason);
          if (failFast) {
            throw result.reason;
          }
        }
      }
    } catch (error) {
      if (failFast) {
        throw error;
      }
      errors.push(error as Error);
    }
  }
  
  if (errors.length > 0 && results.length === 0) {
    throw new Error(`所有请求都失败了: ${errors.map(e => e.message).join(', ')}`);
  }
  
  return results;
}

// === 兼容性API客户端 ===
// 为了保持与现有feature代码的兼容性，提供包装的API客户端
export const apiClient = {
  get: async <T>(url: string): Promise<{ data: T }> => {
    const result = await httpClient.get<T>(url);
    return { data: result };
  },
  post: async <T>(url: string, data?: any): Promise<{ data: T }> => {
    const result = await httpClient.post<T>(url, data);
    return { data: result };
  },
  put: async <T>(url: string, data?: any): Promise<{ data: T }> => {
    const result = await httpClient.put<T>(url, data);
    return { data: result };
  },
  delete: async <T>(url: string): Promise<{ data: T }> => {
    const result = await httpClient.delete<T>(url);
    return { data: result };
  },
};

// === 统一API导出 ===
export const api = {
  // HTTP客户端
  http: httpClient,

  // 业务API（已移除认证API和用户API）
  project: projectApi,
  colorData: colorDataApi,
  gridData: gridDataApi,
  projectSettings: projectSettingsApi,

  // 工具函数
  checkConnection,
  batchRequest,
};

// 默认导出统一的HTTP客户端
export default httpClient;

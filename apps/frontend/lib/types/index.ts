/**
 * 类型定义统一导出
 * 🎯 核心价值：集中管理所有类型定义，提供统一的导入入口
 * 📦 功能范围：基础类型、业务类型、组件类型、API类型
 * 🔄 架构设计：模块化类型定义，支持类型复用和扩展
 * 📍 迁移：合并types/index.ts和types/api.ts的内容
 */

// === 从其他模块导出 ===
export * from './api';
export * from './grid';
export * from './color';
// 避免重复导出，matrix中的CellData和ColorLevel与其他模块冲突
export type {
  MatrixData,
  BasicColorType as MatrixBasicColorType,
  GroupType
} from './matrix';

// === 基础类型定义 ===
export interface BaseEntity {
  id: string;
  createdAt: number;
  updatedAt: number;
}

// === 错误类型 ===
export interface ErrorInfo {
  code: string;
  message: string;
  details?: any;
  stack?: string;
}

// === 配置类型 ===
export interface AppConfig {
  apiUrl: string;
  version: string;
  environment: 'development' | 'production' | 'test';
  features: FeatureFlags;
}

export interface FeatureFlags {
  enableDebugMode: boolean;
  enableExperimentalFeatures: boolean;
  enableAnalytics: boolean;
  enablePerformanceMonitoring: boolean;
}

// === 事件类型 ===
export interface AppEvent {
  type: string;
  payload: any;
  timestamp: number;
  source: string;
}

// === 状态类型 ===
export interface LoadingState {
  isLoading: boolean;
  error: string | null;
  lastUpdated: number | null;
}

// === 表单类型 ===
export interface FormField {
  name: string;
  label: string;
  type: 'text' | 'number' | 'email' | 'password' | 'select' | 'checkbox' | 'radio';
  value: any;
  error?: string;
  required?: boolean;
  disabled?: boolean;
  placeholder?: string;
  options?: Array<{ label: string; value: any }>;
}

export interface FormState {
  fields: Record<string, FormField>;
  isValid: boolean;
  isDirty: boolean;
  isSubmitting: boolean;
  errors: Record<string, string>;
}

// === 通知类型 ===
export interface NotificationAction {
  label: string;
  action: () => void;
  style?: 'primary' | 'secondary';
}

// === 模态框类型 ===
export interface ModalConfig {
  id: string;
  title: string;
  content: React.ReactNode;
  size?: 'sm' | 'md' | 'lg' | 'xl';
  closable?: boolean;
  maskClosable?: boolean;
  actions?: ModalAction[];
}

export interface ModalAction {
  label: string;
  action: () => void;
  style?: 'primary' | 'secondary' | 'danger';
  loading?: boolean;
  disabled?: boolean;
}

// === 路由类型 ===
export interface RouteConfig {
  path: string;
  component: React.ComponentType;
  exact?: boolean;
  guards?: RouteGuard[];
  meta?: RouteMeta;
}

export interface RouteGuard {
  name: string;
  check: () => boolean | Promise<boolean>;
  redirect?: string;
}

export interface RouteMeta {
  title?: string;
  description?: string;
  keywords?: string[];
  requiresAuth?: boolean;
  roles?: string[];
}

// === 权限类型 ===
export interface Permission {
  id: string;
  name: string;
  description: string;
  resource: string;
  action: string;
}

export interface Role {
  id: string;
  name: string;
  description: string;
  permissions: Permission[];
}

// === 文件类型 ===
export interface FileInfo {
  id: string;
  name: string;
  size: number;
  type: string;
  url: string;
  uploadedAt: number;
  uploadedBy: string;
}

// === 搜索类型 ===
export interface SearchParams {
  query: string;
  filters: Record<string, any>;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  page?: number;
  limit?: number;
}

export interface SearchResult<T> {
  items: T[];
  total: number;
  query: string;
  took: number;
  suggestions?: string[];
}

// === 缓存类型 ===
export interface CacheEntry<T> {
  key: string;
  value: T;
  expiresAt: number;
  createdAt: number;
}

// === 性能监控类型 ===
export interface PerformanceMetrics {
  renderTime: number;
  loadTime: number;
  memoryUsage: number;
  networkRequests: number;
  errorCount: number;
  timestamp: number;
}

// === 主题类型 ===
export interface ThemeConfig {
  name: string;
  colors: Record<string, string>;
  fonts: Record<string, string>;
  spacing: Record<string, string>;
  breakpoints: Record<string, string>;
}

// === 国际化类型 ===
export interface I18nConfig {
  locale: string;
  fallbackLocale: string;
  messages: Record<string, string>;
}

// === 工具类型 ===
export type Optional<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;
export type RequiredFields<T, K extends keyof T> = T & Required<Pick<T, K>>;
export type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P];
};
export type DeepRequired<T> = {
  [P in keyof T]-?: T[P] extends object ? DeepRequired<T[P]> : T[P];
};

// === 函数类型 ===
export type EventHandler<T = any> = (event: T) => void;
export type AsyncEventHandler<T = any> = (event: T) => Promise<void>;
export type Callback<T = void> = () => T;
export type AsyncCallback<T = void> = () => Promise<T>;

// === 状态管理类型 ===
export interface StoreState {
  [key: string]: any;
}

export interface StoreActions {
  [key: string]: (...args: unknown[]) => any;
}

export interface Store<S extends StoreState, A extends StoreActions> {
  state: S;
  actions: A;
  subscribe: (listener: (state: S) => void) => () => void;
  getState: () => S;
}

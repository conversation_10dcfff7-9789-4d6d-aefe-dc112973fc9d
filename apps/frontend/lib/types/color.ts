// 基础颜色类型定义 - 从constants/colors.ts合并
export type ColorType = 'red' | 'cyan' | 'yellow' | 'purple' | 'orange' | 'green' | 'blue' | 'pink';
export type BasicColorType = ColorType | 'black';

export interface CoordinateGroup {
  coords: number[];  // 修改为更灵活的数组类型
  group: number | null;
}

export interface ColorCoordinates {
  level1: CoordinateGroup[];
  level2?: CoordinateGroup[];  // 某些颜色可能没有level2
  level3: CoordinateGroup[];
  level4: CoordinateGroup[];
}

// 统一颜色信息接口
export interface ColorInfo {
  exists: boolean;
  level: 1 | 2 | 3 | 4;
  group: number | null;
  colorType: ColorType;
}

// 全量颜色信息接口
export interface AllColorInfo {
  black?: { exists: boolean; letter: string };
  red?: ColorInfo;
  orange?: ColorInfo;
  cyan?: ColorInfo;
  yellow?: ColorInfo;
  purple?: ColorInfo;
  green?: ColorInfo;
  blue?: ColorInfo;
  pink?: ColorInfo;
}

// 颜色显示控制状态
export interface ColorDisplayState {
  showCells: boolean;
  showLevel1: boolean;
  showLevel2: boolean;
  showLevel3: boolean;
  showLevel4: boolean;
  selectedGroups: Set<number>;
}

// 颜色CSS映射类型 - 从constants/colors.ts提取
export interface ColorCssMap {
  text: string;
  border: string;
  bg: string;
  hoverBg: string;
  hoverText: string;
  level1: string;
  level2: string;
  level3: string;
  level4: string;
}

// 颜色级别类型
export type ColorLevel = 1 | 2 | 3 | 4;

// 颜色状态类型
export interface ColorState {
  selected: BasicColorType | null;
  hover: BasicColorType | null;
  active: BasicColorType | null;
}

// 颜色映射值类型
export interface ColorMappingValue {
  color: BasicColorType;
  level: ColorLevel;
  priority: number;
}

// 灰度模式颜色激活状态
export interface GrayModeColorState {
  [colorName: string]: boolean;
}

// 灰度模式配置
export interface GrayModeColorConfig {
  activeColors: Set<ColorType>;
  defaultGrayColor: string;
  activationTransitionDuration: number;
}
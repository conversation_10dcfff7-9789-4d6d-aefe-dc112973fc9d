/**
 * API相关类型定义
 * 🎯 核心价值：统一API请求和响应的类型定义
 * 📦 功能范围：请求类型、响应类型、错误类型、状态类型
 * 🔄 架构设计：类型安全的API接口定义
 * 📍 迁移：从types/api.ts迁移到lib/types/api.ts
 */

// 基础API响应类型
export interface BaseApiResponse {
  success: boolean;
  timestamp: number;
  message?: string;
}

// 成功响应类型
export interface SuccessResponse<T = any> extends BaseApiResponse {
  success: true;
  data: T;
}

// 错误响应类型
export interface ErrorResponse extends BaseApiResponse {
  success: false;
  error: string;
  code?: string;
  details?: any;
}

// 联合响应类型
export type ApiResponse<T = any> = SuccessResponse<T> | ErrorResponse;

// 分页请求参数
export interface PaginationRequest {
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

// 分页响应数据
export interface PaginatedData<T> {
  items: T[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
  hasNext: boolean;
  hasPrev: boolean;
}



// 项目相关类型
export interface Project {
  id: string;
  name: string;
  description?: string;
  status: 'active' | 'inactive' | 'archived';
  createdAt: string;
  updatedAt: string;
  owner: {
    id: string;
    name: string;
    email: string;
  };
  settings: ProjectSettings;
}

export interface ProjectSettings {
  theme: 'light' | 'dark' | 'auto';
  language: string;
  timezone: string;
  notifications: boolean;
  features: string[];
}

export interface CreateProjectRequest {
  name: string;
  description?: string;
  settings?: Partial<ProjectSettings>;
}

export interface UpdateProjectRequest {
  name?: string;
  description?: string;
  status?: Project['status'];
  settings?: Partial<ProjectSettings>;
}

// 健康检查类型
export interface HealthCheckResponse {
  status: 'healthy' | 'unhealthy' | 'degraded';
  timestamp: number;
  services: {
    database: {
      status: 'up' | 'down';
      responseTime?: number;
      error?: string;
    };
    cache?: {
      status: 'up' | 'down';
      responseTime?: number;
      error?: string;
    };
    external?: Array<{
      name: string;
      status: 'up' | 'down';
      responseTime?: number;
      error?: string;
    }>;
  };
  version: string;
  uptime: number;
}

// 用户相关类型
export interface User {
  id: string;
  username: string;
  email: string;
  firstName?: string;
  lastName?: string;
  avatar?: string;
  role: 'admin' | 'user' | 'guest';
  status: 'active' | 'inactive' | 'suspended';
  createdAt: string;
  updatedAt: string;
  lastLoginAt?: string;
  preferences: UserPreferences;
}

export interface UserPreferences {
  theme: 'light' | 'dark' | 'auto';
  language: string;
  timezone: string;
  notifications: {
    email: boolean;
    push: boolean;
    sms: boolean;
  };
  privacy: {
    profileVisible: boolean;
    activityVisible: boolean;
  };
}

export interface CreateUserRequest {
  username: string;
  email: string;
  password: string;
  firstName?: string;
  lastName?: string;
  role?: User['role'];
}

export interface UpdateUserRequest {
  username?: string;
  email?: string;
  firstName?: string;
  lastName?: string;
  avatar?: string;
  role?: User['role'];
  status?: User['status'];
  preferences?: Partial<UserPreferences>;
}

// 认证相关类型
export interface LoginRequest {
  email: string;
  password: string;
  rememberMe?: boolean;
}

export interface LoginResponse {
  success: boolean;
  user: User;
  token: string;
  refreshToken: string;
  expiresAt: string;
}

export interface RefreshTokenRequest {
  refreshToken: string;
}

export interface RefreshTokenResponse {
  success: boolean;
  token: string;
  expiresAt: string;
}

// 文件上传类型
export interface FileUploadRequest {
  file: File;
  category?: string;
  tags?: string[];
  metadata?: Record<string, any>;
}

export interface FileUploadResponse {
  success: boolean;
  file: {
    id: string;
    name: string;
    originalName: string;
    size: number;
    mimeType: string;
    url: string;
    thumbnailUrl?: string;
    uploadedAt: string;
  };
}

// 搜索相关类型
export interface SearchRequest {
  query: string;
  filters?: Record<string, any>;
  facets?: string[];
  highlight?: boolean;
  page?: number;
  limit?: number;
}

export interface SearchResponse<T = any> {
  success: boolean;
  results: T[];
  total: number;
  took: number;
  facets?: Record<string, Array<{
    value: string;
    count: number;
  }>>;
  suggestions?: string[];
}

// 批量操作类型
export interface BatchRequest<T = any> {
  operation: 'create' | 'update' | 'delete';
  items: T[];
  options?: {
    continueOnError?: boolean;
    validateOnly?: boolean;
  };
}

export interface BatchResponse<T = any> {
  success: boolean;
  results: Array<{
    success: boolean;
    item?: T;
    error?: string;
    index: number;
  }>;
  summary: {
    total: number;
    successful: number;
    failed: number;
  };
}

// 导出/导入类型
export interface ExportRequest {
  format: 'json' | 'csv' | 'xlsx';
  filters?: Record<string, any>;
  fields?: string[];
}

export interface ExportResponse {
  success: boolean;
  downloadUrl: string;
  filename: string;
  size: number;
  expiresAt: string;
}

export interface ImportRequest {
  file: File;
  format: 'json' | 'csv' | 'xlsx';
  options?: {
    skipHeader?: boolean;
    delimiter?: string;
    encoding?: string;
    validateOnly?: boolean;
  };
}

export interface ImportResponse {
  success: boolean;
  summary: {
    total: number;
    imported: number;
    skipped: number;
    errors: number;
  };
  errors?: Array<{
    row: number;
    field?: string;
    message: string;
  }>;
}

// WebSocket消息类型
export interface WebSocketMessage<T = any> {
  type: string;
  payload: T;
  timestamp: number;
  id?: string;
}

// 通知类型
export interface Notification {
  id: string;
  type: 'info' | 'success' | 'warning' | 'error';
  title: string;
  message: string;
  data?: any;
  read: boolean;
  createdAt: string;
  expiresAt?: string;
}

// 系统配置类型
export interface SystemConfig {
  maintenance: {
    enabled: boolean;
    message?: string;
    startTime?: string;
    endTime?: string;
  };
  features: Record<string, boolean>;
  limits: {
    maxFileSize: number;
    maxRequestSize: number;
    rateLimit: number;
  };
  integrations: Record<string, {
    enabled: boolean;
    config: Record<string, any>;
  }>;
}

// 缺失的API类型定义
export class ApiError extends Error {
  public code?: string;
  public statusCode: number;
  public details?: any;
  public type?: string;

  constructor(
    typeOrMessage: string,
    messageOrStatusCode?: string | number,
    statusCodeOrDetails?: number | any,
    details?: any
  ) {
    // 处理不同的构造函数重载
    if (typeof messageOrStatusCode === 'string') {
      // ApiError(type, message, statusCode, details)
      super(messageOrStatusCode);
      this.type = typeOrMessage;
      this.statusCode = statusCodeOrDetails as number || 500;
      this.details = details;
    } else {
      // ApiError(message, statusCode, details)
      super(typeOrMessage);
      this.statusCode = messageOrStatusCode as number || 500;
      this.details = statusCodeOrDetails;
    }

    this.name = 'ApiError';
  }

  static fromStatus(message: string, statusCode: number, details?: any): ApiError {
    return new ApiError(message, statusCode, details);
  }
}

export interface RequestConfig {
  timeout?: number;
  retries?: number;
  headers?: Record<string, string>;
}

// 数据类型
export interface ColorData {
  id: string;
  color: string;
  value: number;
  createdAt: string;
  updatedAt: string;
}

export interface GridData {
  id: string;
  rows: number;
  cols: number;
  cells: unknown[];
  createdAt: string;
  updatedAt: string;
}



// 请求类型
export interface ColorDataRequest {
  color: string;
  value: number;
}

export interface GridDataRequest {
  rows: number;
  cols: number;
  cells: unknown[];
}

export interface ProjectSettingsRequest {
  settings: Partial<ProjectSettings>;
}

// 响应类型
export interface ProjectResponse {
  success: boolean;
  data: Project;
  message?: string;
  timestamp: number;
}

export interface ColorDataResponse {
  success: boolean;
  data: ColorData;
  message?: string;
  timestamp: number;
}

export interface GridDataResponse {
  success: boolean;
  data: GridData;
  message?: string;
  timestamp: number;
}

export interface ProjectSettingsResponse {
  success: boolean;
  data: ProjectSettings;
  message?: string;
  timestamp: number;
}

/**
 * 验证Schema定义
 * 🎯 核心价值：统一的数据验证规则
 * 📦 功能：Zod schema定义、类型推导
 * ⚡ 类型安全：完整的TypeScript支持
 */

import { z } from 'zod';

// === 基础验证规则 ===

export const StringValidation = {
  required: z.string().min(1, '此字段为必填项'),
  optional: z.string().optional(),
  email: z.string().email('请输入有效的邮箱地址'),
  phone: z.string().regex(/^1[3-9]\d{9}$/, '请输入有效的手机号码'),
  url: z.string().url('请输入有效的URL地址'),
  password: z.string()
    .min(8, '密码至少8位')
    .regex(/^(?=.*[A-Za-z])(?=.*\d)/, '密码需包含字母和数字'),
  slug: z.string().regex(/^[a-z0-9-]+$/, '只能包含小写字母、数字和连字符'),
  uuid: z.string().uuid('无效的UUID格式'),
};

export const NumberValidation = {
  required: z.number().min(0, '数值不能为负'),
  optional: z.number().optional(),
  positive: z.number().positive('数值必须为正数'),
  integer: z.number().int('必须为整数'),
  percentage: z.number().min(0).max(100, '百分比必须在0-100之间'),
  coordinate: z.number().int().min(0).max(99, '坐标必须在0-99之间'),
};

export const ArrayValidation = {
  required: z.array(z.any()).min(1, '至少需要一个项目'),
  optional: z.array(z.any()).optional(),
  nonEmpty: z.array(z.any()).nonempty('数组不能为空'),
};

// === 项目相关Schema ===

export const ProjectSchema = z.object({
  id: StringValidation.uuid.optional(),
  name: z.string().min(1, '此字段为必填项').max(100, '项目名称最多100个字符'),
  description: z.string().max(500, '描述最多500个字符').optional(),
  slug: StringValidation.slug,
  status: z.enum(['active', 'inactive', 'archived']).default('active'),
  createdAt: z.date().optional(),
  updatedAt: z.date().optional(),
});

export type Project = z.infer<typeof ProjectSchema>;

const CreateProjectSchema = ProjectSchema.omit({ id: true, createdAt: true, updatedAt: true });
export type CreateProjectRequest = z.infer<typeof CreateProjectSchema>;

const UpdateProjectSchema = ProjectSchema.partial().omit({ id: true, createdAt: true });
export type UpdateProjectRequest = z.infer<typeof UpdateProjectSchema>;

// === 颜色相关Schema ===

export const ColorSchema = z.object({
  type: z.enum(['rgb', 'hex', 'hsl', 'hsv']),
  value: StringValidation.required,
  alpha: NumberValidation.percentage.optional(),
});

export const ColorCoordinatesSchema = z.object({
  x: NumberValidation.coordinate,
  y: NumberValidation.coordinate,
  color: ColorSchema,
});

export const ColorInfoSchema = z.object({
  id: StringValidation.uuid.optional(),
  name: z.string().min(1, '此字段为必填项').max(50, '颜色名称最多50个字符'),
  coordinates: ColorCoordinatesSchema,
  metadata: z.record(z.any()).optional(),
});

export const ColorDataSchema = z.object({
  colors: z.array(ColorInfoSchema),
  groups: z.array(z.object({
    id: StringValidation.uuid,
    name: StringValidation.required,
    colorIds: z.array(StringValidation.uuid),
  })).optional(),
});

export type ColorType = z.infer<typeof ColorSchema>;
export type ColorCoordinates = z.infer<typeof ColorCoordinatesSchema>;
export type ColorInfo = z.infer<typeof ColorInfoSchema>;
export type ColorData = z.infer<typeof ColorDataSchema>;

// === 网格相关Schema ===

export const GridSchema = z.object({
  size: z.number().int().min(1).max(100),
  displayMode: z.enum(['value', 'coordinates', 'color']),
  cellShape: z.enum(['square', 'rounded']),
  gap: z.number().min(0).max(20),
  padding: z.number().min(0).max(50),
  fontSize: z.number().min(8).max(24),
  scale: z.object({
    enabled: z.boolean(),
    factor: z.number().min(0.1).max(5),
  }),
  animation: z.object({
    enabled: z.boolean(),
    duration: z.number().min(100).max(2000),
  }),
});

export const GridDataSchema = z.object({
  cells: z.array(z.object({
    x: NumberValidation.coordinate,
    y: NumberValidation.coordinate,
    value: z.any(),
    color: ColorSchema.optional(),
    metadata: z.record(z.any()).optional(),
  })),
  config: GridSchema,
  metadata: z.record(z.any()).optional(),
});

export type GridConfig = z.infer<typeof GridSchema>;
export type GridData = z.infer<typeof GridDataSchema>;

// === 表单相关Schema ===

export const SearchFormSchema = z.object({
  query: StringValidation.optional,
  filters: z.record(z.any()).optional(),
  sort: z.object({
    field: StringValidation.required,
    order: z.enum(['asc', 'desc']),
  }).optional(),
});

export const PaginationParamsSchema = z.object({
  page: NumberValidation.integer.min(1).default(1),
  limit: NumberValidation.integer.min(1).max(100).default(20),
  offset: NumberValidation.integer.min(0).optional(),
});

export const FileUploadFormSchema = z.object({
  file: z.instanceof(File, { message: '请选择文件' }),
  name: StringValidation.optional,
  description: StringValidation.optional,
  tags: z.array(StringValidation.required).optional(),
});

export type SearchForm = z.infer<typeof SearchFormSchema>;
export type PaginationParams = z.infer<typeof PaginationParamsSchema>;
export type FileUploadForm = z.infer<typeof FileUploadFormSchema>;

// === 表单验证Schema ===

export const FormSchema = {
  search: SearchFormSchema,
  pagination: PaginationParamsSchema,
  fileUpload: FileUploadFormSchema,
  project: ProjectSchema,
  color: ColorSchema,
  grid: GridSchema,
};

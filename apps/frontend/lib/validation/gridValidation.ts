/**
 * 网格验证函数
 * 🎯 职责：网格系统相关的验证逻辑
 * 📦 功能范围：坐标验证、配置验证、数据完整性检查
 * 🔄 重构说明：从原grid-system/utils/validation.ts迁移通用验证逻辑
 */

import type { CellData, GridUIConfig } from '@/lib/types/grid';
import type { GridBusinessConfig } from '@/features/grid-system/types';
import { 
  GRID_CONSTANTS, 
  VALIDATION_RULES,
  GRID_DISPLAY_MODES,
  CELL_SHAPES,
  INTERACTION_MODES,
  SELECTION_MODES,
} from '@/stores/constants/grid';

// 验证结果接口
export interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

// 详细验证结果接口
export interface DetailedValidationResult extends ValidationResult {
  field: string;
  value: any;
  expectedRange?: { min: number; max: number };
  allowedValues?: readonly string[];
}

/**
 * 验证坐标是否在有效范围内
 * @param x X坐标
 * @param y Y坐标
 * @returns 验证结果
 */
export const validateCoordinates = (x: number, y: number): ValidationResult => {
  const errors: string[] = [];
  const warnings: string[] = [];

  // 检查坐标类型
  if (typeof x !== 'number' || typeof y !== 'number') {
    errors.push('坐标必须是数字类型');
    return { isValid: false, errors, warnings };
  }

  // 检查坐标是否为整数
  if (!Number.isInteger(x) || !Number.isInteger(y)) {
    errors.push('坐标必须是整数');
  }

  // 检查坐标范围
  const { min, max } = VALIDATION_RULES.COORDINATES;
  if (x < min || x > max) {
    errors.push(`X坐标超出范围：${x}，有效范围：[${min}, ${max}]`);
  }
  if (y < min || y > max) {
    errors.push(`Y坐标超出范围：${y}，有效范围：[${min}, ${max}]`);
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
  };
};

/**
 * 验证单元格数据的完整性
 * @param cell 单元格数据
 * @returns 验证结果
 */
export const validateCellData = (cell: CellData): ValidationResult => {
  const errors: string[] = [];
  const warnings: string[] = [];

  // 检查必需字段
  const requiredFields = ['id', 'row', 'col', 'x', 'y', 'index', 'color'];
  for (const field of requiredFields) {
    if (!(field in cell) || cell[field as keyof CellData] === undefined) {
      errors.push(`缺少必需字段：${field}`);
    }
  }

  // 验证坐标
  const coordValidation = validateCoordinates(cell.x, cell.y);
  if (!coordValidation.isValid) {
    errors.push(...coordValidation.errors);
  }

  // 验证颜色格式
  if (cell.color && typeof cell.color === 'string') {
    const colorRegex = /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/;
    if (!colorRegex.test(cell.color)) {
      warnings.push(`颜色格式可能不正确：${cell.color}`);
    }
  }

  // 验证级别
  if (typeof cell.level === 'number' && cell.level < 1) {
    errors.push(`级别必须大于0：${cell.level}`);
  }

  // 验证索引
  if (typeof cell.index === 'number' && cell.index < 0) {
    errors.push(`索引必须非负：${cell.index}`);
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
  };
};

/**
 * 验证UI配置
 * @param config UI配置
 * @returns 验证结果
 */
export const validateUIConfig = (config: Partial<GridUIConfig>): ValidationResult => {
  const errors: string[] = [];
  const warnings: string[] = [];

  // 验证单元格大小 - 检查扩展属性
  if ('size' in config && config.size !== undefined) {
    const { min, max } = VALIDATION_RULES.CELL_SIZE;
    if (config.size < min || config.size > max) {
      errors.push(`单元格大小超出范围：${config.size}，有效范围：[${min}, ${max}]`);
    }
  }

  // 验证字体大小
  if (config.fontSize !== undefined) {
    const { min, max } = VALIDATION_RULES.FONT_SIZE;
    if (config.fontSize < min || config.fontSize > max) {
      errors.push(`字体大小超出范围：${config.fontSize}，有效范围：[${min}, ${max}]`);
    }
  }

  // 验证单元格形状
  if (config.cellShape !== undefined) {
    const validShapes = Object.values(CELL_SHAPES);
    if (!validShapes.includes(config.cellShape as any)) {
      errors.push(`无效的单元格形状：${config.cellShape}，有效值：${validShapes.join(', ')}`);
    }
  }

  // 验证显示模式
  if (config.displayMode !== undefined) {
    const validModes = Object.values(GRID_DISPLAY_MODES);
    if (!validModes.includes(config.displayMode as any)) {
      errors.push(`无效的显示模式：${config.displayMode}，有效值：${validModes.join(', ')}`);
    }
  }

  // 验证动画配置 - 检查扩展属性
  if ('animation' in config && config.animation !== undefined) {
    if (config.animation.duration !== undefined) {
      const { min, max } = VALIDATION_RULES.ANIMATION_DURATION;
      if (config.animation.duration < min || config.animation.duration > max) {
        errors.push(`动画持续时间超出范围：${config.animation.duration}，有效范围：[${min}, ${max}]`);
      }
    }
  }

  // 验证缩放配置 - 检查扩展属性
  if ('scale' in config && config.scale !== undefined) {
    if (config.scale.factor !== undefined) {
      const { min, max } = VALIDATION_RULES.SCALE_FACTOR;
      if (config.scale.factor < min || config.scale.factor > max) {
        errors.push(`缩放因子超出范围：${config.scale.factor}，有效范围：[${min}, ${max}]`);
      }
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
  };
};

/**
 * 验证业务配置
 * @param config 业务配置
 * @returns 验证结果
 */
export const validateBusinessConfig = (config: Partial<GridBusinessConfig>): ValidationResult => {
  const errors: string[] = [];
  const warnings: string[] = [];

  // 验证交互模式
  if (config.interactionMode !== undefined) {
    const validModes = Object.values(INTERACTION_MODES);
    if (!validModes.includes(config.interactionMode as any)) {
      errors.push(`无效的交互模式：${config.interactionMode}，有效值：${validModes.join(', ')}`);
    }
  }

  // 验证选择模式
  if (config.selectionMode !== undefined) {
    const validModes = Object.values(SELECTION_MODES);
    if (!validModes.includes(config.selectionMode as any)) {
      errors.push(`无效的选择模式：${config.selectionMode}，有效值：${validModes.join(', ')}`);
    }
  }

  // 验证批次大小
  if (config.batchSize !== undefined) {
    if (config.batchSize < 1) {
      errors.push(`批次大小必须大于0：${config.batchSize}`);
    }
    if (config.batchSize > 1000) {
      warnings.push(`批次大小较大，可能影响性能：${config.batchSize}`);
    }
  }

  // 验证防抖时间
  if (config.debounceMs !== undefined) {
    if (config.debounceMs < 0) {
      errors.push(`防抖时间不能为负数：${config.debounceMs}`);
    }
    if (config.debounceMs > 5000) {
      warnings.push(`防抖时间过长，可能影响用户体验：${config.debounceMs}ms`);
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
  };
};

/**
 * 验证网格数据数组
 * @param cells 网格数据数组
 * @returns 验证结果
 */
export const validateGridData = (cells: CellData[][]): ValidationResult => {
  const errors: string[] = [];
  const warnings: string[] = [];

  // 检查数组结构
  if (!Array.isArray(cells)) {
    errors.push('网格数据必须是二维数组');
    return { isValid: false, errors, warnings };
  }

  // 检查网格尺寸
  if (cells.length !== GRID_CONSTANTS.GRID_SIZE) {
    errors.push(`网格行数不正确：期望 ${GRID_CONSTANTS.GRID_SIZE}，实际 ${cells.length}`);
  }

  // 检查每行的列数
  cells.forEach((row, rowIndex) => {
    if (!Array.isArray(row)) {
      errors.push(`第 ${rowIndex} 行不是数组`);
      return;
    }
    if (row.length !== GRID_CONSTANTS.GRID_SIZE) {
      errors.push(`第 ${rowIndex} 行列数不正确：期望 ${GRID_CONSTANTS.GRID_SIZE}，实际 ${row.length}`);
    }
  });

  // 验证单元格数据
  let validCells = 0;
  let invalidCells = 0;

  cells.forEach((row, rowIndex) => {
    row.forEach((cell, colIndex) => {
      if (cell) {
        const cellValidation = validateCellData(cell);
        if (cellValidation.isValid) {
          validCells++;
        } else {
          invalidCells++;
          errors.push(`单元格 [${rowIndex}, ${colIndex}] 验证失败：${cellValidation.errors.join(', ')}`);
        }
      }
    });
  });

  if (invalidCells > 0) {
    warnings.push(`发现 ${invalidCells} 个无效单元格，${validCells} 个有效单元格`);
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
  };
};

/**
 * 综合验证函数
 * @param data 要验证的数据
 * @returns 详细验证结果
 */
export const validateAll = (data: {
  cells?: CellData[][];
  uiConfig?: Partial<GridUIConfig>;
  businessConfig?: Partial<GridBusinessConfig>;
}): DetailedValidationResult[] => {
  const results: DetailedValidationResult[] = [];

  // 验证网格数据
  if (data.cells) {
    const gridValidation = validateGridData(data.cells);
    results.push({
      field: 'cells',
      value: data.cells,
      isValid: gridValidation.isValid,
      errors: gridValidation.errors,
      warnings: gridValidation.warnings,
    });
  }

  // 验证UI配置
  if (data.uiConfig) {
    const uiValidation = validateUIConfig(data.uiConfig);
    results.push({
      field: 'uiConfig',
      value: data.uiConfig,
      isValid: uiValidation.isValid,
      errors: uiValidation.errors,
      warnings: uiValidation.warnings,
    });
  }

  // 验证业务配置
  if (data.businessConfig) {
    const businessValidation = validateBusinessConfig(data.businessConfig);
    results.push({
      field: 'businessConfig',
      value: data.businessConfig,
      isValid: businessValidation.isValid,
      errors: businessValidation.errors,
      warnings: businessValidation.warnings,
    });
  }

  return results;
};

/**
 * 验证工具函数
 * 🎯 核心价值：提供便捷的验证工具和辅助函数
 * 📦 功能：数据转换、验证辅助、错误格式化
 * ⚡ 类型安全：完整的TypeScript支持
 */

import { z } from 'zod';

// === 数据转换工具 ===

/**
 * 字符串转数字（安全转换）
 */
export const stringToNumber = z.string().transform((val, ctx) => {
  const parsed = parseFloat(val);
  if (isNaN(parsed)) {
    ctx.addIssue({
      code: z.ZodIssueCode.custom,
      message: '无法转换为数字',
    });
    return z.NEVER;
  }
  return parsed;
});

/**
 * 字符串转整数（安全转换）
 */
export const stringToInt = z.string().transform((val, ctx) => {
  const parsed = parseInt(val, 10);
  if (isNaN(parsed)) {
    ctx.addIssue({
      code: z.ZodIssueCode.custom,
      message: '无法转换为整数',
    });
    return z.NEVER;
  }
  return parsed;
});

/**
 * 字符串转布尔值
 */
export const stringToBoolean = z.string().transform((val) => {
  const lower = val.toLowerCase();
  return lower === 'true' || lower === '1' || lower === 'yes';
});

/**
 * 日期字符串转Date对象
 */
export const stringToDate = z.string().transform((val, ctx) => {
  const date = new Date(val);
  if (isNaN(date.getTime())) {
    ctx.addIssue({
      code: z.ZodIssueCode.custom,
      message: '无效的日期格式',
    });
    return z.NEVER;
  }
  return date;
});

/**
 * JSON字符串转对象
 */
export const stringToJson = <T>(schema: z.ZodType<T>) =>
  z.string().transform((val, ctx) => {
    try {
      const parsed = JSON.parse(val);
      return schema.parse(parsed);
    } catch (error) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: '无效的JSON格式',
      });
      return z.NEVER;
    }
  });

// === 验证辅助函数 ===

/**
 * 创建条件验证
 */
export function conditionalValidation<T>(
  condition: (data: T) => boolean,
  schema: z.ZodType<any>,
  message = '条件验证失败'
) {
  return z.any().refine(
    (data: T) => !condition(data) || schema.safeParse(data).success,
    { message }
  );
}

/**
 * 创建异步验证
 */
export function asyncValidation<T>(
  validator: (value: T) => Promise<boolean>,
  message = '异步验证失败'
) {
  return z.any().refine(validator, { message });
}

/**
 * 创建跨字段验证
 */
export function crossFieldValidation<T extends Record<string, any>>(
  validator: (data: T) => boolean,
  message = '字段关联验证失败'
) {
  return z.object({} as any).refine(validator as any, { message });
}

/**
 * 创建唯一性验证
 */
export function uniqueValidation<T>(
  checkUnique: (value: T) => Promise<boolean>,
  message = '该值已存在'
) {
  return asyncValidation(checkUnique, message);
}

// === 错误处理工具 ===

/**
 * 格式化Zod错误
 */
export interface FormattedError {
  field: string;
  message: string;
  code: string;
}

export function formatZodErrors(error: z.ZodError): FormattedError[] {
  return error.errors.map((err) => ({
    field: err.path.join('.'),
    message: err.message,
    code: err.code,
  }));
}

/**
 * 将Zod错误转换为字段错误对象
 */
export function zodErrorsToFieldErrors(error: z.ZodError): Record<string, string> {
  const fieldErrors: Record<string, string> = {};
  
  error.errors.forEach((err) => {
    const field = err.path.join('.');
    if (!fieldErrors[field]) {
      fieldErrors[field] = err.message;
    }
  });
  
  return fieldErrors;
}

/**
 * 获取第一个错误消息
 */
export function getFirstErrorMessage(error: z.ZodError): string {
  return error.errors[0]?.message || '验证失败';
}

// === 验证预设 ===

/**
 * 常用验证预设
 */
export const ValidationPresets = {
  // 中文姓名
  chineseName: z.string()
    .min(2, '姓名至少2个字符')
    .max(10, '姓名最多10个字符')
    .regex(/^[\u4e00-\u9fa5]+$/, '请输入中文姓名'),
  
  // 身份证号
  idCard: z.string()
    .regex(/^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/, '请输入有效的身份证号'),
  
  // 银行卡号
  bankCard: z.string()
    .regex(/^\d{16,19}$/, '请输入有效的银行卡号'),
  
  // 车牌号
  licensePlate: z.string()
    .regex(/^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领][A-Z][A-Z0-9]{4}[A-Z0-9挂学警港澳]$/, '请输入有效的车牌号'),
  
  // IP地址
  ipAddress: z.string()
    .regex(/^((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/, '请输入有效的IP地址'),
  
  // MAC地址
  macAddress: z.string()
    .regex(/^([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})$/, '请输入有效的MAC地址'),
  
  // 颜色值（十六进制）
  hexColor: z.string()
    .regex(/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/, '请输入有效的颜色值'),
  
  // 版本号
  version: z.string()
    .regex(/^\d+\.\d+\.\d+$/, '请输入有效的版本号（如：1.0.0）'),
};

// === 数据清理工具 ===

/**
 * 清理字符串（去除首尾空格）
 */
export const trimString = z.string().transform(val => val.trim());

/**
 * 清理并转换为小写
 */
export const toLowerCase = z.string().transform(val => val.toLowerCase());

/**
 * 清理并转换为大写
 */
export const toUpperCase = z.string().transform(val => val.toUpperCase());

/**
 * 移除特殊字符
 */
export const removeSpecialChars = z.string().transform(val => 
  val.replace(/[^\w\s\u4e00-\u9fa5]/g, '')
);

/**
 * 标准化手机号（移除分隔符）
 */
export const normalizePhone = z.string().transform(val => 
  val.replace(/[\s-()]/g, '')
);

// === 组合验证工具 ===

/**
 * 创建可选字段验证
 */
export function optionalField<T>(schema: z.ZodType<T>) {
  return z.union([schema, z.literal(''), z.null(), z.undefined()])
    .transform(val => val === '' || val === null || val === undefined ? undefined : val);
}

/**
 * 创建数组字段验证
 */
export function arrayField<T>(itemSchema: z.ZodType<T>, minItems = 0, maxItems?: number) {
  let schema = z.array(itemSchema).min(minItems);
  if (maxItems !== undefined) {
    schema = schema.max(maxItems);
  }
  return schema;
}

/**
 * 创建枚举字段验证
 */
export function enumField<T extends readonly [string, ...string[]]>(
  values: T,
  message = '请选择有效的选项'
) {
  return z.enum(values, { errorMap: () => ({ message }) });
}

// === 验证管道 ===

/**
 * 创建验证管道
 */
export class ValidationPipeline<T> {
  private schema: z.ZodType<T>;
  
  constructor(initialSchema: z.ZodType<T>) {
    this.schema = initialSchema;
  }
  
  /**
   * 添加验证步骤
   */
  pipe<U>(nextSchema: z.ZodType<U>): ValidationPipeline<U> {
    return new ValidationPipeline(this.schema.pipe(nextSchema));
  }
  
  /**
   * 添加转换步骤
   */
  transform<U>(transformer: (value: T) => U): ValidationPipeline<U> {
    return new ValidationPipeline(this.schema.transform(transformer) as any);
  }
  
  /**
   * 添加细化验证
   */
  refine(
    validator: (value: T) => boolean,
    message: string
  ): ValidationPipeline<T> {
    return new ValidationPipeline(this.schema.refine(validator, { message }));
  }
  
  /**
   * 获取最终schema
   */
  build(): z.ZodType<T> {
    return this.schema;
  }
  
  /**
   * 验证数据
   */
  validate(data: unknown): T {
    return this.schema.parse(data);
  }
  
  /**
   * 安全验证数据
   */
  safeValidate(data: unknown) {
    return this.schema.safeParse(data);
  }
}

/**
 * 创建验证管道
 */
export function createValidationPipeline<T>(schema: z.ZodType<T>) {
  return new ValidationPipeline(schema);
}

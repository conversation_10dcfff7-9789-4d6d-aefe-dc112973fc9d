/**
 * 验证系统统一导出
 * 🎯 核心价值：统一的验证规则和工具函数
 * 📦 功能：Schema导出、验证工具、错误处理
 * ⚡ 类型安全：完整的TypeScript支持
 */

// === Schema导出 ===
export * from './schemas';

// === 验证工具导出 ===
export * from './utils';

// === 错误处理导出 ===
export * from './errors';

// === 矩阵验证函数导出 ===
export {
  validateGridData as validateMatrixGridData,
  validateGroupOffsetConsistency,
  validateCoordinate,
  validateMatrixDataIntegrity,
} from './matrixValidation';

// === 网格系统验证函数导出 ===
export {
  validateCoordinates,
  validateCellData,
  validateUIConfig,
  validateBusinessConfig,
  validateGridData,
  validateAll,
} from './gridValidation';

// === 网格验证类型导出 ===
export type {
  ValidationResult,
  DetailedValidationResult,
} from './gridValidation';

// === 常用验证规则快捷导出 ===
export {
  StringValidation,
  NumberValidation,
  ArrayValidation,
  // UserSchema, // 已移除
  ProjectSchema,
  ColorSchema,
  GridSchema,
  FormSchema,
} from './schemas';

// === 类型导出 ===
export type {
  // User, // 已移除
  // CreateUserRequest, // 已移除
  // UpdateUserRequest, // 已移除
  // LoginRequest, // 已移除
  Project,
  CreateProjectRequest,
  UpdateProjectRequest,
  ColorType,
  ColorCoordinates,
  ColorInfo,
  ColorData,
  GridData,
  GridConfig,
  SearchForm,
  PaginationParams,
  FileUploadForm,
} from './schemas';

// === 验证配置 ===
export const ValidationConfig = {
  // 默认错误消息
  defaultMessages: {
    required: '此字段为必填项',
    email: '请输入有效的邮箱地址',
    phone: '请输入有效的手机号码',
    url: '请输入有效的URL地址',
    password: '密码至少8位，需包含字母和数字',
    minLength: (min: number) => `最少${min}个字符`,
    maxLength: (max: number) => `最多${max}个字符`,
    min: (min: number) => `最小值为${min}`,
    max: (max: number) => `最大值为${max}`,
    pattern: '格式不正确',
  },
  
  // 验证选项
  options: {
    // 是否在第一个错误时停止验证
    abortEarly: false,
    // 是否允许未知字段
    allowUnknown: false,
    // 是否去除未知字段
    stripUnknown: true,
  },
  
  // 国际化支持
  i18n: {
    enabled: true,
    defaultLocale: 'zh-CN',
    fallbackLocale: 'en',
  },
} as const;

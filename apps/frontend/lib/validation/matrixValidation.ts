/**
 * 矩阵验证函数
 * 🎯 职责：矩阵数据验证、一致性检查、数据完整性验证
 * 📦 重构来源：从 basicDataStore.ts 中提取的验证函数
 * ✅ 独立验证：提高验证逻辑的独立性和可测试性
 */

import type { CellData, GroupType } from '@/lib/types/matrix';
import { 
  GRID_DIMENSIONS, 
  SPECIAL_COORDINATES,
  GROUP_OFFSET_CONFIGS 
} from '@/stores/constants/matrix';
import { generateGridData } from '@/lib/utils/matrixUtils';

/**
 * 验证网格数据生成的正确性
 * @returns 验证结果
 */
export const validateGridData = (): {
  isValid: boolean;
  totalCells: number;
  centerCell: CellData | null;
  errors: string[];
} => {
  const gridData = generateGridData();
  const errors: string[] = [];

  // 检查总数
  const expectedTotal = GRID_DIMENSIONS.ROWS * GRID_DIMENSIONS.COLS;
  if (gridData.length !== expectedTotal) {
    errors.push(`网格数据总数不正确：期望 ${expectedTotal}，实际 ${gridData.length}`);
  }

  // 检查中心点 (0,0) 对应的序号是否为545
  const centerCell = gridData.find(cell => cell.x === 0 && cell.y === 0) || null;
  if (!centerCell) {
    errors.push('未找到中心点 (0,0)');
  } else if (centerCell.number !== 545) {
    errors.push(`中心点序号不正确：期望 545，实际 ${centerCell.number}`);
  }

  // 检查坐标范围
  const invalidCoords = gridData.filter(cell =>
    cell.x < -16 || cell.x > 16 || cell.y < -16 || cell.y > 16
  );
  if (invalidCoords.length > 0) {
    errors.push(`发现 ${invalidCoords.length} 个超出范围的坐标`);
  }

  return {
    isValid: errors.length === 0,
    totalCells: gridData.length,
    centerCell,
    errors
  };
};

/**
 * 验证GROUP_OFFSET_CONFIGS与SPECIAL_COORDINATES的一致性
 * @returns 验证结果和不一致的项目
 */
export const validateGroupOffsetConsistency = (): {
  isValid: boolean;
  inconsistencies: string[];
  warnings: string[];
} => {
  const inconsistencies: string[] = [];
  const warnings: string[] = [];

  // 检查每个组的黑色格子偏移是否与SPECIAL_COORDINATES一致
  Object.entries(GROUP_OFFSET_CONFIGS).forEach(([groupKey, config]) => {
    const group = groupKey as GroupType;

    // 获取SPECIAL_COORDINATES中该组对应的坐标
    const expectedCoord = Array.from(SPECIAL_COORDINATES.entries())
      .find(([_, letter]) => letter === group)?.[0];

    if (!expectedCoord) {
      warnings.push(`组 ${group} 在SPECIAL_COORDINATES中未找到对应坐标`);
      return;
    }

    const [expectedX, expectedY] = expectedCoord.split(',').map(Number);

    // 检查defaultOffset
    const [defaultX, defaultY] = config.defaultOffset;
    if (defaultX !== expectedX || defaultY !== expectedY) {
      inconsistencies.push(
        `组 ${group} defaultOffset [${defaultX}, ${defaultY}] 与 SPECIAL_COORDINATES [${expectedX}, ${expectedY}] 不一致`
      );
    }

    // 检查level1Offsets中的black偏移（如果存在）
    if (config.level1Offsets?.black) {
      const [blackX, blackY] = config.level1Offsets.black;
      if (blackX !== expectedX || blackY !== expectedY) {
        inconsistencies.push(
          `组 ${group} level1Offsets.black [${blackX}, ${blackY}] 与 SPECIAL_COORDINATES [${expectedX}, ${expectedY}] 不一致`
        );
      }
    }
  });

  return {
    isValid: inconsistencies.length === 0,
    inconsistencies,
    warnings
  };
};

/**
 * 验证坐标是否在有效范围内
 * @param x X坐标
 * @param y Y坐标
 * @returns 是否有效
 */
export const validateCoordinate = (x: number, y: number): boolean => {
  return x >= -16 && x <= 16 && y >= -16 && y <= 16;
};

/**
 * 验证矩阵数据的完整性
 * @param matrixData 矩阵数据
 * @returns 验证结果
 */
export const validateMatrixDataIntegrity = (matrixData: unknown): {
  isValid: boolean;
  errors: string[];
  warnings: string[];
} => {
  const errors: string[] = [];
  const warnings: string[] = [];

  // 检查基本结构
  if (!matrixData) {
    errors.push('矩阵数据为空');
    return { isValid: false, errors, warnings };
  }

  // 类型断言以便访问属性
  const matrixObj = matrixData as any;

  // 检查必要的属性
  const requiredProperties = ['byCoordinate', 'byGroup', 'byColor', 'byLevel'];
  for (const prop of requiredProperties) {
    if (!(prop in matrixObj)) {
      errors.push(`缺少必要属性: ${prop}`);
    }
  }

  // 检查byCoordinate是否为Map
  if (!(matrixObj.byCoordinate instanceof Map)) {
    errors.push('byCoordinate 应该是 Map 类型');
  }

  // 检查数据一致性
  if (matrixObj.byCoordinate instanceof Map) {
    let totalPointsByCoordinate = 0;
    matrixObj.byCoordinate.forEach((points: unknown[]) => {
      totalPointsByCoordinate += points.length;
    });

    // 检查其他索引的数据总数是否一致
    const totalPointsByGroup = Object.values(matrixObj.byGroup || {})
      .reduce((sum: number, points: any) => sum + (Array.isArray(points) ? points.length : 0), 0);

    if (totalPointsByCoordinate !== totalPointsByGroup) {
      warnings.push(`坐标索引和组索引的数据点总数不一致: ${totalPointsByCoordinate} vs ${totalPointsByGroup}`);
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  };
};

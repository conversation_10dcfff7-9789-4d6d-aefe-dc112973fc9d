/**
 * 验证错误处理
 * 🎯 核心价值：统一的错误处理和消息格式化
 * 📦 功能：错误类定义、错误格式化、国际化支持
 * ⚡ 类型安全：完整的TypeScript支持
 */

import { z } from 'zod';

// === 错误类型定义 ===

/**
 * 验证错误接口
 */
export interface ValidationError {
  field: string;
  message: string;
  code: string;
  value?: any;
}

/**
 * 表单错误状态
 */
export interface FormErrorState {
  hasErrors: boolean;
  errors: Record<string, string>;
  firstError?: string;
  errorCount: number;
}

/**
 * 验证结果
 */
export interface ValidationResult<T = any> {
  success: boolean;
  data?: T;
  errors?: ValidationError[];
  errorState?: FormErrorState;
}

// === 错误类定义 ===

/**
 * 自定义验证错误类
 */
export class FormValidationError extends Error {
  public readonly errors: ValidationError[];
  public readonly field?: string;
  public readonly code: string;

  constructor(
    message: string,
    errors: ValidationError[] = [],
    field?: string,
    code = 'VALIDATION_ERROR'
  ) {
    super(message);
    this.name = 'FormValidationError';
    this.errors = errors;
    this.field = field;
    this.code = code;
  }

  /**
   * 获取第一个错误消息
   */
  getFirstError(): string {
    return this.errors[0]?.message || this.message;
  }

  /**
   * 获取指定字段的错误
   */
  getFieldError(field: string): string | undefined {
    return this.errors.find(err => err.field === field)?.message;
  }

  /**
   * 转换为字段错误对象
   */
  toFieldErrors(): Record<string, string> {
    const fieldErrors: Record<string, string> = {};
    this.errors.forEach(err => {
      if (!fieldErrors[err.field]) {
        fieldErrors[err.field] = err.message;
      }
    });
    return fieldErrors;
  }
}

// === 错误处理工具 ===

/**
 * 将Zod错误转换为验证错误
 */
export function zodErrorToValidationErrors(error: z.ZodError): ValidationError[] {
  return error.errors.map(err => ({
    field: err.path.join('.'),
    message: err.message,
    code: err.code,
    value: err.path.length > 0 ? getNestedValue(error.issues, err.path) : undefined,
  }));
}

/**
 * 获取嵌套值
 */
function getNestedValue(obj: any, path: (string | number)[]): unknown {
  return path.reduce((current, key) => current?.[key], obj);
}

/**
 * 创建表单错误状态
 */
export function createFormErrorState(errors: ValidationError[]): FormErrorState {
  const fieldErrors: Record<string, string> = {};
  
  errors.forEach(err => {
    if (!fieldErrors[err.field]) {
      fieldErrors[err.field] = err.message;
    }
  });

  return {
    hasErrors: errors.length > 0,
    errors: fieldErrors,
    firstError: errors[0]?.message,
    errorCount: errors.length,
  };
}

/**
 * 验证数据并返回结果
 */
export function validateWithResult<T>(
  schema: z.ZodType<T>,
  data: unknown
): ValidationResult<T> {
  const result = schema.safeParse(data);
  
  if (result.success) {
    return {
      success: true,
      data: result.data,
      errors: [],
      errorState: createFormErrorState([]),
    };
  }
  
  const errors = zodErrorToValidationErrors(result.error);
  return {
    success: false,
    errors,
    errorState: createFormErrorState(errors),
  };
}

/**
 * 验证数据并抛出自定义错误
 */
export function validateAndThrow<T>(
  schema: z.ZodType<T>,
  data: unknown,
  customMessage?: string
): T {
  const result = schema.safeParse(data);
  
  if (result.success) {
    return result.data;
  }
  
  const errors = zodErrorToValidationErrors(result.error);
  throw new FormValidationError(
    customMessage || '数据验证失败',
    errors
  );
}

// === 错误消息国际化 ===

/**
 * 错误消息映射
 */
export const ErrorMessages = {
  'zh-CN': {
    required: '此字段为必填项',
    email: '请输入有效的邮箱地址',
    phone: '请输入有效的手机号码',
    url: '请输入有效的URL地址',
    password: '密码格式不正确',
    minLength: (min: number) => `最少需要${min}个字符`,
    maxLength: (max: number) => `最多允许${max}个字符`,
    min: (min: number) => `最小值为${min}`,
    max: (max: number) => `最大值为${max}`,
    pattern: '格式不正确',
    unique: '该值已存在',
    custom: '验证失败',
    invalid_type: '数据类型不正确',
    invalid_string: '必须是字符串',
    invalid_number: '必须是数字',
    invalid_boolean: '必须是布尔值',
    invalid_date: '必须是有效日期',
    invalid_array: '必须是数组',
    invalid_object: '必须是对象',
    too_small: '值太小',
    too_big: '值太大',
    invalid_enum_value: '请选择有效的选项',
  },
  'en': {
    required: 'This field is required',
    email: 'Please enter a valid email address',
    phone: 'Please enter a valid phone number',
    url: 'Please enter a valid URL',
    password: 'Password format is incorrect',
    minLength: (min: number) => `Minimum ${min} characters required`,
    maxLength: (max: number) => `Maximum ${max} characters allowed`,
    min: (min: number) => `Minimum value is ${min}`,
    max: (max: number) => `Maximum value is ${max}`,
    pattern: 'Invalid format',
    unique: 'This value already exists',
    custom: 'Validation failed',
    invalid_type: 'Invalid data type',
    invalid_string: 'Must be a string',
    invalid_number: 'Must be a number',
    invalid_boolean: 'Must be a boolean',
    invalid_date: 'Must be a valid date',
    invalid_array: 'Must be an array',
    invalid_object: 'Must be an object',
    too_small: 'Value is too small',
    too_big: 'Value is too big',
    invalid_enum_value: 'Please select a valid option',
  },
} as const;

/**
 * 获取本地化错误消息
 */
export function getLocalizedErrorMessage(
  code: string,
  locale: 'zh-CN' | 'en' = 'zh-CN',
  params?: unknown[]
): string {
  const messages = ErrorMessages[locale];
  const message = messages[code as keyof typeof messages];
  
  if (typeof message === 'function' && params && Array.isArray(params)) {
    return (message as any)(...params);
  }
  
  return typeof message === 'string' ? message : messages.custom;
}

/**
 * 自定义错误消息映射
 */
export function customErrorMap(
  issue: z.ZodIssueOptionalMessage,
  ctx: z.ErrorMapCtx
): { message: string } {
  const locale = 'zh-CN'; // 可以从上下文或配置中获取
  
  switch (issue.code) {
    case z.ZodIssueCode.invalid_type:
      return { message: getLocalizedErrorMessage('invalid_type', locale) };
    
    case z.ZodIssueCode.too_small:
      if (issue.type === 'string') {
        return { message: getLocalizedErrorMessage('minLength', locale, [issue.minimum]) };
      }
      return { message: getLocalizedErrorMessage('min', locale, [issue.minimum]) };
    
    case z.ZodIssueCode.too_big:
      if (issue.type === 'string') {
        return { message: getLocalizedErrorMessage('maxLength', locale, [issue.maximum]) };
      }
      return { message: getLocalizedErrorMessage('max', locale, [issue.maximum]) };
    
    case z.ZodIssueCode.invalid_string:
      if (issue.validation === 'email') {
        return { message: getLocalizedErrorMessage('email', locale) };
      }
      if (issue.validation === 'url') {
        return { message: getLocalizedErrorMessage('url', locale) };
      }
      return { message: getLocalizedErrorMessage('pattern', locale) };
    
    case z.ZodIssueCode.invalid_enum_value:
      return { message: getLocalizedErrorMessage('invalid_enum_value', locale) };
    
    default:
      return { message: ctx.defaultError };
  }
}

// === 错误处理Hook辅助 ===

/**
 * 错误状态管理接口
 */
export interface ErrorStateManager {
  errors: Record<string, string>;
  hasErrors: boolean;
  setError: (field: string, message: string) => void;
  clearError: (field: string) => void;
  clearAllErrors: () => void;
  getError: (field: string) => string | undefined;
  setErrors: (errors: Record<string, string>) => void;
}

/**
 * 创建错误状态管理器
 */
export function createErrorStateManager(
  initialErrors: Record<string, string> = {}
): ErrorStateManager {
  let errors = { ...initialErrors };
  
  return {
    get errors() {
      return { ...errors };
    },
    
    get hasErrors() {
      return Object.keys(errors).length > 0;
    },
    
    setError(field: string, message: string) {
      errors[field] = message;
    },
    
    clearError(field: string) {
      delete errors[field];
    },
    
    clearAllErrors() {
      errors = {};
    },
    
    getError(field: string) {
      return errors[field];
    },
    
    setErrors(newErrors: Record<string, string>) {
      errors = { ...newErrors };
    },
  };
}

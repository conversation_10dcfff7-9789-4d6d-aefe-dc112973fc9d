/**
 * 网格渲染引擎Hook
 * 🎯 核心价值：为React组件提供统一的渲染引擎接口
 * 📦 功能范围：渲染引擎实例管理、配置同步、性能优化
 * ⚡ 性能优化：引擎实例复用、配置变化检测、渲染缓存
 */

import { useMemo, useCallback, useRef, useEffect } from 'react';
import { GridRenderingEngine, type RenderingConfig, type CellRenderData, DEFAULT_RENDERING_CONFIG } from './GridRenderingEngine';
import { usePerformanceOptimizer } from './PerformanceOptimizer';
import type { CellData } from '@/lib/types/grid';
import { useGridConfigStore } from '@/stores/gridConfigStore';
import { useStyleStore } from '@/stores/styleStore';

// Hook返回接口
export interface UseGridRenderingEngineReturn {
  // 核心渲染方法
  getCellRenderData: (cell: CellData) => CellRenderData;
  getBatchCellRenderData: (cells: CellData[]) => Map<string, CellRenderData>;
  getBatchCellRenderDataAsync: (cells: CellData[]) => Promise<Map<string, CellRenderData>>;

  // 渲染引擎控制
  clearCache: () => void;
  getCacheStats: () => { size: number; hitRate: number };

  // 性能优化
  debounceRender: (callback: () => void) => void;
  getPerformanceMetrics: () => any;

  // 配置信息
  config: RenderingConfig;
  isReady: boolean;
}

// Hook选项
export interface UseGridRenderingEngineOptions {
  enableCache?: boolean;
  batchSize?: number;
  autoUpdateConfig?: boolean;
}

/**
 * 网格渲染引擎Hook
 */
export function useGridRenderingEngine(
  options: UseGridRenderingEngineOptions = {}
): UseGridRenderingEngineReturn {
  const {
    enableCache = true,
    batchSize = 100,
    autoUpdateConfig = true,
  } = options;

  // 获取相关状态
  const { baseDisplayMode, colorModeEnabled } = useGridConfigStore();
  const { config: styleConfig } = useStyleStore();

  // 渲染引擎实例引用
  const engineRef = useRef<GridRenderingEngine | null>(null);
  const lastConfigRef = useRef<string>('');

  // 性能优化器
  const { batchRender, debounceRender, getMetrics } = usePerformanceOptimizer({
    batchSize,
    enableRAF: true,
    enableProfiling: process.env.NODE_ENV === 'development',
  });

  // 构建渲染配置
  const renderingConfig = useMemo((): RenderingConfig => {
    return {
      displayMode: baseDisplayMode,
      colorModeEnabled,
      cellSize: styleConfig.cellSize,
      cellGap: styleConfig.cellGap,
      showBorders: styleConfig.showBorders,
      enableAnimations: styleConfig.enableAnimations,
      opacity: styleConfig.opacity,
    };
  }, [
    baseDisplayMode,
    colorModeEnabled,
    styleConfig.cellSize,
    styleConfig.cellGap,
    styleConfig.showBorders,
    styleConfig.enableAnimations,
    styleConfig.opacity,
  ]);

  // 创建或更新渲染引擎
  const engine = useMemo(() => {
    const configHash = JSON.stringify(renderingConfig);
    
    // 如果配置没有变化且引擎已存在，复用现有引擎
    if (engineRef.current && lastConfigRef.current === configHash) {
      return engineRef.current;
    }

    // 创建新引擎或更新配置
    if (!engineRef.current) {
      engineRef.current = new GridRenderingEngine(renderingConfig);
    } else if (autoUpdateConfig) {
      engineRef.current.updateConfig(renderingConfig);
    }

    lastConfigRef.current = configHash;
    return engineRef.current;
  }, [renderingConfig, autoUpdateConfig]);

  // 自动配置同步
  useEffect(() => {
    if (autoUpdateConfig && engine) {
      engine.updateConfig(renderingConfig);
    }
  }, [renderingConfig, autoUpdateConfig, engine]);

  // 获取单元格渲染数据
  const getCellRenderData = useCallback((cell: CellData): CellRenderData => {
    if (!engine) {
      // 降级处理：返回基础渲染数据
      return {
        color: null,
        content: `${cell.x},${cell.y}`,
        style: {
          width: '24px',
          height: '24px',
          backgroundColor: 'transparent',
        },
        className: 'grid-cell',
        isActive: false,
        isVisible: true,
      };
    }

    return engine.getCellRenderData(cell);
  }, [engine]);

  // 同步版本的批量渲染（向后兼容）
  const getBatchCellRenderData = useCallback((cells: CellData[]): Map<string, CellRenderData> => {
    if (!engine) {
      const result = new Map<string, CellRenderData>();
      for (const cell of cells) {
        const key = `${cell.x}-${cell.y}`;
        result.set(key, getCellRenderData(cell));
      }
      return result;
    }

    return engine.getBatchCellRenderData(cells);
  }, [engine, getCellRenderData]);

  // 异步版本的批量渲染（使用性能优化器）
  const getBatchCellRenderDataAsync = useCallback(async (cells: CellData[]): Promise<Map<string, CellRenderData>> => {
    if (!engine) {
      // 降级处理：逐个处理
      const result = new Map<string, CellRenderData>();
      for (const cell of cells) {
        const key = `${cell.x}-${cell.y}`;
        result.set(key, getCellRenderData(cell));
      }
      return result;
    }

    // 使用性能优化器进行批量渲染
    return batchRender(cells, (cell) => engine.getCellRenderData(cell));
  }, [engine, batchRender, getCellRenderData]);

  // 清空缓存
  const clearCache = useCallback(() => {
    if (engine && enableCache) {
      engine.clearCache();
    }
  }, [engine, enableCache]);

  // 获取缓存统计
  const getCacheStats = useCallback(() => {
    if (engine && enableCache) {
      return engine.getCacheStats();
    }
    return { size: 0, hitRate: 0 };
  }, [engine, enableCache]);

  return {
    getCellRenderData,
    getBatchCellRenderData,
    getBatchCellRenderDataAsync,
    clearCache,
    getCacheStats,
    debounceRender,
    getPerformanceMetrics: getMetrics,
    config: renderingConfig,
    isReady: !!engine,
  };
}

// 便捷Hook：仅获取单元格渲染数据
export function useCellRenderData(cell: CellData): CellRenderData {
  const { getCellRenderData } = useGridRenderingEngine();
  return getCellRenderData(cell);
}

// 便捷Hook：批量获取渲染数据
export function useBatchCellRenderData(cells: CellData[]): Map<string, CellRenderData> {
  const { getBatchCellRenderData } = useGridRenderingEngine();
  return getBatchCellRenderData(cells);
}

/**
 * 渲染性能优化器
 * 🎯 核心价值：优化1089个单元格的渲染性能，减少不必要的重渲染
 * 📦 功能范围：批量渲染、智能缓存、渲染调度、性能监控
 * ⚡ 性能优化：requestAnimationFrame、Web Workers、内存优化
 */

import type { CellData } from '@/lib/types/grid';
import type { CellRenderData } from './GridRenderingEngine';

// 性能配置接口
export interface PerformanceConfig {
  batchSize: number;
  enableRAF: boolean;
  enableWebWorkers: boolean;
  maxCacheSize: number;
  debounceMs: number;
  enableProfiling: boolean;
}

// 渲染任务接口
export interface RenderTask {
  id: string;
  cells: CellData[];
  priority: 'high' | 'medium' | 'low';
  callback: (results: Map<string, CellRenderData>) => void;
}

// 性能指标接口
export interface PerformanceMetrics {
  renderTime: number;
  batchCount: number;
  cacheHitRate: number;
  memoryUsage: number;
  frameRate: number;
}

// 默认性能配置
export const DEFAULT_PERFORMANCE_CONFIG: PerformanceConfig = {
  batchSize: 100,
  enableRAF: true,
  enableWebWorkers: false, // 暂时禁用，避免复杂性
  maxCacheSize: 2000,
  debounceMs: 16, // ~60fps
  enableProfiling: process.env.NODE_ENV === 'development',
};

/**
 * 渲染性能优化器类
 */
export class PerformanceOptimizer {
  private config: PerformanceConfig;
  private renderQueue: RenderTask[] = [];
  private isProcessing = false;
  private metrics: PerformanceMetrics;
  private rafId: number | null = null;
  private debounceTimer: number | null = null;

  constructor(config: Partial<PerformanceConfig> = {}) {
    this.config = { ...DEFAULT_PERFORMANCE_CONFIG, ...config };
    this.metrics = {
      renderTime: 0,
      batchCount: 0,
      cacheHitRate: 0,
      memoryUsage: 0,
      frameRate: 0,
    };
  }

  /**
   * 添加渲染任务到队列
   */
  addRenderTask(task: RenderTask): void {
    this.renderQueue.push(task);
    this.scheduleProcessing();
  }

  /**
   * 批量渲染单元格
   */
  async batchRender(
    cells: CellData[],
    renderFunction: (cell: CellData) => CellRenderData
  ): Promise<Map<string, CellRenderData>> {
    const startTime = performance.now();
    const results = new Map<string, CellRenderData>();

    // 分批处理
    const batches = this.createBatches(cells, this.config.batchSize);
    
    for (const batch of batches) {
      if (this.config.enableRAF) {
        await this.processWithRAF(batch, renderFunction, results);
      } else {
        this.processBatch(batch, renderFunction, results);
      }
    }

    // 更新性能指标
    const endTime = performance.now();
    this.updateMetrics(endTime - startTime, batches.length);

    return results;
  }

  /**
   * 智能缓存管理
   */
  optimizeCache<T>(cache: Map<string, T>): void {
    if (cache.size > this.config.maxCacheSize) {
      // LRU策略：移除最旧的条目
      const keysToRemove = Array.from(cache.keys()).slice(0, cache.size - this.config.maxCacheSize);
      keysToRemove.forEach(key => cache.delete(key));
    }
  }

  /**
   * 防抖渲染调度
   */
  debounceRender(callback: () => void): void {
    if (this.debounceTimer) {
      clearTimeout(this.debounceTimer);
    }

    this.debounceTimer = window.setTimeout(() => {
      callback();
      this.debounceTimer = null;
    }, this.config.debounceMs);
  }

  /**
   * 获取性能指标
   */
  getMetrics(): PerformanceMetrics {
    return { ...this.metrics };
  }

  /**
   * 重置性能指标
   */
  resetMetrics(): void {
    this.metrics = {
      renderTime: 0,
      batchCount: 0,
      cacheHitRate: 0,
      memoryUsage: 0,
      frameRate: 0,
    };
  }

  /**
   * 更新配置
   */
  updateConfig(newConfig: Partial<PerformanceConfig>): void {
    this.config = { ...this.config, ...newConfig };
  }

  /**
   * 销毁优化器
   */
  destroy(): void {
    if (this.rafId) {
      cancelAnimationFrame(this.rafId);
      this.rafId = null;
    }

    if (this.debounceTimer) {
      clearTimeout(this.debounceTimer);
      this.debounceTimer = null;
    }

    this.renderQueue = [];
    this.isProcessing = false;
  }

  // 私有方法

  private createBatches<T>(items: T[], batchSize: number): T[][] {
    const batches: T[][] = [];
    for (let i = 0; i < items.length; i += batchSize) {
      batches.push(items.slice(i, i + batchSize));
    }
    return batches;
  }

  private async processWithRAF(
    batch: CellData[],
    renderFunction: (cell: CellData) => CellRenderData,
    results: Map<string, CellRenderData>
  ): Promise<void> {
    return new Promise((resolve) => {
      this.rafId = requestAnimationFrame(() => {
        this.processBatch(batch, renderFunction, results);
        resolve();
      });
    });
  }

  private processBatch(
    batch: CellData[],
    renderFunction: (cell: CellData) => CellRenderData,
    results: Map<string, CellRenderData>
  ): void {
    for (const cell of batch) {
      const key = `${cell.x}-${cell.y}`;
      const renderData = renderFunction(cell);
      results.set(key, renderData);
    }
  }

  private scheduleProcessing(): void {
    if (this.isProcessing || this.renderQueue.length === 0) {
      return;
    }

    this.debounceRender(() => {
      this.processQueue();
    });
  }

  private async processQueue(): Promise<void> {
    if (this.isProcessing) return;

    this.isProcessing = true;

    try {
      // 按优先级排序
      this.renderQueue.sort((a, b) => {
        const priorityOrder = { high: 0, medium: 1, low: 2 };
        return priorityOrder[a.priority] - priorityOrder[b.priority];
      });

      // 处理队列中的任务
      while (this.renderQueue.length > 0) {
        const task = this.renderQueue.shift();
        if (task) {
          await this.processTask(task);
        }
      }
    } finally {
      this.isProcessing = false;
    }
  }

  private async processTask(task: RenderTask): Promise<void> {
    // 这里应该调用实际的渲染函数
    // 暂时模拟处理
    const results = new Map<string, CellRenderData>();
    task.callback(results);
  }

  private updateMetrics(renderTime: number, batchCount: number): void {
    this.metrics.renderTime = renderTime;
    this.metrics.batchCount = batchCount;
    
    // 更新内存使用情况（如果支持）
    if ('memory' in performance) {
      this.metrics.memoryUsage = (performance as any).memory.usedJSHeapSize;
    }

    // 计算帧率
    this.metrics.frameRate = 1000 / renderTime;

    if (this.config.enableProfiling) {
      console.log('🚀 [PerformanceOptimizer] Metrics:', this.metrics);
    }
  }
}

// 全局性能优化器实例
export const globalPerformanceOptimizer = new PerformanceOptimizer();

// React Hook for performance optimization
export function usePerformanceOptimizer(config?: Partial<PerformanceConfig>) {
  const optimizer = new PerformanceOptimizer(config);

  const batchRender = async (
    cells: CellData[],
    renderFunction: (cell: CellData) => CellRenderData
  ) => {
    return optimizer.batchRender(cells, renderFunction);
  };

  const debounceRender = (callback: () => void) => {
    optimizer.debounceRender(callback);
  };

  const getMetrics = () => optimizer.getMetrics();

  return {
    batchRender,
    debounceRender,
    getMetrics,
    optimizer,
  };
}

/**
 * 网格渲染模块统一导出
 * 🎯 核心价值：提供统一的渲染引擎接口和工具
 * 📦 功能范围：渲染引擎、Hook、类型定义、工具函数
 * ⚡ 便利性：一站式渲染解决方案导入
 */

// === 核心渲染引擎 ===
export {
  GridRenderingEngine,
  createRenderingEngine,
  DEFAULT_RENDERING_CONFIG,
  type RenderingConfig,
  type CellRenderData,
} from './GridRenderingEngine';

// === React Hook接口 ===
export {
  useGridRenderingEngine,
  useCellRenderData,
  useBatchCellRenderData,
  type UseGridRenderingEngineReturn,
  type UseGridRenderingEngineOptions,
} from './useGridRenderingEngine';

// === 渲染工具函数 ===
// TODO: 添加渲染相关的工具函数
// export * from './utils';

// === 渲染常量 ===
// TODO: 添加渲染相关的常量
// export * from './constants';

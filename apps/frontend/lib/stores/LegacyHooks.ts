/**
 * 向后兼容的Hook接口
 * 🎯 核心价值：提供向后兼容的Hook接口，确保现有代码无缝迁移
 * 📦 功能范围：兼容现有的basicDataStore、gridConfigStore、styleStore、dynamicStyleStore接口
 * 🔄 架构设计：内部委托给统一状态管理器，保持API兼容性
 */

import { useCallback, useMemo } from 'react';
import { useUnifiedMatrixStore } from './UnifiedMatrixStore';
import type {
  BasicColorType,
  GroupType,
  ColorLevel,
  MatrixData,
  CellData,
  ColorValue,
  ColorVisibility,
  BlackCellData
} from '@/lib/types/matrix';
import type { BaseDisplayMode } from '@/components/grid-system/types';
import type {
  DeviceInfo,
  StyleConfig,
  GridConfig,
  InteractionState,
  LegacyHookInterface
} from '@/lib/types/unified-state';

// ============================================================================
// BasicDataStore 兼容接口
// ============================================================================

/**
 * 兼容原有的 useBasicDataStore Hook
 */
export const useBasicDataStore = <T>(selector?: (state: any) => T) => {
  const store = useUnifiedMatrixStore();
  
  if (selector) {
    return selector({
      matrixData: store.matrixData,
      gridData: store.gridData,
      colorValues: store.colorValues,
      colorVisibility: store.colorVisibility,
      colorLevelRules: store.colorLevelRules,
      blackCellData: store.blackCellData,
      groupVisibility: store.groupVisibility,
      _isHydrated: store.isHydrated,
      
      // 操作方法
      regenerateMatrixData: store.regenerateMatrixData,
      initializeMatrixData: store.initializeMatrixData,
      getMatrixData: store.getMatrixData,
      getDataPointsAt: store.getDataPointsAt,
      getGroupData: (group: GroupType) => {
        const matrixData = store.getMatrixData();
        return matrixData ? matrixData.byGroup[group] || [] : [];
      },
      getColorData: (color: BasicColorType) => {
        const matrixData = store.getMatrixData();
        return matrixData ? matrixData.byColor[color] || [] : [];
      },
      getLevelData: (level: 1 | 2 | 3 | 4) => {
        const matrixData = store.getMatrixData();
        return matrixData ? matrixData.byLevel[level] || [] : [];
      },
      regenerateGridData: store.regenerateGridData,
      getGridData: store.getGridData,
      updateCellData: store.updateCellData,
      getCellAt: store.getCellAt,
      updateColorValues: (colorType: BasicColorType, values: Partial<ColorValue>) => {
        store.updateColorValues(colorType, values);
      },
      getColorValue: (colorType: BasicColorType) => store.colorValues[colorType],
      setColorVisibility: store.setColorVisibility,
      toggleColorLevel: store.toggleColorLevel,
      setGroupVisibility: store.setGroupVisibility,
      toggleGroupVisibility: (group: GroupType) => {
        store.toggleGroupVisibility(group);
      },
      setBlackCellData: (data: BlackCellData) => {
        store.setBlackCellData(data);
      },
      toggleBlackCellVisibility: () => {
        store.toggleBlackCellVisibility();
      },
      toggleAllColorCells: (show: boolean) => {
        store.toggleAllColorCells(show);
      },
      toggleAllGroups: (show: boolean) => {
        store.toggleAllGroups(show);
      },
      resetToDefaults: store.resetToDefaults,
      getAvailableLevels: (colorType: BasicColorType) => store.colorLevelRules[colorType],
      getAllColorTypes: () => Object.keys(store.colorValues) as BasicColorType[],
      getAllGroupTypes: () => Object.keys(store.groupVisibility) as GroupType[]
    });
  }

  // 返回完整的兼容接口
  return {
    matrixData: store.matrixData,
    gridData: store.gridData,
    colorValues: store.colorValues,
    colorVisibility: store.colorVisibility,
    colorLevelRules: store.colorLevelRules,
    blackCellData: store.blackCellData,
    groupVisibility: store.groupVisibility,
    _isHydrated: store.isHydrated,
    regenerateMatrixData: store.regenerateMatrixData,
    initializeMatrixData: store.initializeMatrixData,
    getMatrixData: store.getMatrixData,
    getDataPointsAt: store.getDataPointsAt,
    getGroupData: (group: GroupType) => {
      const matrixData = store.getMatrixData();
      return matrixData ? matrixData.byGroup[group] || [] : [];
    },
    getColorData: (color: BasicColorType) => {
      const matrixData = store.getMatrixData();
      return matrixData ? matrixData.byColor[color] || [] : [];
    },
    getLevelData: (level: 1 | 2 | 3 | 4) => {
      const matrixData = store.getMatrixData();
      return matrixData ? matrixData.byLevel[level] || [] : [];
    },
    regenerateGridData: store.regenerateGridData,
    getGridData: store.getGridData,
    updateCellData: store.updateCellData,
    getCellAt: store.getCellAt,
    updateColorValues: store.updateColorValues,
    getColorValue: (colorType: BasicColorType) => store.colorValues[colorType],
    setColorVisibility: store.setColorVisibility,
    toggleColorLevel: store.toggleColorLevel,
    setGroupVisibility: store.setGroupVisibility,
    toggleGroupVisibility: store.toggleGroupVisibility,
    setBlackCellData: store.setBlackCellData,
    toggleBlackCellVisibility: store.toggleBlackCellVisibility,
    toggleAllColorCells: store.toggleAllColorCells,
    toggleAllGroups: store.toggleAllGroups,
    resetToDefaults: store.resetToDefaults,
    getAvailableLevels: (colorType: BasicColorType) => store.colorLevelRules[colorType],
    getAllColorTypes: () => Object.keys(store.colorValues) as BasicColorType[],
    getAllGroupTypes: () => Object.keys(store.groupVisibility) as GroupType[]
  };
};

// 兼容的选择器Hook
export const useMatrixData = () => useUnifiedMatrixStore(state => state.matrixData);

export const useColorValues = (colorType?: BasicColorType) =>
  useUnifiedMatrixStore(state =>
    colorType ? state.colorValues[colorType] : state.colorValues
  );

export const useColorVisibility = (colorType?: BasicColorType) =>
  useUnifiedMatrixStore(state =>
    colorType ? state.colorVisibility[colorType] : state.colorVisibility
  );

export const useGroupVisibility = (group?: GroupType) =>
  useUnifiedMatrixStore(state =>
    group ? state.groupVisibility[group] : state.groupVisibility
  );

export const useBlackCellData = () =>
  useUnifiedMatrixStore(state => state.blackCellData);

export const useDataPointsAt = (x: number, y: number) =>
  useUnifiedMatrixStore(state => state.getDataPointsAt(x, y));

export const useGroupData = (group: GroupType) =>
  useUnifiedMatrixStore(state => {
    const matrixData = state.matrixData;
    return matrixData ? matrixData.byGroup[group] || [] : [];
  });

export const useColorData = (color: BasicColorType) =>
  useUnifiedMatrixStore(state => {
    const matrixData = state.matrixData;
    return matrixData ? matrixData.byColor[color] || [] : [];
  });

export const useLevelData = (level: 1 | 2 | 3 | 4) =>
  useUnifiedMatrixStore(state => {
    const matrixData = state.matrixData;
    return matrixData ? matrixData.byLevel[level] || [] : [];
  });

export const useGridData = () =>
  useUnifiedMatrixStore(state => state.gridData);

export const useCellAt = (x: number, y: number) =>
  useUnifiedMatrixStore(state => state.getCellAt(x, y));

// ============================================================================
// GridConfigStore 兼容接口
// ============================================================================

/**
 * 兼容原有的 useGridConfigStore Hook
 */
export const useGridConfigStore = <T>(selector?: (state: any) => T) => {
  const store = useUnifiedMatrixStore();
  
  if (selector) {
    return selector({
      baseDisplayMode: store.gridConfig.displayMode,
      gridConfig: store.gridConfig,
      colorModeEnabled: store.gridConfig.colorModeEnabled,
      setDisplayMode: (mode: BaseDisplayMode) => {
        store.updateGridConfig({ displayMode: mode });
      },
      setColorModeEnabled: (enabled: boolean) => {
        store.updateGridConfig({ colorModeEnabled: enabled });
      },
      toggleColorMode: () => {
        store.updateGridConfig({ colorModeEnabled: !store.gridConfig.colorModeEnabled });
      },
      updateGridConfig: store.updateGridConfig,
      resetGridConfig: () => {
        // 重置网格配置到默认值
        store.updateGridConfig({
          displayMode: 'coordinates',
          colorModeEnabled: false,
          grayModeEnabled: false,
          fontSize: 12,
          matrixMargin: 10,
          gridColor: '#e5e7eb',
          cellShape: 'rounded',
          showBorders: true,
          enableAnimations: true
        });
      },
      getFullGridConfig: () => store.gridConfig,
      isCellActive: (x: number, y: number): boolean => {
        // 根据显示模式判断单元格是否激活
        switch (store.gridConfig.displayMode) {
          case 'coordinates':
          case 'color':
          case 'value':
            return true;
          default:
            return false;
        }
      }
    });
  }

  return {
    baseDisplayMode: store.gridConfig.displayMode,
    gridConfig: store.gridConfig,
    colorModeEnabled: store.gridConfig.colorModeEnabled,
    setDisplayMode: (mode: BaseDisplayMode) => {
      store.updateGridConfig({ displayMode: mode });
    },
    setColorModeEnabled: (enabled: boolean) => {
      store.updateGridConfig({ colorModeEnabled: enabled });
    },
    toggleColorMode: () => {
      store.updateGridConfig({ colorModeEnabled: !store.gridConfig.colorModeEnabled });
    },
    updateGridConfig: store.updateGridConfig,
    resetGridConfig: () => {
      store.updateGridConfig({
        displayMode: 'coordinates',
        colorModeEnabled: false,
        grayModeEnabled: false,
        fontSize: 12,
        matrixMargin: 10,
        gridColor: '#e5e7eb',
        cellShape: 'rounded',
        showBorders: true,
        enableAnimations: true
      });
    },
    getFullGridConfig: () => store.gridConfig,
    isCellActive: (x: number, y: number): boolean => {
      switch (store.gridConfig.displayMode) {
        case 'coordinates':
        case 'color':
        case 'value':
          return true;
        default:
          return false;
      }
    }
  };
};

// 兼容的选择器Hook
export const useBaseDisplayMode = () => 
  useUnifiedMatrixStore(state => state.gridConfig.displayMode);

export const useGridConfig = () => 
  useUnifiedMatrixStore(state => state.gridConfig);

export const useColorModeEnabled = () => 
  useUnifiedMatrixStore(state => state.gridConfig.colorModeEnabled);

export const useGridConfigActions = () => {
  const store = useUnifiedMatrixStore();
  
  return useCallback(() => ({
    setDisplayMode: (mode: BaseDisplayMode) => {
      store.updateGridConfig({ displayMode: mode });
    },
    setColorModeEnabled: (enabled: boolean) => {
      store.updateGridConfig({ colorModeEnabled: enabled });
    },
    toggleColorMode: () => {
      store.updateGridConfig({ colorModeEnabled: !store.gridConfig.colorModeEnabled });
    },
    updateGridConfig: store.updateGridConfig,
    resetGridConfig: () => {
      store.updateGridConfig({
        displayMode: 'coordinates',
        colorModeEnabled: false,
        grayModeEnabled: false
      });
    },
    isCellActive: (x: number, y: number): boolean => {
      switch (store.gridConfig.displayMode) {
        case 'coordinates':
        case 'color':
        case 'value':
          return true;
        default:
          return false;
      }
    }
  }), [store]);
};

// ============================================================================
// StyleStore 兼容接口
// ============================================================================

/**
 * 兼容原有的 useStyleStore Hook
 */
export const useStyleStore = <T>(selector?: (state: any) => T) => {
  const store = useUnifiedMatrixStore();
  
  if (selector) {
    return selector({
      config: store.styleConfig,
      theme: 'light', // 默认主题，可以从设备信息推断
      presets: [], // 预设功能暂时不实现
      currentPresetId: null,
      isEditing: false,
      isDirty: false,
      updateConfig: store.updateStyleConfig,
      resetConfig: () => {
        // 重置样式配置到默认值
        store.updateStyleConfig({
          fontSize: 12,
          fontFamily: 'Inter, system-ui, sans-serif',
          fontWeight: 400,
          primaryColor: '#3b82f6',
          secondaryColor: '#64748b',
          backgroundColor: '#ffffff',
          textColor: '#1f2937',
          borderColor: '#e5e7eb',
          cellSize: 24,
          cellGap: 1,
          borderRadius: 4,
          padding: 8,
          margin: 4,
          animationDuration: 200,
          animationEasing: 'ease-in-out',
          enableAnimations: true,
          showGrid: true,
          showBorders: true,
          enableShadows: false,
          opacity: 1.0
        });
      },
      setTheme: (theme: 'light' | 'dark' | 'auto') => {
        // 主题切换逻辑，可以更新样式配置
        console.warn('Theme switching not fully implemented');
      }
    });
  }

  return {
    config: store.styleConfig,
    theme: 'light' as const,
    updateConfig: store.updateStyleConfig,
    // ... 其他方法
  };
};

// ============================================================================
// DynamicStyleStore 兼容接口
// ============================================================================

/**
 * 兼容原有的 useDynamicStyleStore Hook
 */
export const useDynamicStyleStore = <T>(selector?: (state: any) => T) => {
  const store = useUnifiedMatrixStore();
  
  if (selector) {
    return selector({
      config: {
        // 动态样式配置
        breakpoints: {
          mobile: 768,
          tablet: 1024,
          desktop: 1440,
          wide: 1920
        },
        hoverEffects: {
          enabled: true,
          scale: 1.05,
          opacity: 0.8,
          duration: 200
        },
        clickEffects: {
          enabled: true,
          scale: 0.95,
          duration: 150,
          ripple: true
        },
        focusEffects: {
          enabled: true,
          outlineWidth: 2,
          outlineColor: '#3b82f6',
          outlineOffset: 2
        },
        transitions: {
          default: 'all 0.2s ease-in-out',
          fast: 'all 0.1s ease-in-out',
          slow: 'all 0.3s ease-in-out'
        },
        autoResize: true,
        autoScale: true,
        maintainAspectRatio: true,
        enableGPUAcceleration: true,
        enableVirtualization: false,
        debounceDelay: 16
      },
      deviceInfo: store.deviceInfo,
      interactionState: store.interactionState,
      styleCache: store.cacheState.renderCache,
      performanceMetrics: {
        renderTime: store.performanceMetrics.renderTime,
        updateCount: store.performanceMetrics.updateCount,
        cacheHitRate: store.cacheState.cacheStats.hitRate
      },
      updateConfig: (updates: any) => {
        // 更新动态样式配置
        console.warn('Dynamic style config update not fully implemented');
      },
      updateDeviceInfo: (deviceInfo: Partial<DeviceInfo>) => {
        // 需要在统一状态管理器中添加此方法
        console.warn('updateDeviceInfo not fully implemented');
      },
      setHovered: store.setHovered,
      setClicked: store.setClicked,
      setFocused: store.setFocused,
      setDragged: (elementId: string | null) => {
        // 需要在统一状态管理器中添加此方法
        console.warn('setDragged not fully implemented');
      },
      getElementStyle: (elementId: string, baseStyle: any) => {
        // 获取元素的动态样式
        const isHovered = store.interactionState.hoveredElements.has(elementId);
        const isClicked = store.interactionState.clickedElements.has(elementId);
        const isFocused = store.interactionState.focusedElement === elementId;
        
        let dynamicStyle = { ...baseStyle };
        
        if (isHovered) {
          dynamicStyle = {
            ...dynamicStyle,
            transform: 'scale(1.05)',
            opacity: 0.8,
            transition: 'all 0.2s ease-in-out'
          };
        }
        
        if (isClicked) {
          dynamicStyle = {
            ...dynamicStyle,
            transform: 'scale(0.95)',
            transition: 'all 0.15s ease-in-out'
          };
        }
        
        if (isFocused) {
          dynamicStyle = {
            ...dynamicStyle,
            outline: '2px solid #3b82f6',
            outlineOffset: '2px'
          };
        }
        
        return dynamicStyle;
      },
      clearStyleCache: () => store.clearCache('render'),
      updatePerformanceMetrics: store.updatePerformanceMetrics
    });
  }

  return {
    deviceInfo: store.deviceInfo,
    interactionState: store.interactionState,
    setHovered: store.setHovered,
    setClicked: store.setClicked,
    setFocused: store.setFocused,
    getElementStyle: (elementId: string, baseStyle: any) => {
      const isHovered = store.interactionState.hoveredElements.has(elementId);
      const isClicked = store.interactionState.clickedElements.has(elementId);
      const isFocused = store.interactionState.focusedElement === elementId;
      
      let dynamicStyle = { ...baseStyle };
      
      if (isHovered) {
        dynamicStyle = {
          ...dynamicStyle,
          transform: 'scale(1.05)',
          opacity: 0.8,
          transition: 'all 0.2s ease-in-out'
        };
      }
      
      if (isClicked) {
        dynamicStyle = {
          ...dynamicStyle,
          transform: 'scale(0.95)',
          transition: 'all 0.15s ease-in-out'
        };
      }
      
      if (isFocused) {
        dynamicStyle = {
          ...dynamicStyle,
          outline: '2px solid #3b82f6',
          outlineOffset: '2px'
        };
      }
      
      return dynamicStyle;
    },
    clearStyleCache: () => store.clearCache('render'),
    updatePerformanceMetrics: store.updatePerformanceMetrics
  };
};

// ============================================================================
// 统一的兼容接口
// ============================================================================

/**
 * 提供完整的向后兼容接口
 */
export const useLegacyInterface = (): LegacyHookInterface => {
  const store = useUnifiedMatrixStore();
  
  return useMemo(() => ({
    // basicDataStore兼容接口
    matrixData: store.matrixData,
    gridData: store.gridData,
    colorValues: store.colorValues,
    colorVisibility: store.colorVisibility,
    regenerateMatrixData: store.regenerateMatrixData,
    initializeMatrixData: store.initializeMatrixData,
    
    // gridConfigStore兼容接口
    baseDisplayMode: store.gridConfig.displayMode,
    gridConfig: store.gridConfig,
    colorModeEnabled: store.gridConfig.colorModeEnabled,
    setDisplayMode: (mode: BaseDisplayMode) => {
      store.updateGridConfig({ displayMode: mode });
    },
    setColorModeEnabled: (enabled: boolean) => {
      store.updateGridConfig({ colorModeEnabled: enabled });
    },
    
    // styleStore兼容接口
    config: store.styleConfig,
    theme: 'light' as const,
    updateConfig: store.updateStyleConfig,
    
    // dynamicStyleStore兼容接口
    deviceInfo: store.deviceInfo,
    interactionState: store.interactionState,
    getElementStyle: (elementId: string, baseStyle: any) => {
      const isHovered = store.interactionState.hoveredElements.has(elementId);
      let dynamicStyle = { ...baseStyle };
      
      if (isHovered) {
        dynamicStyle = {
          ...dynamicStyle,
          transform: 'scale(1.05)',
          transition: 'all 0.2s ease-in-out'
        };
      }
      
      return dynamicStyle;
    }
  }), [store]);
};
/**
 * 统一Hydration管理器
 * 🎯 核心价值：统一管理所有store的hydration状态，避免重复逻辑
 * 📦 功能范围：hydration状态跟踪、初始化协调、状态同步
 * ⚡ 性能优化：避免重复的hydration检查和初始化
 */

import { create } from 'zustand';
import { subscribeWithSelector } from 'zustand/middleware';

// Hydration状态接口
export interface HydrationState {
  // 各个store的hydration状态
  basicDataHydrated: boolean;
  gridConfigHydrated: boolean;
  styleHydrated: boolean;
  
  // 全局hydration状态
  isFullyHydrated: boolean;
  
  // 初始化状态
  isInitializing: boolean;
  initializationError: string | null;
  
  // 操作方法
  setStoreHydrated: (storeName: keyof HydrationState, hydrated: boolean) => void;
  setInitializing: (initializing: boolean) => void;
  setInitializationError: (error: string | null) => void;
  reset: () => void;
}

// 初始状态
const initialState = {
  basicDataHydrated: false,
  gridConfigHydrated: false,
  styleHydrated: false,
  isFullyHydrated: false,
  isInitializing: false,
  initializationError: null,
};

// 创建Hydration管理器
export const useHydrationManager = create<HydrationState>()(
  subscribeWithSelector((set, get) => ({
    ...initialState,
    
    setStoreHydrated: (storeName: keyof HydrationState, hydrated: boolean) => {
      set((state) => {
        const newState = { ...state, [storeName]: hydrated };
        
        // 检查是否所有store都已hydrated
        const allHydrated = newState.basicDataHydrated && 
                           newState.gridConfigHydrated && 
                           newState.styleHydrated;
        
        return {
          ...newState,
          isFullyHydrated: allHydrated,
        };
      });
    },
    
    setInitializing: (initializing: boolean) => {
      set({ isInitializing: initializing });
    },
    
    setInitializationError: (error: string | null) => {
      set({ initializationError: error });
    },
    
    reset: () => {
      set(initialState);
    },
  }))
);

// 便捷的选择器hooks
export const useIsFullyHydrated = () => 
  useHydrationManager((state) => state.isFullyHydrated);

export const useIsInitializing = () => 
  useHydrationManager((state) => state.isInitializing);

export const useInitializationError = () => 
  useHydrationManager((state) => state.initializationError);

export const useStoreHydrationStatus = () => 
  useHydrationManager((state) => ({
    basicData: state.basicDataHydrated,
    gridConfig: state.gridConfigHydrated,
    style: state.styleHydrated,
  }));

// Hydration管理器操作hooks
export const useHydrationActions = () => {
  const setStoreHydrated = useHydrationManager((state) => state.setStoreHydrated);
  const setInitializing = useHydrationManager((state) => state.setInitializing);
  const setInitializationError = useHydrationManager((state) => state.setInitializationError);
  const reset = useHydrationManager((state) => state.reset);
  
  return {
    setStoreHydrated,
    setInitializing,
    setInitializationError,
    reset,
  };
};

// 初始化协调器
export class HydrationCoordinator {
  private static instance: HydrationCoordinator;
  private initializationPromise: Promise<void> | null = null;
  
  static getInstance(): HydrationCoordinator {
    if (!HydrationCoordinator.instance) {
      HydrationCoordinator.instance = new HydrationCoordinator();
    }
    return HydrationCoordinator.instance;
  }
  
  /**
   * 协调所有store的初始化
   */
  async coordinateInitialization(): Promise<void> {
    if (this.initializationPromise) {
      return this.initializationPromise;
    }
    
    this.initializationPromise = this.performInitialization();
    return this.initializationPromise;
  }
  
  private async performInitialization(): Promise<void> {
    const { setInitializing, setInitializationError } = useHydrationManager.getState();
    
    try {
      setInitializing(true);
      setInitializationError(null);
      
      // 等待所有store完成hydration
      await this.waitForHydration();
      
      // 执行初始化后的数据生成
      await this.initializeData();
      
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown initialization error';
      setInitializationError(errorMessage);
      throw error;
    } finally {
      setInitializing(false);
    }
  }
  
  private async waitForHydration(): Promise<void> {
    return new Promise((resolve) => {
      const unsubscribe = useHydrationManager.subscribe(
        (state) => state.isFullyHydrated,
        (isFullyHydrated) => {
          if (isFullyHydrated) {
            unsubscribe();
            resolve();
          }
        }
      );
      
      // 如果已经hydrated，立即resolve
      if (useHydrationManager.getState().isFullyHydrated) {
        unsubscribe();
        resolve();
      }
    });
  }
  
  private async initializeData(): Promise<void> {
    // 这里可以添加需要在hydration完成后执行的初始化逻辑
    // 例如：生成矩阵数据、初始化配置等
    
    // 模拟异步初始化
    await new Promise(resolve => setTimeout(resolve, 100));
  }
  
  /**
   * 重置初始化状态
   */
  reset(): void {
    this.initializationPromise = null;
    useHydrationManager.getState().reset();
  }
}

// 全局初始化协调器实例
export const hydrationCoordinator = HydrationCoordinator.getInstance();

// React Hook for easy initialization
export function useHydrationCoordinator() {
  const isFullyHydrated = useIsFullyHydrated();
  const isInitializing = useIsInitializing();
  const error = useInitializationError();
  
  const initialize = async () => {
    try {
      await hydrationCoordinator.coordinateInitialization();
    } catch (error) {
      console.error('Hydration initialization failed:', error);
    }
  };
  
  const reset = () => {
    hydrationCoordinator.reset();
  };
  
  return {
    isFullyHydrated,
    isInitializing,
    error,
    initialize,
    reset,
  };
}

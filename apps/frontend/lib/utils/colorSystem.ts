// 统一颜色系统 - 整合colorUtils.ts功能，解决颜色工具重复问题

import { logger } from '@/lib/hooks/useLogManager';
import type { ColorType, ColorInfo, AllColorInfo, BasicColorType } from '@/lib/types/color';
import {
  COLOR_CSS_MAP,
  BLACK_CSS_MAP,
  COLOR_NAMES,
  COLOR_SHORT_NAMES,
  COLOR_NUMBER_MAP,
  COLOR_PRIORITY_ORDER,
} from '@/stores/constants/colors';

// ==================== 颜色工具函数部分 (来自colorUtils.ts) ====================

// 获取颜色CSS样式
export const getColorCSS = (colorType: ColorType, styleType: string): string => {
  return COLOR_CSS_MAP[colorType]?.[styleType as keyof typeof COLOR_CSS_MAP[ColorType]] || '';
};

// 获取颜色CSS映射
export const getColorCSSMap = (colorType: ColorType): typeof COLOR_CSS_MAP[ColorType] | undefined => {
  return COLOR_CSS_MAP[colorType];
};

// 获取黑色CSS样式
export const getBlackCSS = (styleType: string): string => {
  return BLACK_CSS_MAP[styleType as keyof typeof BLACK_CSS_MAP] || '';
};

// 获取颜色名称
export const getColorName = (colorType: ColorType | 'black'): string => {
  return COLOR_NAMES[colorType];
};

// 获取颜色显示名称（支持完整/简短格式）
export const getColorDisplayName = (
  colorType: BasicColorType, 
  format: 'full' | 'short' = 'full'
): string => {
  return format === 'short' ? COLOR_SHORT_NAMES[colorType] : COLOR_NAMES[colorType];
};

// 获取颜色数字编码
export const getColorNumber = (colorType: Exclude<BasicColorType, 'black'>): string => {
  return COLOR_NUMBER_MAP[colorType] || '';
};

// 获取颜色优先级
export const getColorPriority = (colorType: BasicColorType): number => {
  return COLOR_PRIORITY_ORDER.indexOf(colorType);
};

// 根据级别获取单元格样式
export const getCellStyle = (colorType: ColorType, level: number, clicked = false): string => {
  const baseStyles = getColorCSS(colorType, `level${level}`);
  const clickedStyles = clicked ? 'scale-110 z-20 shadow-lg' : '';
  return `${baseStyles} ${clickedStyles} transition-all duration-200`.trim();
};

// Tab样式生成器（用于颜色选择等）
export const getTabStyle = (tabKey: string, activeTab: string): string => {
  const isActive = tabKey === activeTab;
  return isActive ? 'bg-white text-gray-800 border-gray-400 shadow-sm' : 'bg-gray-100 text-gray-600 border-gray-200 hover:bg-gray-200';
}; 

// ==================== 颜色索引系统部分 (原有功能) ====================

// 创建坐标索引 - 预计算所有坐标的颜色信息
export class ColorCoordinateIndex {
  private coordinateIndex: Map<string, ColorInfo> = new Map();
  private specialCoordinates: Map<string, string> = new Map();
  private allColorInfoCache: Map<string, AllColorInfo> = new Map(); // 缓存机制
  private readonly cacheLimit = 1000; // 缓存限制，避免内存泄漏
  
  constructor(allColorCoordinates: Record<ColorType, any>, specialCoordinates?: Map<string, string>) {
    this.buildIndex(allColorCoordinates);
    if (specialCoordinates) {
      this.specialCoordinates = specialCoordinates;
    }
  }

  private buildIndex(allColorCoordinates: Record<ColorType, any>) {
    Object.entries(allColorCoordinates).forEach(([colorType, coordsByLevel]) => {
      if (!coordsByLevel) return;

      Object.entries(coordsByLevel).forEach(([level, coordinates]) => {
        if (!Array.isArray(coordinates)) return;

        coordinates.forEach((coordinate: unknown) => {
          // 类型断言以便访问属性
          const coordObj = coordinate as any;

          const coords = coordObj.coords || [coordObj.x, coordObj.y];
          const [x, y] = coords;
          const key = `${x},${y}`;

                     this.coordinateIndex.set(key, {
             colorType: colorType as ColorType,
             level: parseInt(level.replace('level', '')) as 1 | 2 | 3 | 4,
             group: coordObj.group,
             exists: true,
           });
        });
      });
    });

    logger.debug('color', `ColorCoordinateIndex 构建完成，包含 ${this.coordinateIndex.size} 个坐标`);
  }

  // 一次获取所有颜色信息 - 替代8次函数调用
  getAllColorInfo(x: number, y: number): AllColorInfo {
    const key = `${x},${y}`;
    
    // 检查缓存
    if (this.allColorInfoCache.has(key)) {
      return this.allColorInfoCache.get(key)!;
    }
    
    const result: AllColorInfo = {};
    
    // 检查黑色特殊坐标
    const specialLetter = this.specialCoordinates.get(key);
    if (specialLetter) {
      result.black = { exists: true, letter: specialLetter };
    }
    
    // 检查是否有任何颜色信息
    const info = this.coordinateIndex.get(key);
    if (info) {
      result[info.colorType] = info;
    }
    
    // 缓存结果（如果缓存未满）
    if (this.allColorInfoCache.size < this.cacheLimit) {
      this.allColorInfoCache.set(key, result);
    } else if (this.allColorInfoCache.size >= this.cacheLimit) {
      // LRU策略：清理最旧的缓存条目
      const firstKey = this.allColorInfoCache.keys().next().value;
      if (firstKey) {
        this.allColorInfoCache.delete(firstKey);
        this.allColorInfoCache.set(key, result);
      }
    }
    
    return result;
  }

  // 检查坐标是否有特定颜色
  hasColor(x: number, y: number, colorType: ColorType): boolean {
    const allColors = this.getAllColorInfo(x, y);
    return !!allColors[colorType];
  }

  // 获取特定颜色的信息
  getColorInfo(x: number, y: number, colorType: ColorType): ColorInfo | null {
    const allColors = this.getAllColorInfo(x, y);
    return allColors[colorType] || null;
  }

  // 清除缓存
  clearCache(): void {
    this.allColorInfoCache.clear();
    logger.debug('color', 'ColorCoordinateIndex 缓存已清除');
  }

  // 获取索引统计信息
  getStats() {
    return {
      totalCoordinates: this.coordinateIndex.size,
      specialCoordinates: this.specialCoordinates.size,
      cacheSize: this.allColorInfoCache.size,
      cacheLimit: this.cacheLimit,
    };
  }
}

// 获取颜色对应的CSS类（保持向后兼容）
export function getColorByLevel(colorType: ColorType, level: number, colorCssMap?: any): string {
  if (!colorCssMap) {
    // 如果没有提供 CSS 映射，使用新的统一函数
    return getColorCSS(colorType, `level${level}`) || 'bg-gray-300';
  }
  return colorCssMap[colorType]?.[`level${level}`] || colorCssMap[colorType]?.level1 || 'bg-gray-300';
}

// 导出所有颜色工具函数，替代单独的colorUtils.ts文件
export {
  // 重新导出类型
  type ColorType,
  type BasicColorType,
  type ColorInfo,
  type AllColorInfo,
}; 
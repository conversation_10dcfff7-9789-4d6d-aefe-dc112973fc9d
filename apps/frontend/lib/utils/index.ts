/**
 * Utils模块统一导出文件
 * 🎯 核心价值：提供统一的工具函数导出入口
 * 📦 重构：统一管理所有工具函数，便于导入和使用
 */

// CSS类名合并工具
export { cn } from './cn';

// 颜色相关工具函数 (从整合后的colorSystem.ts导入)
export {
  getColorCSS,
  getColorCSSMap,
  getBlackCSS,
  getColorName,
  getColorDisplayName,
  getColorNumber,
  getColorPriority,
  getCellStyle,
  getTabStyle,
  ColorCoordinateIndex,
  getColorByLevel,
} from './colorSystem';

// 单元格相关工具函数 - 注意：generateGridData已移至stores/basicDataStore.ts
// cellUtils.ts已废弃，功能整合到cellDataHelpers.ts和basicDataStore.ts

// 单元格数据辅助函数
export {
  initializeGridData,
  batchUpdateCellColors,
  batchUpdateCellGroups,
  validateCellData,
  cloneCellData,
  compareCellData,
  findCellByCoordinates,
  findCellByPosition,
  getCellsInRange,
  calculateDistance,
  getCellNeighbors,
  formatCellForDisplay,
  exportCellsToJson,
  importCellsFromJson,
  getGroupCellIds,
} from './cellDataHelpers';

// 按钮工具函数
export {
  getButtonStyle,
  getGridStyle,
  getAdvancedButtonStyle,
  getActiveButtonStyle,
  getColorButtonStyle,
  getGridButtonStyle,
  getModeButtonStyle,
  getDangerButtonStyle,
  getSuccessButtonStyle,
  getButtonStylePlaceholder,
} from './buttonUtils';

// 样式工具函数 - 暂时移除，文件不存在
// TODO: 如需要样式工具函数，请创建 styleUtils.ts 文件

// 变体工具函数 - 暂时移除，文件不存在
// TODO: 如需要变体工具函数，请创建 variants.ts 文件

// 颜色坐标工具函数 - 用于选择性颜色显示功能
export {
  getCellColorByCoordinate,
  isCoordinateColor,
  isCoordinateInActiveColors,
  getActiveColorCoordinates,
  validateActiveColors,
  getColorDistributionStats,
  batchCheckCoordinateColors,
  getRegionColorDistribution,
  createColorActivationSnapshot,
  compareColorActivationSnapshots,
  debounceColorActivation,
  ColorLookupCache,
  defaultColorCache,
  getCachedCellColor,
} from './colorCoordinateUtils';

// 矩阵计算工具函数
export {
  isValidCoordinate,
  generateTransformRule,
  calculateGroupCoordinates,
  generateMatrixData,
  generateGridData,
} from './matrixUtils';

// 矩阵辅助工具函数
export {
  generateConsistentColorVisibility,
  generateDefaultColorVisibility,
  generateDefaultGroupVisibility,
  generateDefaultBlackCellData,
  getCellColorByCoordinate as getMatrixCellColorByCoordinate,
  getAllColorTypes,
  getAllGroupTypes,
  getAvailableLevels as getMatrixAvailableLevels,
  getColorValue,
} from './matrixHelpers';

// 存储工具函数
export {
  createCustomStorage,
  validateAndFixMatrixData,
  isValidPersistedState,
  createMigrationFunction,
} from './storageUtils';



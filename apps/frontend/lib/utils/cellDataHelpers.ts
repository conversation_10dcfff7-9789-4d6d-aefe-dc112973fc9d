/**
 * cellDataHelpers - 单元格数据辅助函数
 * 🎯 核心价值：提供单元格数据的初始化、批量操作、验证等工具函数
 * 📦 功能范围：数据初始化、批量更新、数据验证、格式转换
 * 🔄 架构设计：纯函数设计，配合CellDataManager使用
 */

import type { CellData } from '@/lib/types/grid';
import type { CellDataManager, ColorType, LevelType, GroupType } from '@/lib/hooks/useCellDataManager';

/**
 * 初始化网格数据
 * 创建一个基础的网格数据集
 */
export function initializeGridData(manager: CellDataManager, rows = 33, cols = 33): void {
  // 清空现有数据
  manager.clear();
  
  // 计算网格中心
  const centerRow = Math.floor(rows / 2);
  const centerCol = Math.floor(cols / 2);
  
  // 创建网格数据
  for (let row = 0; row < rows; row++) {
    for (let col = 0; col < cols; col++) {
      // 计算相对于中心的坐标
      const x = col - centerCol;
      const y = centerRow - row; // Y轴向上为正
      
      // 计算索引
      const index = row * cols + col;
      
      // 创建单元格数据
      const cellData: Omit<CellData, 'id'> = {
        row,
        col,
        x,
        y,
        index,
        color: 'black', // 默认颜色
        colorMappingValue: 0,
        level: 1,
        group: null,
        isActive: false,
        number: index + 1,
      };
      
      manager.createCell(cellData);
    }
  }
}

/**
 * 批量更新单元格颜色
 */
export function batchUpdateCellColors(
  manager: CellDataManager,
  cellIds: string[],
  color: ColorType,
  level?: LevelType
): boolean {
  try {
    cellIds.forEach(id => {
      const updates: Partial<CellData> = { color };
      if (level !== undefined) {
        updates.level = level;
      }
      manager.updateCell(id, updates);
    });
    return true;
  } catch (error) {
    process.env.NODE_ENV === 'development' && console.error('批量更新颜色失败:', error);
    return false;
  }
}

/**
 * 批量更新单元格分组
 */
export function batchUpdateCellGroups(
  manager: CellDataManager,
  cellIds: string[],
  group: GroupType | null
): boolean {
  try {
    cellIds.forEach(id => {
      manager.updateCell(id, { group });
    });
    return true;
  } catch (error) {
    process.env.NODE_ENV === 'development' && console.error('批量更新分组失败:', error);
    return false;
  }
}

/**
 * 获取指定分组的所有单元格ID
 */
export function getGroupCellIds(manager: CellDataManager, group: GroupType): string[] {
  return manager.getCellsByGroup(group).map(cell => cell.id);
}

/**
 * 验证单元格数据
 */
export function validateCellData(cell: unknown): cell is CellData {
  if (!cell || typeof cell !== 'object') return false;

  // 类型断言以便访问属性
  const cellObj = cell as any;

  // 必需字段检查
  const requiredFields = ['id', 'row', 'col', 'x', 'y', 'index', 'color', 'level'];
  for (const field of requiredFields) {
    if (!(field in cellObj)) return false;
  }

  // 类型检查
  if (typeof cellObj.id !== 'string') return false;
  if (typeof cellObj.row !== 'number') return false;
  if (typeof cellObj.col !== 'number') return false;
  if (typeof cellObj.x !== 'number') return false;
  if (typeof cellObj.y !== 'number') return false;
  if (typeof cellObj.index !== 'number') return false;
  if (typeof cellObj.color !== 'string') return false;
  if (typeof cellObj.level !== 'number') return false;

  // 范围检查
  if (cellObj.level < 1 || cellObj.level > 4) return false;

  return true;
}

/**
 * 根据坐标查找单元格
 */
export function findCellByCoordinates(
  manager: CellDataManager,
  x: number,
  y: number
): CellData | undefined {
  return manager.queryCells({ x, y })[0];
}

/**
 * 根据位置查找单元格
 */
export function findCellByPosition(
  manager: CellDataManager,
  row: number,
  col: number
): CellData | undefined {
  return manager.getCellByPosition(row, col);
}

/**
 * 获取指定范围内的单元格
 */
export function getCellsInRange(
  manager: CellDataManager,
  startX: number,
  startY: number,
  endX: number,
  endY: number
): CellData[] {
  const cells: CellData[] = [];
  const minX = Math.min(startX, endX);
  const maxX = Math.max(startX, endX);
  const minY = Math.min(startY, endY);
  const maxY = Math.max(startY, endY);
  
  for (let x = minX; x <= maxX; x++) {
    for (let y = minY; y <= maxY; y++) {
      const cell = findCellByCoordinates(manager, x, y);
      if (cell) {
        cells.push(cell);
      }
    }
  }
  
  return cells;
}

/**
 * 计算两个单元格之间的距离
 */
export function calculateDistance(cell1: CellData, cell2: CellData): number {
  const dx = cell1.x - cell2.x;
  const dy = cell1.y - cell2.y;
  return Math.sqrt(dx * dx + dy * dy);
}

/**
 * 获取单元格的邻居
 */
export function getCellNeighbors(
  manager: CellDataManager,
  cell: CellData,
  radius = 1
): CellData[] {
  const neighbors: CellData[] = [];
  
  for (let dx = -radius; dx <= radius; dx++) {
    for (let dy = -radius; dy <= radius; dy++) {
      if (dx === 0 && dy === 0) continue; // 跳过自己
      
      const neighborCell = findCellByCoordinates(manager, cell.x + dx, cell.y + dy);
      if (neighborCell) {
        neighbors.push(neighborCell);
      }
    }
  }
  
  return neighbors;
}

/**
 * 格式化单元格数据为显示文本
 */
export function formatCellForDisplay(cell: CellData): string {
  return `Cell(${cell.x}, ${cell.y}) - ${cell.color} L${cell.level}${cell.group ? ` G${cell.group}` : ''}`;
}

/**
 * 将单元格数据导出为JSON
 */
export function exportCellsToJson(manager: CellDataManager): string {
  const cells = manager.getAllCells();
  return JSON.stringify(cells, null, 2);
}

/**
 * 从JSON导入单元格数据
 */
export function importCellsFromJson(manager: CellDataManager, jsonData: string): boolean {
  try {
    const cells = JSON.parse(jsonData) as CellData[];
    
    // 验证数据
    if (!Array.isArray(cells) || !cells.every(validateCellData)) {
      throw new Error('无效的单元格数据格式');
    }
    
    // 清空现有数据
    manager.clear();
    
    // 导入新数据
    cells.forEach(cell => {
      const { id, ...cellData } = cell; // 移除ID，让manager重新生成
      manager.createCell(cellData);
    });
    
    return true;
  } catch (error) {
    console.error('导入数据失败:', error);
    return false;
  }
}

/**
 * 创建单元格数据的深拷贝
 */
export function cloneCellData(cell: CellData): CellData {
  return {
    ...cell,
    // 如果有嵌套对象，需要深拷贝
  };
}

/**
 * 比较两个单元格数据是否相等
 */
export function compareCellData(cell1: CellData, cell2: CellData): boolean {
  const keys = Object.keys(cell1) as (keyof CellData)[];
  return keys.every(key => cell1[key] === cell2[key]);
}

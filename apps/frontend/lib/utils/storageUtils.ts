/**
 * 存储工具函数
 * 🎯 职责：处理复杂数据结构的序列化和反序列化
 * 📦 功能：Map 对象序列化、localStorage 操作、数据恢复
 * ✅ 用途：为 Zustand persist 中间件提供自定义存储配置
 */

import type { MatrixData } from '@/lib/types/matrix';
import { generateMatrixData } from './matrixUtils';

/**
 * 自定义存储配置，正确处理 Map 对象的序列化/反序列化
 * 解决 Zustand persist 中间件无法正确处理 Map 对象的问题
 */
export const createCustomStorage = () => ({
  /**
   * 从 localStorage 读取数据并恢复 Map 对象
   * @param name 存储键名
   * @returns 解析后的数据或 null
   */
  getItem: (name: string) => {
    const str = localStorage.getItem(name);
    if (!str) return null;

    try {
      const parsed = JSON.parse(str);

      // 重新创建 Map 对象
      if (parsed.state?.matrixData?.byCoordinate && Array.isArray(parsed.state.matrixData.byCoordinate)) {
        // 从数组格式重新创建 Map
        parsed.state.matrixData.byCoordinate = new Map(parsed.state.matrixData.byCoordinate);
      }

      return parsed;
    } catch (error) {
      console.error('Failed to parse stored data:', error);
      return null;
    }
  },

  /**
   * 将数据序列化并存储到 localStorage
   * @param name 存储键名
   * @param value 要存储的数据
   */
  setItem: (name: string, value: any) => {
    try {
      // 将 Map 转换为数组进行序列化
      const serializable = { ...value };
      
      if (serializable.state?.matrixData?.byCoordinate instanceof Map) {
        serializable.state.matrixData.byCoordinate = Array.from(
          serializable.state.matrixData.byCoordinate.entries()
        );
      }

      localStorage.setItem(name, JSON.stringify(serializable));
    } catch (error) {
      console.error('Failed to store data:', error);
    }
  },

  /**
   * 从 localStorage 删除数据
   * @param name 存储键名
   */
  removeItem: (name: string) => {
    localStorage.removeItem(name);
  }
});

/**
 * 验证并修复矩阵数据结构
 * 确保 matrixData.byCoordinate 是正确的 Map 对象
 * @param matrixData 要验证的矩阵数据
 * @returns 修复后的矩阵数据
 */
export const validateAndFixMatrixData = (matrixData: unknown): MatrixData => {
  if (!matrixData) {
    console.warn('MatrixData is null or undefined, generating new data');
    return generateMatrixData();
  }

  // 类型断言以便访问属性
  const matrixObj = matrixData as any;

  // 检查 byCoordinate 是否是 Map 对象
  if (!(matrixObj.byCoordinate instanceof Map)) {
    console.warn('MatrixData.byCoordinate is not a Map, regenerating matrix data');
    return generateMatrixData();
  }

  // 检查 Map 是否有数据
  if (matrixObj.byCoordinate.size === 0) {
    console.warn('MatrixData.byCoordinate is empty, regenerating matrix data');
    return generateMatrixData();
  }

  return matrixObj as MatrixData;
};

/**
 * 检查存储的数据是否有效
 * @param persistedState 持久化的状态数据
 * @returns 是否有效
 */
export const isValidPersistedState = (persistedState: unknown): boolean => {
  if (!persistedState || typeof persistedState !== 'object') {
    return false;
  }

  // 类型断言以便访问属性
  const stateObj = persistedState as any;

  if (!stateObj.matrixData) {
    return false;
  }

  // 检查关键字段是否存在
  const requiredFields = ['byCoordinate', 'byGroup', 'byColor', 'byLevel'];
  return requiredFields.every(field => stateObj.matrixData[field] !== undefined);
};

/**
 * 创建数据迁移函数
 * 确保数据结构的完整性
 * @param generateMatrixDataFn 生成矩阵数据的函数
 * @returns 迁移函数
 */
export const createMigrationFunction = (generateMatrixDataFn: () => MatrixData) => {
  return (persistedState: any, version: number) => {
    // 确保 matrixData.byCoordinate 是 Map 对象
    if (persistedState?.matrixData) {
      persistedState.matrixData = validateAndFixMatrixData(persistedState.matrixData);
    }

    return persistedState;
  };
};

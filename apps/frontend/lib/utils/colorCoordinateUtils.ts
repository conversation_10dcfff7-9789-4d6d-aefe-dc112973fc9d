/**
 * Color Coordinate Utilities - 颜色坐标工具函数
 * 为选择性颜色显示功能提供便捷的工具函数
 * 基于 ColorCoordinateService 提供高级抽象
 */

import { ColorCoordinateService } from '@/lib/services/ColorCoordinateService';
import type { BasicColorType } from '@/lib/types/matrix';

/**
 * 获取单元格在指定坐标的颜色（优化版本）
 * 这是 getCellColorByCoordinate 的优化版本，专为灰度模式设计
 * @param x X坐标
 * @param y Y坐标
 * @returns 颜色类型，如果坐标没有颜色数据则返回null
 */
export function getCellColorByCoordinate(x: number, y: number): BasicColorType | null {
  return ColorCoordinateService.getCellColorByCoordinate(x, y);
}

/**
 * 检查坐标是否包含指定颜色
 * @param x X坐标
 * @param y Y坐标
 * @param targetColor 目标颜色
 * @returns 是否匹配指定颜色
 */
export function isCoordinateColor(x: number, y: number, targetColor: BasicColorType): boolean {
  const cellColor = getCellColorByCoordinate(x, y);
  return cellColor === targetColor;
}

/**
 * 检查坐标是否包含任何激活的颜色
 * @param x X坐标
 * @param y Y坐标
 * @param activeColors 激活的颜色集合
 * @returns 是否包含任何激活的颜色
 */
export function isCoordinateInActiveColors(
  x: number, 
  y: number, 
  activeColors: Set<string>
): boolean {
  const cellColor = getCellColorByCoordinate(x, y);
  return cellColor !== null && activeColors.has(cellColor);
}

/**
 * 获取所有激活颜色的坐标集合
 * @param activeColors 激活的颜色集合
 * @returns 所有激活颜色的坐标数组
 */
export function getActiveColorCoordinates(activeColors: Set<string>): [number, number][] {
  const coordinates: [number, number][] = [];
  
  for (const color of activeColors) {
    if (ColorCoordinateService.isValidColorType(color)) {
      const colorCoords = ColorCoordinateService.getCoordinatesByColor(color as BasicColorType);
      coordinates.push(...colorCoords);
    }
  }
  
  return coordinates;
}

/**
 * 验证颜色激活状态的有效性
 * @param activeColors 激活的颜色集合
 * @returns 验证结果和错误信息
 */
export function validateActiveColors(activeColors: Set<string>): {
  isValid: boolean;
  invalidColors: string[];
  validColors: string[];
} {
  const invalidColors: string[] = [];
  const validColors: string[] = [];
  
  for (const color of activeColors) {
    if (ColorCoordinateService.isValidColorType(color)) {
      validColors.push(color);
    } else {
      invalidColors.push(color);
    }
  }
  
  return {
    isValid: invalidColors.length === 0,
    invalidColors,
    validColors
  };
}

/**
 * 获取颜色在网格中的分布统计
 * @param color 颜色类型
 * @returns 颜色分布统计信息
 */
export function getColorDistributionStats(color: BasicColorType) {
  if (!ColorCoordinateService.isValidColorType(color)) {
    return null;
  }
  
  const coordinates = ColorCoordinateService.getCoordinatesByColor(color);
  
  if (coordinates.length === 0) {
    return {
      color,
      totalCount: 0,
      levels: {},
      coordinateRanges: { x: { min: 0, max: 0 }, y: { min: 0, max: 0 } }
    };
  }
  
  // 统计各级别的坐标数量
  const levels: Record<number, number> = {};
  for (let level = 1; level <= 4; level++) {
    const levelCoords = ColorCoordinateService.getCoordinatesByColorAndLevel(color, level as 1 | 2 | 3 | 4);
    if (levelCoords.length > 0) {
      levels[level] = levelCoords.length;
    }
  }
  
  // 计算坐标范围
  const xCoords = coordinates.map(([x]) => x);
  const yCoords = coordinates.map(([, y]) => y);
  
  return {
    color,
    totalCount: coordinates.length,
    levels,
    coordinateRanges: {
      x: { min: Math.min(...xCoords), max: Math.max(...xCoords) },
      y: { min: Math.min(...yCoords), max: Math.max(...yCoords) }
    }
  };
}

/**
 * 批量检查多个坐标的颜色状态
 * @param coordinates 坐标数组
 * @param activeColors 激活的颜色集合
 * @returns 每个坐标的颜色状态信息
 */
export function batchCheckCoordinateColors(
  coordinates: [number, number][],
  activeColors: Set<string>
): Array<{
  coords: [number, number];
  color: BasicColorType | null;
  isActive: boolean;
}> {
  return coordinates.map(([x, y]) => {
    const color = getCellColorByCoordinate(x, y);
    const isActive = color !== null && activeColors.has(color);
    
    return {
      coords: [x, y],
      color,
      isActive
    };
  });
}

/**
 * 获取指定区域内的颜色分布
 * @param minX 最小X坐标
 * @param maxX 最大X坐标
 * @param minY 最小Y坐标
 * @param maxY 最大Y坐标
 * @param activeColors 激活的颜色集合（可选）
 * @returns 区域内的颜色分布信息
 */
export function getRegionColorDistribution(
  minX: number,
  maxX: number,
  minY: number,
  maxY: number,
  activeColors?: Set<string>
) {
  const regionCoords = ColorCoordinateService.getColorCoordinatesInRange(minX, maxX, minY, maxY);
  
  const distribution: Record<string, number> = {};
  const activeDistribution: Record<string, number> = {};
  
  regionCoords.forEach(({ color }) => {
    distribution[color] = (distribution[color] || 0) + 1;
    
    if (activeColors && activeColors.has(color)) {
      activeDistribution[color] = (activeDistribution[color] || 0) + 1;
    }
  });
  
  return {
    totalCoordinates: regionCoords.length,
    colorDistribution: distribution,
    activeColorDistribution: activeColors ? activeDistribution : null,
    region: { minX, maxX, minY, maxY }
  };
}

/**
 * 创建颜色激活状态的快照
 * @param activeColors 当前激活的颜色集合
 * @returns 颜色激活状态快照
 */
export function createColorActivationSnapshot(activeColors: Set<string>) {
  const snapshot = {
    timestamp: Date.now(),
    activeColors: Array.from(activeColors),
    colorStats: {} as Record<string, ReturnType<typeof getColorDistributionStats>>
  };
  
  // 为每个激活的颜色生成统计信息
  for (const color of activeColors) {
    if (ColorCoordinateService.isValidColorType(color)) {
      snapshot.colorStats[color] = getColorDistributionStats(color as BasicColorType);
    }
  }
  
  return snapshot;
}

/**
 * 比较两个颜色激活状态
 * @param snapshot1 第一个快照
 * @param snapshot2 第二个快照
 * @returns 比较结果
 */
export function compareColorActivationSnapshots(
  snapshot1: ReturnType<typeof createColorActivationSnapshot>,
  snapshot2: ReturnType<typeof createColorActivationSnapshot>
) {
  const colors1 = new Set(snapshot1.activeColors);
  const colors2 = new Set(snapshot2.activeColors);
  
  const added = snapshot2.activeColors.filter(color => !colors1.has(color));
  const removed = snapshot1.activeColors.filter(color => !colors2.has(color));
  const unchanged = snapshot1.activeColors.filter(color => colors2.has(color));
  
  return {
    added,
    removed,
    unchanged,
    hasChanges: added.length > 0 || removed.length > 0,
    timeDiff: snapshot2.timestamp - snapshot1.timestamp
  };
}

/**
 * 防抖颜色激活状态更新
 * @param callback 回调函数
 * @param delay 延迟时间（毫秒）
 * @returns 防抖后的函数
 */
export function debounceColorActivation<T extends (...args: unknown[]) => void>(
  callback: T,
  delay: number = 100
): T {
  let timeoutId: NodeJS.Timeout;
  
  return ((...args: Parameters<T>) => {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => callback(...args), delay);
  }) as T;
}

/**
 * 性能优化的颜色查找缓存
 * 为频繁查找的坐标提供缓存机制
 */
export class ColorLookupCache {
  private cache = new Map<string, BasicColorType | null>();
  private maxSize: number;
  
  constructor(maxSize: number = 1000) {
    this.maxSize = maxSize;
  }
  
  get(x: number, y: number): BasicColorType | null {
    const key = `${x},${y}`;
    
    if (this.cache.has(key)) {
      return this.cache.get(key)!;
    }
    
    const color = getCellColorByCoordinate(x, y);
    
    // 如果缓存已满，删除最旧的条目
    if (this.cache.size >= this.maxSize) {
      const firstKey = this.cache.keys().next().value;
      if (firstKey !== undefined) {
        this.cache.delete(firstKey);
      }
    }
    
    this.cache.set(key, color);
    return color;
  }
  
  clear(): void {
    this.cache.clear();
  }
  
  getStats() {
    return {
      size: this.cache.size,
      maxSize: this.maxSize,
      hitRate: this.cache.size > 0 ? 1 : 0 // 简化的命中率计算
    };
  }
}

// 导出默认的缓存实例
export const defaultColorCache = new ColorLookupCache();

/**
 * 使用缓存的颜色查找函数
 * @param x X坐标
 * @param y Y坐标
 * @returns 颜色类型，使用缓存优化性能
 */
export function getCachedCellColor(x: number, y: number): BasicColorType | null {
  return defaultColorCache.get(x, y);
}
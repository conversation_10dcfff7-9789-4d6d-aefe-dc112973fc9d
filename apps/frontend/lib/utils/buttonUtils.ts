/**
 * buttonUtils - 按钮相关工具函数
 * 🎯 核心价值：提供按钮样式、状态、交互的工具函数
 * 📦 功能范围：按钮样式计算、状态管理、事件处理
 * 🔄 架构设计：纯函数设计，支持主题和动态样式
 */

import { cn } from './cn';

// 按钮变体类型
export type ButtonVariant = 'default' | 'primary' | 'secondary' | 'outline' | 'ghost' | 'destructive';

// 按钮尺寸类型
export type ButtonSize = 'sm' | 'md' | 'lg' | 'xl';

// 按钮状态类型
export type ButtonState = 'normal' | 'hover' | 'active' | 'disabled' | 'loading';

// 按钮配置接口
export interface ButtonConfig {
  variant: ButtonVariant;
  size: ButtonSize;
  state: ButtonState;
  fullWidth?: boolean;
  rounded?: boolean;
  shadow?: boolean;
}

// 按钮样式映射
const buttonVariantStyles: Record<ButtonVariant, string> = {
  default: 'bg-gray-100 text-gray-900 hover:bg-gray-200 border border-gray-300',
  primary: 'bg-blue-600 text-white hover:bg-blue-700 border border-blue-600',
  secondary: 'bg-gray-600 text-white hover:bg-gray-700 border border-gray-600',
  outline: 'bg-transparent text-gray-900 hover:bg-gray-50 border border-gray-300',
  ghost: 'bg-transparent text-gray-900 hover:bg-gray-100 border border-transparent',
  destructive: 'bg-red-600 text-white hover:bg-red-700 border border-red-600',
};

const buttonSizeStyles: Record<ButtonSize, string> = {
  sm: 'px-2 py-1 text-xs',
  md: 'px-3 py-2 text-sm',
  lg: 'px-4 py-2 text-base',
  xl: 'px-6 py-3 text-lg',
};

const buttonStateStyles: Record<ButtonState, string> = {
  normal: '',
  hover: 'hover:scale-105',
  active: 'scale-95',
  disabled: 'opacity-50 cursor-not-allowed pointer-events-none',
  loading: 'opacity-75 cursor-wait',
};

/**
 * 生成按钮样式类名
 */
export function getButtonClassName(config: ButtonConfig): string {
  const {
    variant,
    size,
    state,
    fullWidth = false,
    rounded = false,
    shadow = false,
  } = config;

  return cn(
    // 基础样式
    'inline-flex items-center justify-center font-medium transition-all duration-200',
    'focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500',
    
    // 变体样式
    buttonVariantStyles[variant],
    
    // 尺寸样式
    buttonSizeStyles[size],
    
    // 状态样式
    buttonStateStyles[state],
    
    // 可选样式
    fullWidth && 'w-full',
    rounded && 'rounded-full',
    !rounded && 'rounded-md',
    shadow && 'shadow-sm hover:shadow-md',
  );
}

/**
 * 生成按钮图标样式类名
 */
export function getButtonIconClassName(size: ButtonSize, hasText: boolean = true): string {
  const iconSizes: Record<ButtonSize, string> = {
    sm: 'w-3 h-3',
    md: 'w-4 h-4',
    lg: 'w-5 h-5',
    xl: 'w-6 h-6',
  };

  return cn(
    iconSizes[size],
    hasText && 'mr-2'
  );
}

/**
 * 生成加载状态的按钮内容
 */
export function getLoadingButtonContent(
  originalContent: any,
  loadingText?: string
): unknown {
  // 返回加载状态的配置对象，而不是JSX
  return {
    type: 'loading',
    icon: {
      type: 'spinner',
      className: 'animate-spin -ml-1 mr-2 h-4 w-4',
    },
    text: loadingText || originalContent,
  };
}

/**
 * 按钮点击防抖处理
 */
export function debounceButtonClick<T extends any[]>(
  fn: (...args: T) => void,
  delay: number = 300
): (...args: T) => void {
  let timeoutId: NodeJS.Timeout;
  
  return (...args: T) => {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => fn(...args), delay);
  };
}

/**
 * 按钮点击节流处理
 */
export function throttleButtonClick<T extends any[]>(
  fn: (...args: T) => void,
  delay: number = 300
): (...args: T) => void {
  let lastCall = 0;
  
  return (...args: T) => {
    const now = Date.now();
    if (now - lastCall >= delay) {
      lastCall = now;
      fn(...args);
    }
  };
}

/**
 * 生成按钮组样式
 */
export function getButtonGroupClassName(
  orientation: 'horizontal' | 'vertical' = 'horizontal'
): string {
  return cn(
    'inline-flex',
    orientation === 'horizontal' ? 'flex-row' : 'flex-col',
    '[&>button:not(:first-child)]:ml-0',
    '[&>button:not(:last-child)]:rounded-r-none',
    '[&>button:not(:first-child)]:rounded-l-none',
    '[&>button:not(:first-child)]:border-l-0'
  );
}

/**
 * 验证按钮配置
 */
export function validateButtonConfig(config: Partial<ButtonConfig>): ButtonConfig {
  return {
    variant: config.variant || 'default',
    size: config.size || 'md',
    state: config.state || 'normal',
    fullWidth: config.fullWidth || false,
    rounded: config.rounded || false,
    shadow: config.shadow || false,
  };
}

/**
 * 获取按钮的可访问性属性
 */
export function getButtonA11yProps(
  config: ButtonConfig,
  label?: string,
  describedBy?: string
) {
  const props: Record<string, any> = {
    role: 'button',
    tabIndex: config.state === 'disabled' ? -1 : 0,
  };

  if (label) {
    props['aria-label'] = label;
  }

  if (describedBy) {
    props['aria-describedby'] = describedBy;
  }

  if (config.state === 'disabled') {
    props['aria-disabled'] = true;
  }

  if (config.state === 'loading') {
    props['aria-busy'] = true;
  }

  return props;
}

/**
 * 按钮主题适配器
 */
export function adaptButtonToTheme(
  config: ButtonConfig,
  theme: 'light' | 'dark'
): ButtonConfig {
  // 根据主题调整按钮配置
  if (theme === 'dark') {
    // 在深色主题下可能需要调整某些变体
    return config;
  }

  return config;
}

/**
 * 获取按钮样式
 */
export function getButtonStyle(variant: ButtonVariant = 'default', size: ButtonSize = 'md'): string {
  return getButtonClassName({ variant, size, state: 'normal' });
}

/**
 * 获取标签页样式
 */
export function getTabStyle(isActive: boolean = false): string {
  return cn(
    'px-4 py-2 text-sm font-medium rounded-md transition-colors',
    isActive
      ? 'bg-blue-100 text-blue-700 border-blue-300'
      : 'text-gray-500 hover:text-gray-700 hover:bg-gray-50'
  );
}

/**
 * 获取网格样式
 */
export function getGridStyle(): string {
  return 'grid gap-1 p-2';
}

/**
 * 获取高级按钮样式
 */
export function getAdvancedButtonStyle(): string {
  return getButtonClassName({
    variant: 'primary',
    size: 'lg',
    state: 'normal',
    shadow: true,
    rounded: true
  });
}

/**
 * 获取激活按钮样式
 */
export function getActiveButtonStyle(): string {
  return getButtonClassName({
    variant: 'primary',
    size: 'md',
    state: 'active'
  });
}

/**
 * 获取颜色按钮样式
 */
export function getColorButtonStyle(color: string = 'blue'): string {
  return cn(
    'px-3 py-2 rounded-md text-white font-medium',
    `bg-${color}-600 hover:bg-${color}-700`
  );
}

/**
 * 获取网格按钮样式
 */
export function getGridButtonStyle(): string {
  return getButtonClassName({
    variant: 'outline',
    size: 'sm',
    state: 'normal'
  });
}

/**
 * 获取模式按钮样式
 */
export function getModeButtonStyle(isSelected: boolean = false): string {
  return getButtonClassName({
    variant: isSelected ? 'primary' : 'ghost',
    size: 'md',
    state: 'normal'
  });
}

/**
 * 获取危险按钮样式
 */
export function getDangerButtonStyle(): string {
  return getButtonClassName({
    variant: 'destructive',
    size: 'md',
    state: 'normal'
  });
}

/**
 * 获取成功按钮样式
 */
export function getSuccessButtonStyle(): string {
  return cn(
    'px-4 py-2 rounded-md text-white font-medium',
    'bg-green-600 hover:bg-green-700 border border-green-600'
  );
}

/**
 * 按钮样式占位符
 */
export function getButtonStylePlaceholder(): string {
  return 'px-3 py-2 rounded-md border border-gray-300 bg-gray-50 text-gray-500';
}

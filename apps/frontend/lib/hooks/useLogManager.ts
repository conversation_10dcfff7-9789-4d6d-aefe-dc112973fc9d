/**
 * LogManager - 日志管理器
 * 🎯 核心价值：统一的日志记录、管理和分析
 * 📦 功能范围：日志记录、级别控制、格式化、存储、分析
 * 🔄 架构设计：单例模式，支持多种日志输出和存储方式
 */

// 日志级别枚举
export enum LogLevel {
  DEBUG = 0,
  INFO = 1,
  WARN = 2,
  ERROR = 3,
  FATAL = 4,
}

// 日志条目接口
export interface LogEntry {
  id: string;
  timestamp: number;
  level: LogLevel;
  message: string;
  data?: any;
  source: string;
  category?: string;
  tags?: string[];
  userId?: string;
  sessionId?: string;
  stack?: string;
}

// 日志配置接口
export interface LogConfig {
  level: LogLevel;
  enableConsole: boolean;
  enableStorage: boolean;
  enableRemote: boolean;
  maxEntries: number;
  categories: string[];
  remoteEndpoint?: string;
  formatters: LogFormatter[];
}

// 日志格式化器接口
export interface LogFormatter {
  name: string;
  format: (entry: LogEntry) => string;
}

// 日志输出器接口
export interface LogOutput {
  name: string;
  write: (entry: LogEntry, formatted: string) => void;
}

// 日志过滤器接口
export interface LogFilter {
  name: string;
  filter: (entry: LogEntry) => boolean;
}

/**
 * 日志管理器类
 */
export class LogManager {
  private static instance: LogManager;
  private config: LogConfig;
  private entries: LogEntry[] = [];
  private outputs: LogOutput[] = [];
  private filters: LogFilter[] = [];
  private nextId = 1;

  private constructor() {
    this.config = {
      level: LogLevel.INFO,
      enableConsole: true,
      enableStorage: false,
      enableRemote: false,
      maxEntries: 1000,
      categories: [],
      formatters: [this.createDefaultFormatter()],
    };

    // 添加默认输出器
    this.addOutput(this.createConsoleOutput());
  }

  /**
   * 获取单例实例
   */
  public static getInstance(): LogManager {
    if (!LogManager.instance) {
      LogManager.instance = new LogManager();
    }
    return LogManager.instance;
  }

  /**
   * 配置日志管理器
   */
  public configure(config: Partial<LogConfig>): void {
    this.config = { ...this.config, ...config };
  }

  /**
   * 记录调试日志
   */
  public debug(message: string, data?: any, source = 'app'): void {
    this.log(LogLevel.DEBUG, message, data, source);
  }

  /**
   * 记录信息日志
   */
  public info(message: string, data?: any, source = 'app'): void {
    this.log(LogLevel.INFO, message, data, source);
  }

  /**
   * 记录警告日志
   */
  public warn(message: string, data?: any, source = 'app'): void {
    this.log(LogLevel.WARN, message, data, source);
  }

  /**
   * 记录错误日志
   */
  public error(message: string, data?: any, source = 'app'): void {
    this.log(LogLevel.ERROR, message, data, source);
  }

  /**
   * 记录致命错误日志
   */
  public fatal(message: string, data?: any, source = 'app'): void {
    this.log(LogLevel.FATAL, message, data, source);
  }

  /**
   * 记录日志
   */
  public log(level: LogLevel, message: string, data?: any, source = 'app'): void {
    // 检查日志级别
    if (level < this.config.level) {
      return;
    }

    // 创建日志条目
    const entry: LogEntry = {
      id: `log_${this.nextId++}`,
      timestamp: Date.now(),
      level,
      message,
      data,
      source,
      stack: level >= LogLevel.ERROR ? new Error().stack : undefined,
    };

    // 应用过滤器
    if (!this.applyFilters(entry)) {
      return;
    }

    // 存储日志条目
    this.storeEntry(entry);

    // 输出日志
    this.outputEntry(entry);
  }

  /**
   * 添加日志输出器
   */
  public addOutput(output: LogOutput): void {
    this.outputs.push(output);
  }

  /**
   * 移除日志输出器
   */
  public removeOutput(name: string): void {
    this.outputs = this.outputs.filter(output => output.name !== name);
  }

  /**
   * 添加日志过滤器
   */
  public addFilter(filter: LogFilter): void {
    this.filters.push(filter);
  }

  /**
   * 移除日志过滤器
   */
  public removeFilter(name: string): void {
    this.filters = this.filters.filter(filter => filter.name !== name);
  }

  /**
   * 获取日志条目
   */
  public getEntries(filter?: Partial<LogEntry>): LogEntry[] {
    if (!filter) {
      return [...this.entries];
    }

    return this.entries.filter(entry => {
      return Object.entries(filter).every(([key, value]) => {
        return entry[key as keyof LogEntry] === value;
      });
    });
  }

  /**
   * 清空日志
   */
  public clear(): void {
    this.entries = [];
  }

  /**
   * 导出日志
   */
  public export(format: 'json' | 'csv' | 'txt' = 'json'): string {
    switch (format) {
      case 'json':
        return JSON.stringify(this.entries, null, 2);
      case 'csv':
        return this.exportToCsv();
      case 'txt':
        return this.exportToText();
      default:
        return JSON.stringify(this.entries, null, 2);
    }
  }

  /**
   * 应用过滤器
   */
  private applyFilters(entry: LogEntry): boolean {
    return this.filters.every(filter => filter.filter(entry));
  }

  /**
   * 存储日志条目
   */
  private storeEntry(entry: LogEntry): void {
    this.entries.push(entry);

    // 限制条目数量
    if (this.entries.length > this.config.maxEntries) {
      this.entries = this.entries.slice(-this.config.maxEntries);
    }
  }

  /**
   * 输出日志条目
   */
  private outputEntry(entry: LogEntry): void {
    const formatted = this.formatEntry(entry);
    this.outputs.forEach(output => {
      try {
        output.write(entry, formatted);
      } catch (error) {
        process.env.NODE_ENV === 'development' && console.error('日志输出错误:', error);
      }
    });
  }

  /**
   * 格式化日志条目
   */
  private formatEntry(entry: LogEntry): string {
    const formatter = this.config.formatters[0];
    return formatter ? formatter.format(entry) : this.defaultFormat(entry);
  }

  /**
   * 默认格式化
   */
  private defaultFormat(entry: LogEntry): string {
    const timestamp = new Date(entry.timestamp).toISOString();
    const level = LogLevel[entry.level];
    return `[${timestamp}] ${level} [${entry.source}] ${entry.message}`;
  }

  /**
   * 创建默认格式化器
   */
  private createDefaultFormatter(): LogFormatter {
    return {
      name: 'default',
      format: (entry) => this.defaultFormat(entry),
    };
  }

  /**
   * 创建控制台输出器
   */
  private createConsoleOutput(): LogOutput {
    return {
      name: 'console',
      write: (entry, formatted) => {
        if (!this.config.enableConsole) return;

        switch (entry.level) {
          case LogLevel.DEBUG:
            console.debug(formatted, entry.data);
            break;
          case LogLevel.INFO:
            process.env.NODE_ENV === 'development' && console.info(formatted, entry.data);
            break;
          case LogLevel.WARN:
            process.env.NODE_ENV === 'development' && console.warn(formatted, entry.data);
            break;
          case LogLevel.ERROR:
          case LogLevel.FATAL:
            process.env.NODE_ENV === 'development' && console.error(formatted, entry.data);
            break;
        }
      },
    };
  }

  /**
   * 导出为CSV格式
   */
  private exportToCsv(): string {
    const headers = ['timestamp', 'level', 'source', 'message', 'data'];
    const rows = this.entries.map(entry => [
      new Date(entry.timestamp).toISOString(),
      LogLevel[entry.level],
      entry.source,
      entry.message,
      entry.data ? JSON.stringify(entry.data) : '',
    ]);

    return [headers, ...rows]
      .map(row => row.map(cell => `"${cell}"`).join(','))
      .join('\n');
  }

  /**
   * 导出为文本格式
   */
  private exportToText(): string {
    return this.entries
      .map(entry => this.defaultFormat(entry))
      .join('\n');
  }
}

// 导出单例实例
export const logger = LogManager.getInstance();

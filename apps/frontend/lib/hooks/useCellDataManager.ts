/**
 * CellDataManager - 单元格数据管理器
 * 🎯 核心价值：统一的单元格数据CRUD操作和事件管理
 * 📦 功能范围：数据存储、查询、更新、事件通知
 * 🔄 架构设计：单例模式，确保全局状态一致性
 */

import type { CellData } from '@/lib/types/grid';

// 数据变更事件类型
export interface DataChangeEvent {
  type: 'create' | 'update' | 'delete' | 'clear';
  cellIds: string[];
  timestamp: number;
  data?: CellData[];
}

import type { GroupType as MatrixGroupType } from '@/lib/types/matrix';

// 查询条件类型
export interface QueryConditions {
  color?: string;
  level?: number;
  group?: MatrixGroupType | null;
  x?: number;
  y?: number;
  row?: number;
  col?: number;
}

// 颜色类型
export type ColorType = 'red' | 'cyan' | 'yellow' | 'purple' | 'orange' | 'green' | 'blue' | 'pink' | 'black';

// 级别类型
export type LevelType = 1 | 2 | 3 | 4;

// 分组类型 - 使用字母组
export type GroupType = MatrixGroupType;

// 统计信息类型
export interface CellDataStats {
  totalCells: number;
  colorCounts: Record<string, number>;
  levelCounts: Record<number, number>;
  groupCounts: Record<string, number>; // 改为string以支持字母组
}

/**
 * 单元格数据管理器类
 */
export class CellDataManager {
  private static instance: CellDataManager;
  private cells: Map<string, CellData> = new Map();
  private changeListeners: ((event: DataChangeEvent) => void)[] = [];
  private nextId = 1;

  private constructor() {
    // 私有构造函数，确保单例
  }

  /**
   * 获取单例实例
   */
  public static getInstance(): CellDataManager {
    if (!CellDataManager.instance) {
      CellDataManager.instance = new CellDataManager();
    }
    return CellDataManager.instance;
  }

  /**
   * 创建新单元格
   */
  public createCell(cellData: Omit<CellData, 'id'>): CellData {
    const id = `cell_${this.nextId++}`;
    const newCell: CellData = {
      ...cellData,
      id,
    };
    
    this.cells.set(id, newCell);
    this.notifyChange({
      type: 'create',
      cellIds: [id],
      timestamp: Date.now(),
      data: [newCell],
    });
    
    return newCell;
  }

  /**
   * 根据ID获取单元格
   */
  public getCellById(id: string): CellData | undefined {
    return this.cells.get(id);
  }

  /**
   * 根据位置获取单元格
   */
  public getCellByPosition(row: number, col: number): CellData | undefined {
    for (const cell of this.cells.values()) {
      if (cell.row === row && cell.col === col) {
        return cell;
      }
    }
    return undefined;
  }

  /**
   * 根据索引获取单元格
   */
  public getCellByIndex(index: number): CellData | undefined {
    for (const cell of this.cells.values()) {
      if (cell.index === index) {
        return cell;
      }
    }
    return undefined;
  }

  /**
   * 更新单元格
   */
  public updateCell(id: string, updates: Partial<CellData>): boolean {
    const cell = this.cells.get(id);
    if (!cell) return false;

    const updatedCell = { ...cell, ...updates, id }; // 确保ID不被覆盖
    this.cells.set(id, updatedCell);
    
    this.notifyChange({
      type: 'update',
      cellIds: [id],
      timestamp: Date.now(),
      data: [updatedCell],
    });
    
    return true;
  }

  /**
   * 删除单元格
   */
  public deleteCell(id: string): boolean {
    const deleted = this.cells.delete(id);
    if (deleted) {
      this.notifyChange({
        type: 'delete',
        cellIds: [id],
        timestamp: Date.now(),
      });
    }
    return deleted;
  }

  /**
   * 获取所有单元格
   */
  public getAllCells(): CellData[] {
    return Array.from(this.cells.values());
  }

  /**
   * 根据颜色查询单元格
   */
  public getCellsByColor(color: string): CellData[] {
    return this.getAllCells().filter(cell => cell.color === color);
  }

  /**
   * 根据级别查询单元格
   */
  public getCellsByLevel(level: LevelType): CellData[] {
    return this.getAllCells().filter(cell => cell.level === level);
  }

  /**
   * 根据分组查询单元格
   */
  public getCellsByGroup(group: GroupType | null): CellData[] {
    return this.getAllCells().filter(cell => cell.group === group);
  }

  /**
   * 根据条件查询单元格
   */
  public queryCells(conditions: QueryConditions): CellData[] {
    return this.getAllCells().filter(cell => {
      if (conditions.color && cell.color !== conditions.color) return false;
      if (conditions.level && cell.level !== conditions.level) return false;
      if (conditions.group && cell.group !== conditions.group) return false;
      if (conditions.x && cell.x !== conditions.x) return false;
      if (conditions.y && cell.y !== conditions.y) return false;
      if (conditions.row && cell.row !== conditions.row) return false;
      if (conditions.col && cell.col !== conditions.col) return false;
      return true;
    });
  }

  /**
   * 清空所有数据
   */
  public clear(): void {
    const cellIds = Array.from(this.cells.keys());
    this.cells.clear();
    this.notifyChange({
      type: 'clear',
      cellIds,
      timestamp: Date.now(),
    });
  }

  /**
   * 获取统计信息
   */
  public getStats(): CellDataStats {
    const cells = this.getAllCells();
    const colorCounts: Record<string, number> = {};
    const levelCounts: Record<number, number> = {};
    const groupCounts: Record<string, number> = {}; // 改为string类型以支持字母组

    cells.forEach(cell => {
      // 颜色统计
      colorCounts[cell.color] = (colorCounts[cell.color] || 0) + 1;

      // 级别统计
      levelCounts[cell.level] = (levelCounts[cell.level] || 0) + 1;

      // 分组统计
      if (cell.group !== null) {
        groupCounts[cell.group] = (groupCounts[cell.group] || 0) + 1;
      }
    });

    return {
      totalCells: cells.length,
      colorCounts,
      levelCounts,
      groupCounts,
    };
  }

  /**
   * 添加变更监听器
   */
  public addChangeListener(listener: (event: DataChangeEvent) => void): void {
    this.changeListeners.push(listener);
  }

  /**
   * 移除变更监听器
   */
  public removeChangeListener(listener: (event: DataChangeEvent) => void): void {
    const index = this.changeListeners.indexOf(listener);
    if (index > -1) {
      this.changeListeners.splice(index, 1);
    }
  }

  /**
   * 通知变更事件
   */
  private notifyChange(event: DataChangeEvent): void {
    this.changeListeners.forEach(listener => {
      try {
        listener(event);
      } catch (error) {
        process.env.NODE_ENV === 'development' && console.error('Error in change listener:', error);
      }
    });
  }
}

/**
 * 设备性能检测器
 * 🎯 核心价值：自动检测设备性能并调整渲染配置，确保最佳用户体验
 * 📦 功能范围：CPU性能检测、内存检测、GPU检测、网络检测、配置自动调整
 * ⚡ 性能优化：根据设备能力自适应调整，避免性能瓶颈
 */

export type PerformanceLevel = 'high' | 'medium' | 'low';

export interface DeviceCapabilities {
  cpu: {
    level: PerformanceLevel;
    benchmarkScore: number;
    coreCount: number;
  };
  memory: {
    total: number; // MB
    available: number; // MB
    level: PerformanceLevel;
  };
  gpu: {
    vendor: string;
    renderer: string;
    level: PerformanceLevel;
    webglSupported: boolean;
    webgl2Supported: boolean;
  };
  network: {
    type: string;
    downlink: number; // Mbps
    rtt: number; // ms
    level: PerformanceLevel;
  };
  display: {
    width: number;
    height: number;
    pixelRatio: number;
    colorDepth: number;
  };
}

export interface PerformanceConfig {
  batchSize: number;
  debounceDelay: number;
  enableGPUAcceleration: boolean;
  enableVirtualization: boolean;
  cacheMaxSize: number;
  performanceMode: PerformanceLevel;
  enableAnimations: boolean;
  maxConcurrentTasks: number;
}

export interface BenchmarkResult {
  cpuScore: number;
  memoryScore: number;
  renderScore: number;
  overallScore: number;
  duration: number;
}

/**
 * 设备性能检测器类
 */
export class DevicePerformanceDetector {
  private capabilities: DeviceCapabilities | null = null;
  private benchmarkCache = new Map<string, BenchmarkResult>();
  private monitoringInterval: number | null = null;
  private performanceObserver: PerformanceObserver | null = null;

  constructor() {
    this.initializePerformanceObserver();
  }

  /**
   * 检测设备性能
   */
  async detectDeviceCapabilities(): Promise<DeviceCapabilities> {
    if (this.capabilities) {
      return this.capabilities;
    }

    const [cpu, memory, gpu, network, display] = await Promise.all([
      this.detectCPUPerformance(),
      this.detectMemoryCapabilities(),
      this.detectGPUCapabilities(),
      this.detectNetworkCapabilities(),
      this.detectDisplayCapabilities()
    ]);

    this.capabilities = {
      cpu,
      memory,
      gpu,
      network,
      display
    };

    return this.capabilities;
  }

  /**
   * CPU性能检测
   */
  private async detectCPUPerformance(): Promise<DeviceCapabilities['cpu']> {
    const cacheKey = 'cpu-benchmark';
    const cached = this.benchmarkCache.get(cacheKey);
    
    if (cached && Date.now() - cached.duration < 5 * 60 * 1000) { // 5分钟缓存
      return {
        level: this.scoreToPerfLevel(cached.cpuScore),
        benchmarkScore: cached.cpuScore,
        coreCount: navigator.hardwareConcurrency || 4
      };
    }

    const startTime = performance.now();
    
    // CPU密集型基准测试
    let result = 0;
    const iterations = 1000000;
    
    for (let i = 0; i < iterations; i++) {
      result += Math.sqrt(i) * Math.sin(i) * Math.cos(i);
    }
    
    const duration = performance.now() - startTime;
    const score = Math.max(0, 1000 - duration); // 分数越高性能越好
    
    this.benchmarkCache.set(cacheKey, {
      cpuScore: score,
      memoryScore: 0,
      renderScore: 0,
      overallScore: score,
      duration: Date.now()
    });

    return {
      level: this.scoreToPerfLevel(score),
      benchmarkScore: score,
      coreCount: navigator.hardwareConcurrency || 4
    };
  }

  /**
   * 内存能力检测
   */
  private async detectMemoryCapabilities(): Promise<DeviceCapabilities['memory']> {
    // @ts-ignore - 实验性API
    const memoryInfo = (navigator as any).deviceMemory || 
                      (performance as any).memory?.jsHeapSizeLimit / (1024 * 1024 * 1024) || 
                      4; // 默认4GB

    const totalMemory = memoryInfo * 1024; // 转换为MB
    
    // 估算可用内存（通常是总内存的70%）
    const availableMemory = totalMemory * 0.7;

    let level: PerformanceLevel = 'medium';
    if (totalMemory >= 8192) level = 'high';   // 8GB+
    else if (totalMemory >= 4096) level = 'medium'; // 4GB+
    else level = 'low';                        // <4GB

    return {
      total: totalMemory,
      available: availableMemory,
      level
    };
  }

  /**
   * GPU能力检测
   */
  private async detectGPUCapabilities(): Promise<DeviceCapabilities['gpu']> {
    const canvas = document.createElement('canvas');
    const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
    const gl2 = canvas.getContext('webgl2');

    let vendor = 'unknown';
    let renderer = 'unknown';
    let level: PerformanceLevel = 'low';

    if (gl) {
      const debugInfo = gl.getExtension('WEBGL_debug_renderer_info');
      if (debugInfo) {
        vendor = gl.getParameter(debugInfo.UNMASKED_VENDOR_WEBGL) || 'unknown';
        renderer = gl.getParameter(debugInfo.UNMASKED_RENDERER_WEBGL) || 'unknown';
      }

      // 基于GPU信息判断性能等级
      const rendererLower = renderer.toLowerCase();
      if (rendererLower.includes('nvidia') || rendererLower.includes('amd') || rendererLower.includes('radeon')) {
        level = 'high';
      } else if (rendererLower.includes('intel') && !rendererLower.includes('hd 3000')) {
        level = 'medium';
      }
    }

    return {
      vendor,
      renderer,
      level,
      webglSupported: !!gl,
      webgl2Supported: !!gl2
    };
  }

  /**
   * 网络能力检测
   */
  private async detectNetworkCapabilities(): Promise<DeviceCapabilities['network']> {
    // @ts-ignore - 实验性API
    const connection = (navigator as any).connection || 
                      (navigator as any).mozConnection || 
                      (navigator as any).webkitConnection;

    let type = 'unknown';
    let downlink = 10; // 默认10Mbps
    let rtt = 100; // 默认100ms
    let level: PerformanceLevel = 'medium';

    if (connection) {
      type = connection.effectiveType || connection.type || 'unknown';
      downlink = connection.downlink || 10;
      rtt = connection.rtt || 100;

      // 基于网络类型判断性能等级
      if (type === '4g' || downlink >= 10) {
        level = 'high';
      } else if (type === '3g' || downlink >= 1) {
        level = 'medium';
      } else {
        level = 'low';
      }
    }

    return {
      type,
      downlink,
      rtt,
      level
    };
  }

  /**
   * 显示能力检测
   */
  private async detectDisplayCapabilities(): Promise<DeviceCapabilities['display']> {
    return {
      width: window.screen.width,
      height: window.screen.height,
      pixelRatio: window.devicePixelRatio || 1,
      colorDepth: window.screen.colorDepth || 24
    };
  }

  /**
   * 自动调整性能配置
   */
  async autoAdjustConfig(baseConfig: PerformanceConfig): Promise<PerformanceConfig> {
    const capabilities = await this.detectDeviceCapabilities();
    
    // 计算综合性能等级
    const overallLevel = this.calculateOverallPerformanceLevel(capabilities);
    
    const adjustedConfig: PerformanceConfig = { ...baseConfig };

    switch (overallLevel) {
      case 'high':
        adjustedConfig.batchSize = Math.max(baseConfig.batchSize * 2, 100);
        adjustedConfig.debounceDelay = Math.max(baseConfig.debounceDelay / 2, 8);
        adjustedConfig.enableGPUAcceleration = true;
        adjustedConfig.enableVirtualization = false; // 高性能设备不需要虚拟化
        adjustedConfig.cacheMaxSize = baseConfig.cacheMaxSize * 2;
        adjustedConfig.performanceMode = 'high';
        adjustedConfig.enableAnimations = true;
        adjustedConfig.maxConcurrentTasks = 8;
        break;

      case 'medium':
        adjustedConfig.batchSize = baseConfig.batchSize;
        adjustedConfig.debounceDelay = baseConfig.debounceDelay;
        adjustedConfig.enableGPUAcceleration = capabilities.gpu.webglSupported;
        adjustedConfig.enableVirtualization = false;
        adjustedConfig.cacheMaxSize = baseConfig.cacheMaxSize;
        adjustedConfig.performanceMode = 'medium';
        adjustedConfig.enableAnimations = true;
        adjustedConfig.maxConcurrentTasks = 4;
        break;

      case 'low':
        adjustedConfig.batchSize = Math.max(baseConfig.batchSize / 2, 25);
        adjustedConfig.debounceDelay = baseConfig.debounceDelay * 2;
        adjustedConfig.enableGPUAcceleration = false;
        adjustedConfig.enableVirtualization = true; // 低性能设备启用虚拟化
        adjustedConfig.cacheMaxSize = Math.max(baseConfig.cacheMaxSize / 2, 100);
        adjustedConfig.performanceMode = 'low';
        adjustedConfig.enableAnimations = false;
        adjustedConfig.maxConcurrentTasks = 2;
        break;
    }

    return adjustedConfig;
  }

  /**
   * 计算综合性能等级
   */
  private calculateOverallPerformanceLevel(capabilities: DeviceCapabilities): PerformanceLevel {
    const scores = {
      high: 0,
      medium: 0,
      low: 0
    };

    // CPU权重：40%
    scores[capabilities.cpu.level] += 0.4;
    
    // 内存权重：30%
    scores[capabilities.memory.level] += 0.3;
    
    // GPU权重：20%
    scores[capabilities.gpu.level] += 0.2;
    
    // 网络权重：10%
    scores[capabilities.network.level] += 0.1;

    // 返回得分最高的等级
    const maxScore = Math.max(scores.high, scores.medium, scores.low);
    if (scores.high === maxScore) return 'high';
    if (scores.medium === maxScore) return 'medium';
    return 'low';
  }

  /**
   * 分数转性能等级
   */
  private scoreToPerfLevel(score: number): PerformanceLevel {
    if (score >= 800) return 'high';
    if (score >= 400) return 'medium';
    return 'low';
  }

  /**
   * 开始性能监控
   */
  startPerformanceMonitoring(callback: (metrics: any) => void): void {
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
    }

    this.monitoringInterval = window.setInterval(() => {
      const metrics = this.collectRealTimeMetrics();
      callback(metrics);
    }, 5000); // 每5秒收集一次
  }

  /**
   * 停止性能监控
   */
  stopPerformanceMonitoring(): void {
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = null;
    }
  }

  /**
   * 收集实时性能指标
   */
  private collectRealTimeMetrics() {
    const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
    const memory = (performance as any).memory;

    return {
      // 页面加载性能
      loadTime: navigation?.loadEventEnd - navigation?.loadEventStart || 0,
      domContentLoaded: navigation?.domContentLoadedEventEnd - navigation?.domContentLoadedEventStart || 0,
      
      // 内存使用
      memoryUsed: memory?.usedJSHeapSize || 0,
      memoryTotal: memory?.totalJSHeapSize || 0,
      memoryLimit: memory?.jsHeapSizeLimit || 0,
      
      // FPS估算
      fps: this.estimateFPS(),
      
      // 时间戳
      timestamp: Date.now()
    };
  }

  /**
   * 估算FPS
   */
  private estimateFPS(): number {
    // 简单的FPS估算，实际项目中可以使用更精确的方法
    return 60; // 默认返回60，实际应该通过requestAnimationFrame测量
  }

  /**
   * 初始化性能观察器
   */
  private initializePerformanceObserver(): void {
    if ('PerformanceObserver' in window) {
      this.performanceObserver = new PerformanceObserver((list) => {
        // 处理性能条目
        const entries = list.getEntries();
        entries.forEach(entry => {
          if (entry.entryType === 'measure' || entry.entryType === 'mark') {
            // 可以在这里处理自定义的性能标记
          }
        });
      });

      this.performanceObserver.observe({ 
        entryTypes: ['measure', 'mark', 'navigation', 'resource'] 
      });
    }
  }

  /**
   * 获取设备能力
   */
  getCapabilities(): DeviceCapabilities | null {
    return this.capabilities;
  }

  /**
   * 销毁检测器
   */
  destroy(): void {
    this.stopPerformanceMonitoring();
    
    if (this.performanceObserver) {
      this.performanceObserver.disconnect();
      this.performanceObserver = null;
    }
    
    this.benchmarkCache.clear();
    this.capabilities = null;
  }
}

// 创建全局性能检测器实例
export const globalPerformanceDetector = new DevicePerformanceDetector();

// 性能检测器工厂函数
export function createPerformanceDetector(): DevicePerformanceDetector {
  return new DevicePerformanceDetector();
}

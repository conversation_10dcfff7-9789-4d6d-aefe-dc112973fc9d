/**
 * TanStack Query Provider
 * 🎯 核心价值：提供查询客户端上下文
 * 📦 功能：查询客户端提供者、开发工具集成
 * ⚡ 开发体验：开发环境下的调试工具
 */

'use client';

import React from 'react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';
import { createQueryClient } from './config';

// === Provider Props ===
interface QueryProviderProps {
  children: React.ReactNode;
  client?: QueryClient;
}

// === 全局查询客户端实例 ===
let globalQueryClient: QueryClient | undefined = undefined;

// === 获取查询客户端 ===
function getQueryClient() {
  if (typeof window === 'undefined') {
    // 服务端：每次创建新实例
    return createQueryClient();
  } else {
    // 客户端：复用全局实例
    if (!globalQueryClient) {
      globalQueryClient = createQueryClient();
    }
    return globalQueryClient;
  }
}

// === Query Provider 组件 ===
export function QueryProvider({ children, client }: QueryProviderProps) {
  // 使用传入的客户端或创建默认客户端
  const queryClient = client || getQueryClient();

  return (
    <QueryClientProvider client={queryClient}>
      {children}
      {/* 开发环境下显示调试工具 */}
      {process.env.NODE_ENV === 'development' && (
        <ReactQueryDevtools
          initialIsOpen={false}
          buttonPosition="bottom-right"
        />
      )}
    </QueryClientProvider>
  );
}

// === 导出查询客户端获取函数 ===
export { getQueryClient };

// === 默认导出 ===
export default QueryProvider;

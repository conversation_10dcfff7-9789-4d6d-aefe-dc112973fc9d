/**
 * 数据相关查询Hooks
 * 🎯 核心价值：颜色数据和网格数据的查询和变更操作
 * 📦 功能：颜色数据、网格数据的获取和更新
 * ⚡ 类型安全：完整的TypeScript支持
 */

import {
  useQuery,
  useMutation,
  useQueryClient,
  type UseQueryOptions,
  type UseMutationOptions,
} from '@tanstack/react-query';
import { queryKeys, queryOptions } from '../config';
import { colorDataApi, gridDataApi } from '@/lib/api/client';
import type {
  ColorDataResponse,
  GridDataResponse,
  ColorDataRequest,
  GridDataRequest,
} from '@/lib/types/api';

// === 颜色数据查询 ===
export function useColorData(
  projectId: string,
  options?: Omit<UseQueryOptions<ColorDataResponse>, 'queryKey' | 'queryFn'>
) {
  return useQuery({
    queryKey: queryKeys.data.colors(projectId),
    queryFn: () => colorDataApi.getColorData(projectId),
    enabled: !!projectId,
    ...queryOptions.user,
    ...options,
  });
}

// === 网格数据查询 ===
export function useGridData(
  projectId: string,
  options?: Omit<UseQueryOptions<GridDataResponse>, 'queryKey' | 'queryFn'>
) {
  return useQuery({
    queryKey: queryKeys.data.grid(projectId),
    queryFn: () => gridDataApi.getGridData(projectId),
    enabled: !!projectId,
    ...queryOptions.user,
    ...options,
  });
}

// === 更新颜色数据变更 ===
export function useUpdateColorData(
  options?: UseMutationOptions<ColorDataResponse, Error, { projectId: string; data: ColorDataRequest }>
) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ projectId, data }) => colorDataApi.updateColorData(projectId, data),
    onSuccess: (updatedData, { projectId }) => {
      // 更新颜色数据缓存
      queryClient.setQueryData(
        queryKeys.data.colors(projectId),
        updatedData
      );
    },
    onError: (error, { projectId }) => {
      // 错误时重新获取数据
      queryClient.invalidateQueries({
        queryKey: queryKeys.data.colors(projectId),
      });
    },
    ...options,
  });
}

// === 更新网格数据变更 ===
export function useUpdateGridData(
  options?: UseMutationOptions<GridDataResponse, Error, { projectId: string; data: GridDataRequest }>
) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ projectId, data }) => gridDataApi.updateGridData(projectId, data),
    onSuccess: (updatedData, { projectId }) => {
      // 更新网格数据缓存
      queryClient.setQueryData(
        queryKeys.data.grid(projectId),
        updatedData
      );
    },
    onError: (error, { projectId }) => {
      // 错误时重新获取数据
      queryClient.invalidateQueries({
        queryKey: queryKeys.data.grid(projectId),
      });
    },
    ...options,
  });
}

// === 批量数据操作 ===

// 预加载项目数据
export function usePrefetchProjectData() {
  const queryClient = useQueryClient();

  return (projectId: string) => {
    // 预加载颜色数据
    queryClient.prefetchQuery({
      queryKey: queryKeys.data.colors(projectId),
      queryFn: () => colorDataApi.getColorData(projectId),
      ...queryOptions.user,
    });

    // 预加载网格数据
    queryClient.prefetchQuery({
      queryKey: queryKeys.data.grid(projectId),
      queryFn: () => gridDataApi.getGridData(projectId),
      ...queryOptions.user,
    });
  };
}

// 获取项目所有数据（组合hook）
export function useProjectData(projectId: string) {
  const colorDataQuery = useColorData(projectId);
  const gridDataQuery = useGridData(projectId);

  return {
    colorData: colorDataQuery.data,
    gridData: gridDataQuery.data,
    isLoading: colorDataQuery.isLoading || gridDataQuery.isLoading,
    isError: colorDataQuery.isError || gridDataQuery.isError,
    error: colorDataQuery.error || gridDataQuery.error,
    refetch: () => {
      colorDataQuery.refetch();
      gridDataQuery.refetch();
    },
  };
}

// 清除项目数据缓存
export function useClearProjectDataCache() {
  const queryClient = useQueryClient();

  return (projectId: string) => {
    queryClient.removeQueries({
      queryKey: queryKeys.data.colors(projectId),
    });
    queryClient.removeQueries({
      queryKey: queryKeys.data.grid(projectId),
    });
  };
}

// 获取数据缓存状态
export function useDataCacheStatus(projectId: string) {
  const queryClient = useQueryClient();

  const colorDataCache = queryClient.getQueryState(queryKeys.data.colors(projectId));
  const gridDataCache = queryClient.getQueryState(queryKeys.data.grid(projectId));

  return {
    colorData: {
      isCached: !!colorDataCache?.data,
      isStale: colorDataCache?.isInvalidated || false,
      lastUpdated: colorDataCache?.dataUpdatedAt,
    },
    gridData: {
      isCached: !!gridDataCache?.data,
      isStale: gridDataCache?.isInvalidated || false,
      lastUpdated: gridDataCache?.dataUpdatedAt,
    },
  };
}

// 乐观更新辅助函数
export function useOptimisticColorUpdate() {
  const queryClient = useQueryClient();

  return (projectId: string, updater: (old: ColorDataResponse | undefined) => ColorDataResponse) => {
    queryClient.setQueryData(
      queryKeys.data.colors(projectId),
      updater
    );
  };
}

export function useOptimisticGridUpdate() {
  const queryClient = useQueryClient();

  return (projectId: string, updater: (old: GridDataResponse | undefined) => GridDataResponse) => {
    queryClient.setQueryData(
      queryKeys.data.grid(projectId),
      updater
    );
  };
}

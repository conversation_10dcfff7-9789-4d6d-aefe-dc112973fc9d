/**
 * TanStack Query 兼容层 - useBusinessData
 * 🎯 核心价值：保持API兼容性，同时使用TanStack Query
 * 📦 功能：与原useBusinessData相同的API，但使用TanStack Query实现
 * ⚡ 类型安全：完整的TypeScript支持
 */

import { useCallback, useMemo, useRef } from 'react';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import type {
  BusinessDataType,
  QueryConditions,
  BusinessStats,
  BusinessDataConfig,
  UseBusinessDataReturn,
  BusinessDataItem,
} from '@/features/shared/hooks/useBusinessData';
import { isBusinessDataItem } from '@/features/shared/hooks/useBusinessData';

// 缓存条目类型
interface CacheEntry<T> {
  data: T;
  timestamp: number;
}

// 生成查询键
function getBusinessDataQueryKey(dataType: BusinessDataType) {
  return ['businessData', dataType];
}

// 计算统计信息
function calculateStats(data: unknown): BusinessStats {
  if (!data || !Array.isArray(data)) {
    return {
      total: 0,
      active: 0,
      visible: 0,
      byType: {},
      byLevel: {},
      lastUpdate: Date.now(),
    };
  }

  const stats: BusinessStats = {
    total: data.length,
    active: 0,
    visible: 0,
    byType: {},
    byLevel: {},
    lastUpdate: Date.now(),
  };

  // 计算统计信息
  data.forEach((item: unknown) => {
    if (!isBusinessDataItem(item)) return;

    // 活跃项
    if (item.active) {
      stats.active++;
    }

    // 可见项
    if (item.visible !== false) {
      stats.visible++;
    }

    // 按类型统计
    if (item.type) {
      stats.byType[item.type] = (stats.byType[item.type] || 0) + 1;
    }

    // 按级别统计
    if (item.level !== undefined) {
      stats.byLevel[item.level] = (stats.byLevel[item.level] || 0) + 1;
    }
  });

  return stats;
}

/**
 * 使用TanStack Query实现的useBusinessData
 * 保持与原API完全兼容
 */
export function useBusinessData<T>(
  config: BusinessDataConfig<T>
): UseBusinessDataReturn<T> {
  const {
    dataType,
    fetcher,
    validator,
    transformer,
    enableCache = true,
    cacheTimeout = 5 * 60 * 1000, // 5分钟
    enableAutoRefresh = false,
    refreshInterval = 30000,
    fetchOnMount = true,
  } = config;

  // 查询客户端
  const queryClient = useQueryClient();
  
  // 缓存引用
  const cacheRef = useRef<Map<string, CacheEntry<T>>>(new Map());
  
  // 查询键
  const queryKey = getBusinessDataQueryKey(dataType);
  
  // 使用TanStack Query
  const {
    data,
    isLoading,
    isRefetching,
    error,
    refetch,
    dataUpdatedAt,
  } = useQuery({
    queryKey,
    queryFn: fetcher,
    staleTime: cacheTimeout,
    refetchInterval: enableAutoRefresh ? refreshInterval : undefined,
    enabled: fetchOnMount,
    retry: 1,
  });

  // 应用转换器
  const transformedData = useMemo(() => {
    if (!data) return null;
    return transformer ? transformer(data) : data;
  }, [data, transformer]);

  // 计算统计信息
  const stats = useMemo(() => {
    if (!transformedData) return null;
    return calculateStats(transformedData);
  }, [transformedData]);

  // 查询功能
  const query = useCallback((conditions: QueryConditions) => {
    if (!transformedData || !Array.isArray(transformedData)) return [];

    return transformedData.filter((item: unknown) => {
      if (!isBusinessDataItem(item)) return false;

      return Object.entries(conditions).every(([key, value]) => {
        if (value === undefined) return true;
        return item[key] === value;
      });
    });
  }, [transformedData]);

  const filter = useCallback((predicate: (item: unknown) => boolean) => {
    if (!transformedData || !Array.isArray(transformedData)) return [];
    return transformedData.filter(predicate);
  }, [transformedData]);

  const search = useCallback((searchTerm: string, fields: string[]) => {
    if (!transformedData || !Array.isArray(transformedData)) return [];
    if (!searchTerm.trim()) return transformedData;

    const term = searchTerm.toLowerCase();
    return transformedData.filter((item: unknown) => {
      if (!isBusinessDataItem(item)) return false;

      return fields.some(field => {
        const value = item[field];
        return value && value.toString().toLowerCase().includes(term);
      });
    });
  }, [transformedData]);

  // 统计功能
  const getStats = useCallback((): BusinessStats => {
    if (stats) return stats;

    if (!transformedData) {
      return {
        total: 0,
        active: 0,
        visible: 0,
        byType: {},
        byLevel: {},
        lastUpdate: Date.now(),
      };
    }

    return calculateStats(transformedData);
  }, [stats, transformedData]);

  const getCount = useCallback((conditions?: QueryConditions) => {
    if (!conditions) {
      return transformedData ? (Array.isArray(transformedData) ? transformedData.length : 1) : 0;
    }
    return query(conditions).length;
  }, [transformedData, query]);

  // 验证功能
  const validate = useCallback((): boolean => {
    if (!transformedData || !validator) return true;
    const validationError = validator(transformedData);
    return !validationError;
  }, [transformedData, validator]);

  // 缓存控制
  const clearCache = useCallback(() => {
    queryClient.invalidateQueries({ queryKey });
  }, [queryClient, queryKey]);

  const getCacheInfo = useCallback(() => {
    return {
      size: transformedData ? JSON.stringify(transformedData).length : 0,
      lastUpdate: dataUpdatedAt || 0,
    };
  }, [transformedData, dataUpdatedAt]);

  // 操作函数
  const fetch = useCallback(async () => {
    if (isLoading) return;
    await refetch();
  }, [isLoading, refetch]);

  const refresh = useCallback(async () => {
    if (isRefetching) return;
    await refetch();
  }, [isRefetching, refetch]);

  const reset = useCallback(() => {
    queryClient.resetQueries({ queryKey });
  }, [queryClient, queryKey]);

  // 返回与原API兼容的结果
  return {
    // 状态
    data: transformedData,
    isLoading,
    error: error ? (error as Error).message : null,
    isRefreshing: isRefetching,
    lastFetchTime: dataUpdatedAt || null,
    stats,

    // 操作
    fetch,
    refresh,
    reset,

    // 查询功能
    query,
    filter,
    search,

    // 统计功能
    getStats,
    getCount,

    // 验证功能
    validate,

    // 缓存控制
    clearCache,
    getCacheInfo,
  };
}

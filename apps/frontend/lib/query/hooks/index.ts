/**
 * TanStack Query Hooks 统一导出
 * 🎯 核心价值：统一查询hooks导出
 * 📦 功能：项目、数据、系统hooks统一导出
 * ⚡ 便利性：一站式导入
 */

// === 项目相关hooks ===
export {
  useProjects,
  useProject,
  useProjectSettings,
  useCreateProject,
  useUpdateProject,
  useDeleteProject,
  usePrefetchProject,
  useProjectCache,
  useSetProjectCache,
} from './useProjects';

// === 数据相关hooks ===
export {
  useColorData,
  useGridData,
  useUpdateColorData,
  useUpdateGridData,
  usePrefetchProjectData,
  useProjectData,
  useClearProjectDataCache,
  useDataCacheStatus,
  useOptimisticColorUpdate,
  useOptimisticGridUpdate,
} from './useData';

// === 系统相关hooks ===
export {
  useHealthCheck,
  useSystemStatus,
  useSystemMonitoring,
  useNetworkStatus,
  useSystemDiagnostics,
} from './useSystem';

// === 兼容层hooks ===
export {
  useBusinessData,
} from './useBusinessData';

// === 通用查询hooks（从TanStack Query重新导出） ===
export {
  useQuery,
  useMutation,
  useQueryClient,
  useInfiniteQuery,
  useSuspenseQuery,
  useSuspenseInfiniteQuery,
  useQueries,
  useMutationState,
  useIsFetching,
  useIsMutating,
} from '@tanstack/react-query';

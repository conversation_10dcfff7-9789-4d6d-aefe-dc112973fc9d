/**
 * 系统相关查询Hooks
 * 🎯 核心价值：系统健康检查等系统级操作
 * 📦 功能：健康检查、网络状态监控
 * ⚡ 类型安全：完整的TypeScript支持
 */

import React from 'react';
import {
  useQuery,
  useQueryClient,
  type UseQueryOptions,
} from '@tanstack/react-query';
import { queryKeys, queryOptions } from '../config';
import { checkConnection } from '@/lib/api/client';

// === 健康检查查询 ===
export function useHealthCheck(
  options?: Omit<UseQueryOptions<boolean>, 'queryKey' | 'queryFn'>
) {
  return useQuery({
    queryKey: queryKeys.system.health(),
    queryFn: () => checkConnection(),
    ...queryOptions.system,
    // 健康检查失败时不显示错误，静默处理
    retry: 1,
    retryDelay: 1000,
    ...options,
  });
}



// === 系统状态组合hook ===
export function useSystemStatus() {
  const healthQuery = useHealthCheck();

  return {
    isHealthy: healthQuery.data ?? false,
    isLoading: healthQuery.isLoading,
    isError: healthQuery.isError,
    error: healthQuery.error,
    refetch: () => {
      healthQuery.refetch();
    },
  };
}

// === 系统监控hook ===
export function useSystemMonitoring(options?: {
  healthCheckInterval?: number;
  onHealthChange?: (isHealthy: boolean) => void;
}) {
  const { healthCheckInterval = 30000, onHealthChange } = options || {};

  const healthQuery = useHealthCheck({
    refetchInterval: healthCheckInterval,
    refetchIntervalInBackground: true,
  });

  // 健康状态变化时的回调
  React.useEffect(() => {
    if (onHealthChange && healthQuery.data !== undefined) {
      onHealthChange(healthQuery.data);
    }
  }, [healthQuery.data, onHealthChange]);

  return {
    isHealthy: healthQuery.data ?? false,
    isMonitoring: !healthQuery.isError,
    lastCheck: healthQuery.dataUpdatedAt,
    error: healthQuery.error,
  };
}

// === 网络状态hook ===
export function useNetworkStatus() {
  const [isOnline, setIsOnline] = React.useState(
    typeof navigator !== 'undefined' ? navigator.onLine : true
  );

  React.useEffect(() => {
    const handleOnline = () => setIsOnline(true);
    const handleOffline = () => setIsOnline(false);

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  return isOnline;
}

// === 系统诊断hook ===
export function useSystemDiagnostics() {
  const queryClient = useQueryClient();
  const healthQuery = useHealthCheck();
  const networkStatus = useNetworkStatus();

  const getDiagnostics = React.useCallback(() => {
    const queryCache = queryClient.getQueryCache();
    const queries = queryCache.getAll();

    return {
      network: {
        isOnline: networkStatus,
        apiHealthy: healthQuery.data ?? false,
      },
      cache: {
        totalQueries: queries.length,
        activeQueries: queries.filter(q => q.state.fetchStatus === 'fetching').length,
        staleQueries: queries.filter(q => q.isStale()).length,
        errorQueries: queries.filter(q => q.state.status === 'error').length,
      },
      performance: {
        lastHealthCheck: healthQuery.dataUpdatedAt,
        healthCheckDuration: healthQuery.isFetching ? 'checking...' : 'completed',
      },
    };
  }, [queryClient, healthQuery, networkStatus]);

  return {
    getDiagnostics,
    isHealthy: healthQuery.data && networkStatus,
    diagnostics: getDiagnostics(),
  };
}



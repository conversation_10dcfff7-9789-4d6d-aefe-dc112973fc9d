/**
 * 项目相关查询Hooks
 * 🎯 核心价值：项目数据的查询和变更操作
 * 📦 功能：项目列表、详情、创建、更新、删除
 * ⚡ 类型安全：完整的TypeScript支持
 */

import {
  useQuery,
  useMutation,
  useQueryClient,
  type UseQueryOptions,
  type UseMutationOptions,
} from '@tanstack/react-query';
import { queryKeys, queryOptions } from '../config';
import { projectApi } from '@/lib/api/client';
import type {
  ProjectResponse,
  CreateProjectRequest,
  PaginatedData,
} from '@/lib/types/api';

// === 项目列表查询 ===
export function useProjects(
  options?: Omit<UseQueryOptions<PaginatedData<ProjectResponse[]>>, 'queryKey' | 'queryFn'>
) {
  return useQuery({
    queryKey: queryKeys.projects.lists(),
    queryFn: () => projectApi.getProjects(),
    ...queryOptions.user,
    ...options,
  });
}

// === 单个项目查询 ===
export function useProject(
  projectId: string,
  options?: Omit<UseQueryOptions<ProjectResponse>, 'queryKey' | 'queryFn'>
) {
  return useQuery({
    queryKey: queryKeys.projects.detail(projectId),
    queryFn: () => projectApi.getProject(projectId),
    enabled: !!projectId,
    ...queryOptions.user,
    ...options,
  });
}

// === 项目设置查询 ===
export function useProjectSettings(
  projectId: string,
  options?: Omit<UseQueryOptions<any>, 'queryKey' | 'queryFn'>
) {
  return useQuery({
    queryKey: queryKeys.projects.settings(projectId),
    queryFn: () => projectApi.getProject(projectId), // 假设设置包含在项目详情中
    enabled: !!projectId,
    ...queryOptions.user,
    ...options,
  });
}

// === 创建项目变更 ===
export function useCreateProject(
  options?: UseMutationOptions<ProjectResponse, Error, CreateProjectRequest>
) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreateProjectRequest) => projectApi.createProject(data),
    onSuccess: (newProject) => {
      // 更新项目列表缓存
      queryClient.invalidateQueries({
        queryKey: queryKeys.projects.lists(),
      });
      
      // 设置新项目的缓存
      queryClient.setQueryData(
        queryKeys.projects.detail(newProject.data.id),
        newProject
      );
    },
    ...options,
  });
}

// === 更新项目变更 ===
export function useUpdateProject(
  options?: UseMutationOptions<ProjectResponse, Error, { id: string; data: Partial<CreateProjectRequest> }>
) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }) => projectApi.updateProject(id, data),
    onSuccess: (updatedProject, { id }) => {
      // 更新项目详情缓存
      queryClient.setQueryData(
        queryKeys.projects.detail(id),
        updatedProject
      );
      
      // 更新项目列表缓存
      queryClient.invalidateQueries({
        queryKey: queryKeys.projects.lists(),
      });
    },
    ...options,
  });
}

// === 删除项目变更 ===
export function useDeleteProject(
  options?: UseMutationOptions<void, Error, string>
) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (projectId: string) => projectApi.deleteProject(projectId),
    onSuccess: (_, projectId) => {
      // 移除项目详情缓存
      queryClient.removeQueries({
        queryKey: queryKeys.projects.detail(projectId),
      });
      
      // 更新项目列表缓存
      queryClient.invalidateQueries({
        queryKey: queryKeys.projects.lists(),
      });
    },
    ...options,
  });
}

// === 批量操作hooks ===

// 预加载项目详情
export function usePrefetchProject() {
  const queryClient = useQueryClient();

  return (projectId: string) => {
    queryClient.prefetchQuery({
      queryKey: queryKeys.projects.detail(projectId),
      queryFn: () => projectApi.getProject(projectId),
      ...queryOptions.user,
    });
  };
}

// 获取项目缓存数据（不触发网络请求）
export function useProjectCache(projectId: string) {
  const queryClient = useQueryClient();
  
  return queryClient.getQueryData<ProjectResponse>(
    queryKeys.projects.detail(projectId)
  );
}

// 手动设置项目缓存
export function useSetProjectCache() {
  const queryClient = useQueryClient();

  return (projectId: string, data: ProjectResponse) => {
    queryClient.setQueryData(
      queryKeys.projects.detail(projectId),
      data
    );
  };
}

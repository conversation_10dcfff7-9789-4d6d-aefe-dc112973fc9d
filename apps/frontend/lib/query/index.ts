/**
 * TanStack Query 统一导出
 * 🎯 核心价值：统一查询功能导出
 * 📦 功能：配置、Provider、hooks统一导出
 * ⚡ 便利性：一站式导入
 */

// === 配置导出 ===
export {
  createQueryClient,
  queryClient,
  queryKeys,
  queryOptions,
  QUERY_CONFIG,
  type QueryKey,
  type QueryOptions,
} from './config';

// === Provider导出 ===
export {
  QueryProvider,
  getQueryClient,
} from './provider';

// === 自定义hooks导出 ===
export * from './hooks';

// === TanStack Query核心导出 ===
export {
  useQuery,
  useMutation,
  useQueryClient,
  useInfiniteQuery,
  useSuspenseQuery,
  useSuspenseInfiniteQuery,
  useQueries,
  useMutationState,
  useIsFetching,
  useIsMutating,
} from '@tanstack/react-query';

// === 类型导出 ===
export type {
  QueryClient,
  UseQueryOptions,
  UseMutationOptions,
  UseInfiniteQueryOptions,
  QueryKey as TanStackQueryKey,
  QueryFunction,
  MutationFunction,
  InfiniteData,
} from '@tanstack/react-query';

/**
 * TanStack Query 配置
 * 🎯 核心价值：统一查询配置，优化缓存策略
 * 📦 功能：查询客户端配置、默认选项、错误处理
 * ⚡ 类型安全：完整的TypeScript支持
 */

import { QueryClient, DefaultOptions } from '@tanstack/react-query';

// === 查询默认配置 ===
const queryDefaults: DefaultOptions = {
  queries: {
    // 缓存时间：5分钟
    staleTime: 5 * 60 * 1000,
    // 垃圾回收时间：10分钟
    gcTime: 10 * 60 * 1000,
    // 重试配置
    retry: (failureCount, error: unknown) => {
      // 类型保护：检查是否是对象且有相关属性
      const errorObj = error as any;

      // 4xx错误不重试
      if (errorObj?.status >= 400 && errorObj?.status < 500) {
        return false;
      }
      // 最多重试3次
      return failureCount < 3;
    },
    // 重试延迟：指数退避
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
    // 窗口聚焦时重新获取
    refetchOnWindowFocus: false,
    // 网络重连时重新获取
    refetchOnReconnect: true,
    // 组件挂载时重新获取
    refetchOnMount: true,
  },
  mutations: {
    // 变更重试配置
    retry: (failureCount, error: unknown) => {
      // 类型保护：检查是否是对象且有相关属性
      const errorObj = error as any;

      // 4xx错误不重试
      if (errorObj?.status >= 400 && errorObj?.status < 500) {
        return false;
      }
      // 最多重试1次
      return failureCount < 1;
    },
    // 重试延迟
    retryDelay: 1000,
  },
};

// === 查询键工厂 ===
export const queryKeys = {
  // 项目相关
  projects: {
    all: ['projects'] as const,
    lists: () => [...queryKeys.projects.all, 'list'] as const,
    list: (filters: Record<string, any>) => [...queryKeys.projects.lists(), { filters }] as const,
    details: () => [...queryKeys.projects.all, 'detail'] as const,
    detail: (id: string) => [...queryKeys.projects.details(), id] as const,
    settings: (id: string) => [...queryKeys.projects.detail(id), 'settings'] as const,
  },
  
  // 数据相关
  data: {
    all: ['data'] as const,
    colors: (projectId: string) => [...queryKeys.data.all, 'colors', projectId] as const,
    grid: (projectId: string) => [...queryKeys.data.all, 'grid', projectId] as const,
  },
  
  // 系统相关
  system: {
    all: ['system'] as const,
    health: () => [...queryKeys.system.all, 'health'] as const,
  },
} as const;

// === 错误处理 ===
export const defaultErrorHandler = (error: unknown) => {
  process.env.NODE_ENV === 'development' && console.error('Query error:', error);

  // 类型保护：检查是否是对象且有相关属性
  const errorObj = error as any;

  // 这里可以集成错误上报服务
  // 例如：Sentry, LogRocket 等

  // 根据错误类型显示不同的提示
  if (errorObj?.status === 401) {
    // 处理认证错误
    process.env.NODE_ENV === 'development' && console.warn('认证失败，请重新登录');
  } else if (errorObj?.status === 403) {
    // 处理权限错误
    process.env.NODE_ENV === 'development' && console.warn('权限不足');
  } else if (errorObj?.status >= 500) {
    // 处理服务器错误
    process.env.NODE_ENV === 'development' && console.error('服务器错误，请稍后重试');
  } else if (errorObj?.name === 'NetworkError') {
    // 处理网络错误
    process.env.NODE_ENV === 'development' && console.warn('网络连接异常，请检查网络');
  }
};

// === 查询客户端配置 ===
export const createQueryClient = () => {
  return new QueryClient({
    defaultOptions: queryDefaults,
    
    // 全局错误处理
    mutationCache: undefined, // 可以在这里添加全局变更缓存配置
    queryCache: undefined,    // 可以在这里添加全局查询缓存配置
  });
};

// === 查询配置常量 ===
export const QUERY_CONFIG = {
  // 缓存时间配置
  STALE_TIME: {
    SHORT: 1 * 60 * 1000,      // 1分钟
    MEDIUM: 5 * 60 * 1000,     // 5分钟
    LONG: 30 * 60 * 1000,      // 30分钟
    VERY_LONG: 60 * 60 * 1000, // 1小时
  },
  
  // 垃圾回收时间配置
  GC_TIME: {
    SHORT: 5 * 60 * 1000,      // 5分钟
    MEDIUM: 10 * 60 * 1000,    // 10分钟
    LONG: 30 * 60 * 1000,      // 30分钟
    VERY_LONG: 60 * 60 * 1000, // 1小时
  },
  
  // 重试配置
  RETRY: {
    NONE: 0,
    LOW: 1,
    MEDIUM: 3,
    HIGH: 5,
  },
} as const;

// === 查询选项预设 ===
export const queryOptions = {
  // 实时数据（短缓存）
  realtime: {
    staleTime: QUERY_CONFIG.STALE_TIME.SHORT,
    gcTime: QUERY_CONFIG.GC_TIME.SHORT,
    refetchInterval: 30000, // 30秒自动刷新
  },
  
  // 静态数据（长缓存）
  static: {
    staleTime: QUERY_CONFIG.STALE_TIME.VERY_LONG,
    gcTime: QUERY_CONFIG.GC_TIME.VERY_LONG,
    refetchOnWindowFocus: false,
    refetchOnReconnect: false,
  },
  
  // 用户数据（中等缓存）
  user: {
    staleTime: QUERY_CONFIG.STALE_TIME.MEDIUM,
    gcTime: QUERY_CONFIG.GC_TIME.MEDIUM,
  },
  
  // 系统数据（短缓存，高重试）
  system: {
    staleTime: QUERY_CONFIG.STALE_TIME.SHORT,
    gcTime: QUERY_CONFIG.GC_TIME.SHORT,
    retry: QUERY_CONFIG.RETRY.HIGH,
  },
} as const;

// === 导出默认查询客户端 ===
export const queryClient = createQueryClient();

// === 类型导出 ===
export type QueryKey = typeof queryKeys;
export type QueryOptions = typeof queryOptions;

{"ci": {"collect": {"url": ["http://localhost:3000", "http://localhost:3000/api/health"], "numberOfRuns": 3, "settings": {"chromeFlags": "--no-sandbox --headless --disable-gpu --disable-dev-shm-usage", "preset": "desktop", "throttling": {"rttMs": 40, "throughputKbps": 10240, "cpuSlowdownMultiplier": 1, "requestLatencyMs": 0, "downloadThroughputKbps": 0, "uploadThroughputKbps": 0}, "emulatedFormFactor": "desktop", "locale": "zh-CN"}}, "assert": {"preset": "lighthouse:recommended", "assertions": {"categories:performance": ["error", {"minScore": 0.8}], "categories:accessibility": ["error", {"minScore": 0.9}], "categories:best-practices": ["error", {"minScore": 0.9}], "categories:seo": ["error", {"minScore": 0.9}], "categories:pwa": ["warn", {"minScore": 0.6}], "first-contentful-paint": ["error", {"maxNumericValue": 2000}], "largest-contentful-paint": ["error", {"maxNumericValue": 3000}], "cumulative-layout-shift": ["error", {"maxNumericValue": 0.1}], "total-blocking-time": ["error", {"maxNumericValue": 300}], "speed-index": ["error", {"maxNumericValue": 2500}], "uses-responsive-images": "warn", "uses-optimized-images": "warn", "uses-webp-images": "warn", "uses-text-compression": "error", "unused-css-rules": "warn", "unused-javascript": "warn", "modern-image-formats": "warn", "efficient-animated-content": "warn", "color-contrast": "error", "image-alt": "error", "label": "error", "link-name": "error", "button-name": "error", "is-on-https": "error", "uses-http2": "warn", "no-vulnerable-libraries": "error", "csp-xss": "warn"}}, "upload": {"target": "temporary-public-storage"}}}
/**
 * 灰色模式显示逻辑测试
 * 验证重构后的灰色模式实现是否符合需求文档的所有验收标准
 */

import { describe, it, expect } from 'vitest';
import type { BaseDisplayMode, GridConfig } from '@/components/grid-system/types';
import { DEFAULT_GRID_CONFIG } from '@/components/grid-system/types';
import type { CellData } from '@/lib/types/grid';

// 测试数据
const mockCellData: CellData = {
  id: 'test-cell-1',
  row: 5,
  col: 10,
  x: 5,
  y: 10,
  index: 55,
  color: '#ff0000',
  colorMappingValue: 3,
  level: 3,
  group: 'A', // 使用字母组而不是数字组
  isActive: true,
  number: 3, // 向后兼容字段
};

const mockInactiveCellData: CellData = {
  ...mockCellData,
  isActive: false
};

const createGridConfig = (displayMode: BaseDisplayMode, grayModeEnabled: boolean = false): GridConfig => ({
  ...DEFAULT_GRID_CONFIG,
  displayMode,
  grayModeEnabled,
});

describe('灰色模式显示逻辑测试', () => {
  describe('基础配置测试', () => {
    it('应该正确创建基础显示模式配置', () => {
      const config = createGridConfig('coordinates', false);

      expect(config.displayMode).toBe('coordinates');
      expect(config.grayModeEnabled).toBe(false);
      expect(config.fontSize).toBe(12);
      expect(config.cellShape).toBe('square');
    });

    it('应该正确创建启用灰色模式的配置', () => {
      const config = createGridConfig('value', true);

      expect(config.displayMode).toBe('value');
      expect(config.grayModeEnabled).toBe(true);
    });
  });

  describe('显示模式验证', () => {
    it('应该支持所有基础显示模式', () => {
      const modes: BaseDisplayMode[] = ['coordinates', 'value', 'color'];

      modes.forEach(mode => {
        const config = createGridConfig(mode, false);
        expect(config.displayMode).toBe(mode);
      });
    });

    it('应该正确处理灰色模式开关', () => {
      const configDisabled = createGridConfig('color', false);
      const configEnabled = createGridConfig('color', true);

      expect(configDisabled.grayModeEnabled).toBe(false);
      expect(configEnabled.grayModeEnabled).toBe(true);
    });
  });

  describe('单元格数据验证', () => {
    it('应该包含必要的单元格属性', () => {
      expect(mockCellData.x).toBe(5);
      expect(mockCellData.y).toBe(10);
      expect(mockCellData.color).toBe('#ff0000');
      expect(mockCellData.isActive).toBe(true);
    });

    it('应该正确处理非激活单元格', () => {
      expect(mockInactiveCellData.isActive).toBe(false);
      expect(mockInactiveCellData.x).toBe(mockCellData.x);
      expect(mockInactiveCellData.y).toBe(mockCellData.y);
    });
  });
});

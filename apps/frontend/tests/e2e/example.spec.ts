import { test, expect } from '@playwright/test';

test.describe('基础页面测试', () => {
  test('首页应该正常加载', async ({ page }) => {
    // 访问首页
    await page.goto('/');
    
    // 检查页面标题
    await expect(page).toHaveTitle(/cube/);
    
    // 检查页面是否包含主要内容
    await expect(page.locator('body')).toBeVisible();
  });

  test('页面应该响应式设计', async ({ page }) => {
    // 测试桌面视图
    await page.setViewportSize({ width: 1200, height: 800 });
    await page.goto('/');
    await expect(page.locator('body')).toBeVisible();
    
    // 测试移动端视图
    await page.setViewportSize({ width: 375, height: 667 });
    await page.goto('/');
    await expect(page.locator('body')).toBeVisible();
  });

  test('导航功能测试', async ({ page }) => {
    await page.goto('/');
    
    // 等待页面完全加载
    await page.waitForLoadState('networkidle');
    
    // 检查页面是否可交互
    await expect(page.locator('body')).toBeVisible();
  });
});

/**
 * 统一状态管理迁移测试
 * 🎯 核心价值：确保统一状态管理器能够正确迁移现有状态，保持向后兼容性
 * 📦 测试范围：状态迁移、向后兼容性、性能验证、数据完整性
 * 🔄 测试策略：单元测试、集成测试、性能测试、兼容性测试
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { renderHook, act } from '@testing-library/react';
import { useUnifiedMatrixStore } from '@/lib/stores/UnifiedMatrixStore';
import {
  useBasicDataStore,
  useGridConfigStore,
  useStyleStore,
  useDynamicStyleStore,
  useLegacyInterface
} from '@/lib/stores/LegacyHooks';
import type { BasicColorType, GroupType } from '@/lib/types/matrix';
import type { BaseDisplayMode } from '@/components/grid-system/types';

// Mock performance API
Object.defineProperty(window, 'performance', {
  value: {
    now: vi.fn(() => Date.now())
  }
});

describe('统一状态管理迁移测试', () => {
  beforeEach(() => {
    // 清理localStorage
    localStorage.clear();
    vi.clearAllMocks();
  });

  afterEach(() => {
    localStorage.clear();
  });

  describe('统一状态管理器基础功能', () => {
    it('应该正确初始化统一状态', () => {
      const { result } = renderHook(() => useUnifiedMatrixStore());
      
      expect(result.current.isInitialized).toBe(true);
      expect(result.current.isHydrated).toBe(true);
      expect(result.current.matrixData).not.toBeNull();
      expect(result.current.gridData).toHaveLength(1089); // 33x33网格
      expect(result.current.version).toBe(1);
    });

    it('应该正确初始化矩阵数据', () => {
      const { result } = renderHook(() => useUnifiedMatrixStore());
      
      act(() => {
        result.current.initializeMatrixData();
      });

      expect(result.current.matrixData).not.toBeNull();
      expect(result.current.isInitialized).toBe(true);
      expect(result.current.matrixData?.byCoordinate.size).toBeGreaterThan(0);
    });

    it('应该正确更新配置', () => {
      const { result } = renderHook(() => useUnifiedMatrixStore());
      
      act(() => {
        result.current.updateGridConfig({
          displayMode: 'color',
          colorModeEnabled: true
        });
      });

      expect(result.current.gridConfig.displayMode).toBe('color');
      expect(result.current.gridConfig.colorModeEnabled).toBe(true);
    });

    it('应该正确管理交互状态', () => {
      const { result } = renderHook(() => useUnifiedMatrixStore());
      
      act(() => {
        result.current.setHovered('cell-1', true);
        result.current.setClicked('cell-2', true);
        result.current.setFocused('cell-3');
      });

      expect(result.current.interactionState.hoveredElements.has('cell-1')).toBe(true);
      expect(result.current.interactionState.clickedElements.has('cell-2')).toBe(true);
      expect(result.current.interactionState.focusedElement).toBe('cell-3');
    });
  });

  describe('计算属性功能', () => {
    it('应该正确计算可见单元格', () => {
      const { result } = renderHook(() => useUnifiedMatrixStore());
      
      act(() => {
        result.current.initializeMatrixData();
      });

      const visibleCells = result.current.computed.visibleCells;
      expect(Array.isArray(visibleCells)).toBe(true);
      expect(visibleCells.length).toBeGreaterThan(0);
    });

    it('应该正确计算渲染数据', () => {
      const { result } = renderHook(() => useUnifiedMatrixStore());
      
      act(() => {
        result.current.initializeMatrixData();
      });

      const renderData = result.current.computed.renderData;
      expect(renderData instanceof Map).toBe(true);
      expect(renderData.size).toBeGreaterThan(0);
    });

    it('应该正确计算统计信息', () => {
      const { result } = renderHook(() => useUnifiedMatrixStore());
      
      act(() => {
        result.current.initializeMatrixData();
      });

      const statistics = result.current.computed.statistics;
      expect(statistics.totalCells).toBe(1089);
      expect(statistics.visibleCells).toBeGreaterThan(0);
      expect(typeof statistics.colorDistribution).toBe('object');
      expect(typeof statistics.groupDistribution).toBe('object');
    });
  });

  describe('BasicDataStore 向后兼容性', () => {
    it('应该提供兼容的矩阵数据接口', () => {
      const { result } = renderHook(() => useBasicDataStore());
      
      expect(result.current.matrixData).not.toBeNull();
      expect(result.current.gridData).toHaveLength(1089);
      expect(typeof result.current.colorValues).toBe('object');
      expect(typeof result.current.colorVisibility).toBe('object');
      expect(typeof result.current.regenerateMatrixData).toBe('function');
      expect(typeof result.current.initializeMatrixData).toBe('function');
    });

    it('应该正确处理矩阵数据初始化', () => {
      const { result } = renderHook(() => useBasicDataStore());
      
      act(() => {
        result.current.initializeMatrixData();
      });

      expect(result.current.matrixData).not.toBeNull();
      expect(result.current._isHydrated).toBe(true);
    });

    it('应该正确处理颜色可见性控制', () => {
      const { result } = renderHook(() => useBasicDataStore());
      
      act(() => {
        result.current.toggleAllColorCells(false);
      });

      // 验证所有颜色的showCells都被设置为false
      Object.values(result.current.colorVisibility).forEach(visibility => {
        expect(visibility.showCells).toBe(false);
      });
    });

    it('应该正确处理组可见性控制', () => {
      const { result } = renderHook(() => useBasicDataStore());
      
      act(() => {
        result.current.toggleAllGroups(false);
      });

      // 验证所有组的可见性都被设置为false
      Object.values(result.current.groupVisibility).forEach(visible => {
        expect(visible).toBe(false);
      });
    });
  });

  describe('GridConfigStore 向后兼容性', () => {
    it('应该提供兼容的网格配置接口', () => {
      const { result } = renderHook(() => useGridConfigStore());
      
      // 重置到默认状态
      act(() => {
        result.current.resetGridConfig();
      });
      
      expect(result.current.baseDisplayMode).toBe('coordinates');
      expect(typeof result.current.gridConfig).toBe('object');
      expect(result.current.colorModeEnabled).toBe(false);
      expect(typeof result.current.setDisplayMode).toBe('function');
      expect(typeof result.current.setColorModeEnabled).toBe('function');
      expect(typeof result.current.toggleColorMode).toBe('function');
    });

    it('应该正确处理显示模式切换', () => {
      const { result } = renderHook(() => useGridConfigStore());
      
      act(() => {
        result.current.setDisplayMode('color');
      });

      expect(result.current.baseDisplayMode).toBe('color');
      expect(result.current.gridConfig.displayMode).toBe('color');
    });

    it('应该正确处理颜色模式切换', () => {
      const { result } = renderHook(() => useGridConfigStore());
      
      // 确保初始状态
      act(() => {
        result.current.resetGridConfig();
      });
      
      expect(result.current.colorModeEnabled).toBe(false);
      
      act(() => {
        result.current.toggleColorMode();
      });

      expect(result.current.colorModeEnabled).toBe(true);
    });

    it('应该正确判断单元格激活状态', () => {
      const { result } = renderHook(() => useGridConfigStore());
      
      // 坐标模式下应该激活
      expect(result.current.isCellActive(0, 0)).toBe(true);
      
      act(() => {
        result.current.setDisplayMode('color');
      });
      
      // 颜色模式下也应该激活
      expect(result.current.isCellActive(0, 0)).toBe(true);
    });
  });

  describe('StyleStore 向后兼容性', () => {
    it('应该提供兼容的样式配置接口', () => {
      const { result } = renderHook(() => useStyleStore());
      
      expect(typeof result.current.config).toBe('object');
      expect(result.current.theme).toBe('light');
      expect(typeof result.current.updateConfig).toBe('function');
    });

    it('应该正确更新样式配置', () => {
      const { result } = renderHook(() => useStyleStore());
      
      act(() => {
        result.current.updateConfig({
          fontSize: 16,
          primaryColor: '#ff0000'
        });
      });

      expect(result.current.config.fontSize).toBe(16);
      expect(result.current.config.primaryColor).toBe('#ff0000');
    });
  });

  describe('DynamicStyleStore 向后兼容性', () => {
    it('应该提供兼容的动态样式接口', () => {
      const { result } = renderHook(() => useDynamicStyleStore());
      
      expect(typeof result.current.deviceInfo).toBe('object');
      expect(typeof result.current.interactionState).toBe('object');
      expect(typeof result.current.setHovered).toBe('function');
      expect(typeof result.current.setClicked).toBe('function');
      expect(typeof result.current.setFocused).toBe('function');
    });

    it('应该正确处理交互状态', () => {
      const { result } = renderHook(() => useDynamicStyleStore());
      
      act(() => {
        result.current.setHovered('test-element', true);
      });

      expect(result.current.interactionState.hoveredElements.has('test-element')).toBe(true);
    });

    it('应该正确生成动态样式', () => {
      const { result } = renderHook(() => useDynamicStyleStore());
      
      const baseStyle = { backgroundColor: 'blue' };
      
      act(() => {
        result.current.setHovered('test-element', true);
      });

      const dynamicStyle = result.current.getElementStyle('test-element', baseStyle);
      
      expect(dynamicStyle.backgroundColor).toBe('blue');
      expect(dynamicStyle.transform).toBe('scale(1.05)');
      expect(dynamicStyle.transition).toBe('all 0.2s ease-in-out');
    });
  });

  describe('性能测试', () => {
    it('矩阵数据初始化应该在合理时间内完成', () => {
      const { result } = renderHook(() => useUnifiedMatrixStore());
      
      const startTime = performance.now();
      
      act(() => {
        result.current.initializeMatrixData();
      });
      
      const endTime = performance.now();
      const duration = endTime - startTime;
      
      // 初始化应该在200ms内完成
      expect(duration).toBeLessThan(200);
      expect(result.current.performanceMetrics.initializationTime).toBeGreaterThan(0);
    });

    it('批量更新应该比单个更新更高效', () => {
      const { result } = renderHook(() => useUnifiedMatrixStore());
      
      // 单个更新
      const singleUpdateStart = performance.now();
      act(() => {
        for (let i = 0; i < 10; i++) {
          result.current.updateCellData(i, { color: '#ff0000' });
        }
      });
      const singleUpdateEnd = performance.now();
      const singleUpdateDuration = singleUpdateEnd - singleUpdateStart;
      
      // 批量更新
      const batchUpdateStart = performance.now();
      act(() => {
        const updates = Array.from({ length: 10 }, (_, i) => ({
          index: i + 10,
          data: { color: '#00ff00' }
        }));
        result.current.batchUpdateCells(updates);
      });
      const batchUpdateEnd = performance.now();
      const batchUpdateDuration = batchUpdateEnd - batchUpdateStart;
      
      // 批量更新应该更快（或至少不慢太多）
      expect(batchUpdateDuration).toBeLessThanOrEqual(singleUpdateDuration * 1.5);
    });

    it('缓存应该提高重复计算的性能', () => {
      const { result } = renderHook(() => useUnifiedMatrixStore());
      
      act(() => {
        result.current.initializeMatrixData();
      });

      // 第一次计算
      const firstCalculation = performance.now();
      const visibleCells1 = result.current.computed.visibleCells;
      const firstEnd = performance.now();
      const firstDuration = firstEnd - firstCalculation;

      // 第二次计算（应该使用缓存）
      const secondCalculation = performance.now();
      const visibleCells2 = result.current.computed.visibleCells;
      const secondEnd = performance.now();
      const secondDuration = secondEnd - secondCalculation;

      // 验证结果一致
      expect(visibleCells1).toEqual(visibleCells2);
      
      // 第二次应该更快（使用缓存）
      expect(secondDuration).toBeLessThanOrEqual(firstDuration);
    });
  });

  describe('数据完整性测试', () => {
    it('状态更新应该保持数据一致性', () => {
      const { result } = renderHook(() => useUnifiedMatrixStore());
      
      act(() => {
        result.current.initializeMatrixData();
      });

      const initialMatrixData = result.current.matrixData;
      const initialGridData = result.current.gridData;
      
      // 更新配置不应该影响数据
      act(() => {
        result.current.updateGridConfig({ displayMode: 'color' });
      });

      expect(result.current.matrixData).toBe(initialMatrixData);
      expect(result.current.gridData).toBe(initialGridData);
    });

    it('重置操作应该恢复到默认状态', () => {
      const { result } = renderHook(() => useUnifiedMatrixStore());
      
      // 修改一些状态
      act(() => {
        result.current.initializeMatrixData();
        result.current.updateGridConfig({ displayMode: 'color', colorModeEnabled: true });
        result.current.updateStyleConfig({ fontSize: 20 });
        result.current.setHovered('test', true);
      });

      // 重置
      act(() => {
        result.current.resetToDefaults();
      });

      // 验证状态已重置
      expect(result.current.gridConfig.displayMode).toBe('coordinates');
      expect(result.current.gridConfig.colorModeEnabled).toBe(false);
      expect(result.current.styleConfig.fontSize).toBe(12);
      expect(result.current.interactionState.hoveredElements.size).toBe(0);
    });
  });

  describe('错误处理测试', () => {
    it('应该正确处理无效的配置更新', () => {
      const { result } = renderHook(() => useUnifiedMatrixStore());
      
      // 重置到默认状态
      act(() => {
        result.current.resetToDefaults();
      });
      
      const initialMode = result.current.gridConfig.displayMode;
      
      // 尝试更新无效配置
      act(() => {
        result.current.updateGridConfig({
          displayMode: 'invalid-mode' as BaseDisplayMode
        });
      });

      // 状态应该接受任何值（因为我们没有验证逻辑），但至少不应该崩溃
      // 在实际应用中，应该有验证逻辑来拒绝无效值
      expect(result.current.gridConfig.displayMode).toBeDefined();
    });

    it('应该正确处理缓存清理', () => {
      const { result } = renderHook(() => useUnifiedMatrixStore());
      
      act(() => {
        result.current.initializeMatrixData();
        // 触发一些计算以填充缓存
        result.current.computed.visibleCells;
        result.current.computed.renderData;
      });

      // 清理缓存
      act(() => {
        result.current.clearCache('all');
      });

      // 缓存应该被清空
      expect(result.current.cacheState.renderCache.size).toBe(0);
      expect(result.current.cacheState.computeCache.size).toBe(0);
      expect(result.current.cacheState.configCache.size).toBe(0);
    });
  });

  describe('集成测试', () => {
    it('统一接口应该与所有兼容Hook协同工作', () => {
      const { result: unifiedResult } = renderHook(() => useUnifiedMatrixStore());
      const { result: legacyResult } = renderHook(() => useLegacyInterface());
      
      // 通过统一接口初始化数据
      act(() => {
        unifiedResult.current.initializeMatrixData();
      });

      // 兼容接口应该反映相同的状态
      expect(legacyResult.current.matrixData).toBe(unifiedResult.current.matrixData);
      
      // 通过兼容接口更新配置
      act(() => {
        legacyResult.current.setDisplayMode('color');
      });

      // 统一接口应该反映更新
      expect(unifiedResult.current.gridConfig.displayMode).toBe('color');
    });

    it('多个Hook实例应该共享相同的状态', () => {
      const { result: basicResult } = renderHook(() => useBasicDataStore());
      const { result: gridResult } = renderHook(() => useGridConfigStore());
      
      // 通过一个Hook更新状态
      act(() => {
        basicResult.current.initializeMatrixData();
      });

      // 另一个Hook应该看到更新
      expect(basicResult.current.matrixData).not.toBeNull();
      
      // 通过另一个Hook更新配置
      act(() => {
        gridResult.current.setDisplayMode('value');
      });

      // 配置更新应该在所有Hook中可见
      expect(gridResult.current.baseDisplayMode).toBe('value');
    });
  });
});
/**
 * gridConfigStore 重构测试
 * 验证重构后的gridConfigStore功能
 */

import { describe, it, expect, beforeEach } from 'vitest';
import { useGridConfigStore } from '@/stores/gridConfigStore';
import { createRenderingEngine } from '@/lib/rendering';
import type { CellData } from '@/lib/types/grid';

describe('gridConfigStore 重构测试', () => {
  beforeEach(() => {
    // 重置store状态
    useGridConfigStore.getState().resetGridConfig();
  });

  describe('基础显示模式管理', () => {
    it('应该支持坐标模式', () => {
      const { setDisplayMode, baseDisplayMode } = useGridConfigStore.getState();

      setDisplayMode('coordinates');

      expect(baseDisplayMode).toBe('coordinates');
    });

    it('应该支持所有显示模式', () => {
      const { setDisplayMode } = useGridConfigStore.getState();

      // 测试颜色模式
      setDisplayMode('color');
      expect(useGridConfigStore.getState().baseDisplayMode).toBe('color');

      // 测试数值模式
      setDisplayMode('value');
      expect(useGridConfigStore.getState().baseDisplayMode).toBe('value');

      // 测试坐标模式
      setDisplayMode('coordinates');
      expect(useGridConfigStore.getState().baseDisplayMode).toBe('coordinates');
    });
  });

  describe('显示模式控制', () => {
    it('应该能设置颜色渲染模式', () => {
      const { setDisplayMode } = useGridConfigStore.getState();

      setDisplayMode('color');

      const newState = useGridConfigStore.getState();
      expect(newState.baseDisplayMode).toBe('color');
    });

    it('应该能设置数值输入模式', () => {
      const { setDisplayMode } = useGridConfigStore.getState();

      setDisplayMode('value');

      const newState = useGridConfigStore.getState();
      expect(newState.baseDisplayMode).toBe('value');
    });

    it('应该能正确同步gridConfig中的displayMode', () => {
      const { setDisplayMode, getFullGridConfig } = useGridConfigStore.getState();

      // 测试设置颜色模式
      setDisplayMode('color');
      expect(getFullGridConfig().displayMode).toBe('color');

      // 测试设置数值模式
      setDisplayMode('value');
      expect(getFullGridConfig().displayMode).toBe('value');

      // 测试设置坐标模式
      setDisplayMode('coordinates');
      expect(getFullGridConfig().displayMode).toBe('coordinates');
    });
  });

  describe('单元格内容管理', () => {
    it('坐标模式应该返回坐标字符串', () => {
      const { setDisplayMode } = useGridConfigStore.getState();
      setDisplayMode('coordinates');

      const renderingEngine = createRenderingEngine({
        displayMode: 'coordinates',
        colorModeEnabled: false,
        cellSize: 24,
        cellGap: 2,
        showBorders: true,
        enableAnimations: true,
        opacity: 1,
      });

      const testCell: CellData = {
        id: 'test-cell',
        row: 0,
        col: 0,
        x: 5,
        y: 10,
        index: 0,
        color: 'transparent',
        colorMappingValue: 0,
        level: 1,
        group: null,
        isActive: true,
        number: 0,
      };

      const renderData = renderingEngine.getCellRenderData(testCell);
      expect(renderData.content).toBe('5,10');
    });

    it('颜色模式应该返回空内容', () => {
      const { setDisplayMode } = useGridConfigStore.getState();
      setDisplayMode('color');

      const renderingEngine = createRenderingEngine({
        displayMode: 'color',
        colorModeEnabled: true,
        cellSize: 24,
        cellGap: 2,
        showBorders: true,
        enableAnimations: true,
        opacity: 1,
      });

      const testCell: CellData = {
        id: 'test-cell',
        row: 0,
        col: 0,
        x: 5,
        y: 10,
        index: 0,
        color: 'red',
        colorMappingValue: 1,
        level: 1,
        group: null,
        isActive: true,
        number: 0,
      };

      const renderData = renderingEngine.getCellRenderData(testCell);
      expect(renderData.content).toBeNull();
    });

    it('数值模式应该返回映射字符', () => {
      const { setDisplayMode } = useGridConfigStore.getState();
      setDisplayMode('value');

      const renderingEngine = createRenderingEngine({
        displayMode: 'value',
        colorModeEnabled: false,
        cellSize: 24,
        cellGap: 2,
        showBorders: true,
        enableAnimations: true,
        opacity: 1,
      });

      // 测试有颜色的格子
      const testCellWithColor: CellData = {
        id: 'test-cell-color',
        row: 0,
        col: 0,
        x: 5,
        y: 10,
        index: 0,
        color: 'red',
        colorMappingValue: 1,
        level: 1,
        group: null,
        isActive: true,
        number: 0,
      };

      const renderDataWithColor = renderingEngine.getCellRenderData(testCellWithColor);
      expect(renderDataWithColor.content).toBe('1'); // red的mappingValue是1

      // 测试黑色格子（特殊坐标）
      const testCellBlack: CellData = {
        id: 'test-cell-black',
        row: 0,
        col: 0,
        x: 0,
        y: 0,
        index: 0,
        color: 'black',
        colorMappingValue: 0,
        level: 1,
        group: null,
        isActive: true,
        number: 0,
      };

      const renderDataBlack = renderingEngine.getCellRenderData(testCellBlack);
      expect(renderDataBlack.content).toBe('A'); // (0,0)对应字母A

      // 测试无数据的格子
      const testCellEmpty: CellData = {
        id: 'test-cell-empty',
        row: 0,
        col: 0,
        x: 5,
        y: 10,
        index: 0,
        color: 'transparent',
        colorMappingValue: 0,
        level: 1,
        group: null,
        isActive: true,
        number: 0,
      };

      const renderDataEmpty = renderingEngine.getCellRenderData(testCellEmpty);
      expect(renderDataEmpty.content).toBeNull();
    });
  });

  describe('单元格激活状态', () => {
    it('所有模式的单元格都应该激活', () => {
      const { setDisplayMode, isCellActive } = useGridConfigStore.getState();

      // 坐标模式
      setDisplayMode('coordinates');
      expect(isCellActive(5, 10)).toBe(true);

      // 颜色模式
      setDisplayMode('color');
      expect(isCellActive(5, 10)).toBe(true);

      // 数值模式
      setDisplayMode('value');
      expect(isCellActive(5, 10)).toBe(true);
    });
  });

  describe('配置重置', () => {
    it('应该能重置所有配置到默认状态', () => {
      let store = useGridConfigStore.getState();

      // 修改一些状态
      store.setDisplayMode('color');
      store.setDisplayMode('value');

      // 重置
      store.resetGridConfig();

      // 验证重置结果
      store = useGridConfigStore.getState();
      expect(store.baseDisplayMode).toBe('coordinates');
    });
  });
});

/**
 * 矩阵重构验证测试
 * 🎯 目标：验证重构后的矩阵系统功能完整性
 * 📦 测试范围：类型定义、常量、工具函数、验证逻辑
 */

import { describe, it, expect } from 'vitest';

// 测试类型定义
import type {
  BasicColorType,
  GroupType,
  MatrixDataPoint,
  MatrixData,
  CellData,
  ColorValue,
  ColorVisibility,
  BlackCellData
} from '@/lib/types/matrix';

// 测试常量
import {
  GROUP_A_DATA,
  AVAILABLE_LEVELS,
  DEFAULT_COLOR_VALUES,
  SPECIAL_COORDINATES,
  GRID_DIMENSIONS,
  GRID_CENTER,
  GROUP_OFFSET_CONFIGS
} from '@/stores/constants/matrix';

// 测试工具函数
import {
  isValidCoordinate,
  generateGridData,
  calculateGroupCoordinates,
  generateMatrixData
} from '@/lib/utils/matrixUtils';

import {
  generateDefaultColorVisibility,
  generateDefaultGroupVisibility,
  generateDefaultBlackCellData,
  getAllColorTypes,
  getAllGroupTypes
} from '@/lib/utils/matrixHelpers';

// 测试验证函数
import {
  validateGridData,
  validateGroupOffsetConsistency,
  validateCoordinate
} from '@/lib/validation/matrixValidation';

describe('矩阵重构验证测试', () => {
  describe('类型定义测试', () => {
    it('应该正确定义基础颜色类型', () => {
      const colors: BasicColorType[] = ['black', 'red', 'orange', 'yellow', 'green', 'cyan', 'blue', 'purple', 'pink'];
      expect(colors).toHaveLength(9);
    });

    it('应该正确定义组类型', () => {
      const groups: GroupType[] = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M'];
      expect(groups).toHaveLength(13);
    });
  });

  describe('常量定义测试', () => {
    it('应该包含A组基础数据', () => {
      expect(GROUP_A_DATA).toBeDefined();
      expect(GROUP_A_DATA.black).toBeDefined();
      expect(GROUP_A_DATA.red).toBeDefined();
      expect(GROUP_A_DATA.black[1]).toEqual([[0, 0]]);
    });

    it('应该包含可用级别映射', () => {
      expect(AVAILABLE_LEVELS).toBeDefined();
      expect(AVAILABLE_LEVELS.red).toEqual([1, 2, 3, 4]);
      expect(AVAILABLE_LEVELS.black).toEqual([1]);
    });

    it('应该包含默认颜色值', () => {
      expect(DEFAULT_COLOR_VALUES).toBeDefined();
      expect(DEFAULT_COLOR_VALUES.black.name).toBe('黑色');
      expect(DEFAULT_COLOR_VALUES.red.mappingValue).toBe(1);
    });

    it('应该包含特殊坐标映射', () => {
      expect(SPECIAL_COORDINATES).toBeDefined();
      expect(SPECIAL_COORDINATES.get('0,0')).toBe('A');
      expect(SPECIAL_COORDINATES.get('16,0')).toBe('B');
    });

    it('应该包含网格尺寸常量', () => {
      expect(GRID_DIMENSIONS.ROWS).toBe(33);
      expect(GRID_DIMENSIONS.COLS).toBe(33);
      expect(GRID_CENTER.X).toBe(16);
      expect(GRID_CENTER.Y).toBe(16);
    });

    it('应该包含组偏移配置', () => {
      expect(GROUP_OFFSET_CONFIGS).toBeDefined();
      expect(GROUP_OFFSET_CONFIGS.A.defaultOffset).toEqual([0, 0]);
      expect(GROUP_OFFSET_CONFIGS.B.defaultOffset).toEqual([16, 0]);
    });
  });

  describe('工具函数测试', () => {
    it('应该正确验证坐标', () => {
      expect(isValidCoordinate(0, 0)).toBe(true);
      expect(isValidCoordinate(16, 16)).toBe(true);
      expect(isValidCoordinate(-16, -16)).toBe(true);
      expect(isValidCoordinate(17, 0)).toBe(false);
      expect(isValidCoordinate(0, 17)).toBe(false);
    });

    it('应该生成正确的网格数据', () => {
      const gridData = generateGridData();
      expect(gridData).toHaveLength(1089); // 33 * 33
      
      // 检查中心点
      const centerCell = gridData.find(cell => cell.x === 0 && cell.y === 0);
      expect(centerCell).toBeDefined();
      expect(centerCell?.number).toBe(545);
    });

    it('应该计算组坐标', () => {
      const coordinates = calculateGroupCoordinates('A', 'red', 1);
      expect(coordinates).toHaveLength(1);
      expect(coordinates[0].coords).toEqual([8, 0]);
      expect(coordinates[0].group).toBe('A');
      expect(coordinates[0].color).toBe('red');
      expect(coordinates[0].level).toBe(1);
    });

    it('应该生成矩阵数据', () => {
      const matrixData = generateMatrixData();
      expect(matrixData.byCoordinate).toBeInstanceOf(Map);
      expect(matrixData.byGroup).toBeDefined();
      expect(matrixData.byColor).toBeDefined();
      expect(matrixData.byLevel).toBeDefined();
      
      // 检查A组数据
      expect(matrixData.byGroup.A).toBeDefined();
      expect(matrixData.byGroup.A.length).toBeGreaterThan(0);
    });
  });

  describe('辅助函数测试', () => {
    it('应该生成默认颜色可见性', () => {
      const visibility = generateDefaultColorVisibility();
      expect(visibility.red.showCells).toBe(true);
      expect(visibility.red.showLevel1).toBe(true);
      expect(visibility.black.showLevel1).toBe(true);
    });

    it('应该生成默认组可见性', () => {
      const visibility = generateDefaultGroupVisibility();
      expect(visibility.A).toBe(true);
      expect(visibility.M).toBe(true);
    });

    it('应该生成默认黑色格子数据', () => {
      const blackCellData = generateDefaultBlackCellData();
      expect(blackCellData.visibility).toBe(true);
      expect(blackCellData.coordinates).toHaveLength(13);
      expect(blackCellData.coordinates[0].letter).toBe('A');
    });

    it('应该获取所有颜色类型', () => {
      const colors = getAllColorTypes();
      expect(colors).toHaveLength(9);
      expect(colors).toContain('black');
      expect(colors).toContain('red');
    });

    it('应该获取所有组类型', () => {
      const groups = getAllGroupTypes();
      expect(groups).toHaveLength(13);
      expect(groups).toContain('A');
      expect(groups).toContain('M');
    });
  });

  describe('验证函数测试', () => {
    it('应该验证网格数据', () => {
      const result = validateGridData();
      expect(result.isValid).toBe(true);
      expect(result.totalCells).toBe(1089);
      expect(result.centerCell).toBeDefined();
      expect(result.errors).toHaveLength(0);
    });

    it('应该验证组偏移一致性', () => {
      const result = validateGroupOffsetConsistency();
      expect(result.isValid).toBe(true);
      expect(result.inconsistencies).toHaveLength(0);
    });

    it('应该验证坐标', () => {
      expect(validateCoordinate(0, 0)).toBe(true);
      expect(validateCoordinate(16, 16)).toBe(true);
      expect(validateCoordinate(17, 0)).toBe(false);
    });
  });
});

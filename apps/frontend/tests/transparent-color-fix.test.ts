/**
 * 透明颜色修复测试
 * 🎯 验证 color: "transparent" 的正确处理
 */

import { describe, it, expect } from 'vitest';
import { GridRenderingEngine } from '@/lib/rendering/GridRenderingEngine';
import type { CellData } from '@/lib/types/grid';
import type { RenderingConfig } from '@/lib/rendering/GridRenderingEngine';

describe('透明颜色修复测试', () => {
  const createTestConfig = (colorModeEnabled: boolean): RenderingConfig => ({
    displayMode: 'color' as const,
    colorModeEnabled,
    cellSize: 24,
    cellGap: 2,
    showBorders: true,
    enableAnimations: true,
    opacity: 1,
  });

  const createTestCell = (color: string, isActive: boolean = true): CellData => ({
    id: 'test-cell',
    row: 0,
    col: 0,
    x: 5,
    y: 10,
    index: 0,
    color,
    colorMappingValue: 0,
    level: 1,
    group: null,
    isActive,
    number: 0,
  });

  describe('透明颜色处理', () => {
    it('应该正确处理 color: "transparent"', () => {
      const engine = new GridRenderingEngine(createTestConfig(true));
      const cell = createTestCell('transparent');
      
      const renderData = engine.getCellRenderData(cell);
      
      // 验证透明颜色被正确返回
      expect(renderData.color).toBe('transparent');
      expect(renderData.style.backgroundColor).toBe('transparent');
    });

    it('应该正确处理其他有效颜色值', () => {
      const engine = new GridRenderingEngine(createTestConfig(true));
      const cell = createTestCell('#ff0000');
      
      const renderData = engine.getCellRenderData(cell);
      
      expect(renderData.color).toBe('#ff0000');
      expect(renderData.style.backgroundColor).toBe('#ff0000');
    });

    it('当颜色模式关闭时，应该返回 null', () => {
      const engine = new GridRenderingEngine(createTestConfig(false));
      const cell = createTestCell('transparent');
      
      const renderData = engine.getCellRenderData(cell);
      
      expect(renderData.color).toBe(null);
      expect(renderData.style.backgroundColor).toBe('transparent');
    });

    it('当单元格未激活时，应该返回 null', () => {
      const engine = new GridRenderingEngine(createTestConfig(true));
      const cell = createTestCell('transparent', false);
      
      const renderData = engine.getCellRenderData(cell);
      
      expect(renderData.color).toBe(null);
      expect(renderData.style.backgroundColor).toBe('transparent');
    });

    it('应该正确处理空字符串颜色', () => {
      const engine = new GridRenderingEngine(createTestConfig(true));
      const cell = createTestCell('');
      
      const renderData = engine.getCellRenderData(cell);
      
      expect(renderData.color).toBe(null);
      expect(renderData.style.backgroundColor).toBe('transparent');
    });
  });

  describe('文本颜色处理', () => {
    it('透明背景应该使用默认文本颜色', () => {
      const engine = new GridRenderingEngine(createTestConfig(true));
      const cell = createTestCell('transparent');
      
      const renderData = engine.getCellRenderData(cell);
      
      expect(renderData.style.color).toBe('#374151');
    });

    it('黑色背景应该使用白色文本', () => {
      const engine = new GridRenderingEngine(createTestConfig(true));
      const cell = createTestCell('#000000');
      
      const renderData = engine.getCellRenderData(cell);
      
      expect(renderData.style.color).toBe('#ffffff');
    });

    it('其他颜色背景应该使用黑色文本', () => {
      const engine = new GridRenderingEngine(createTestConfig(true));
      const cell = createTestCell('#ff0000');
      
      const renderData = engine.getCellRenderData(cell);
      
      expect(renderData.style.color).toBe('#000000');
    });
  });

  describe('colorMappingValue 优先级处理', () => {
    it('应该优先使用 colorMappingValue 而不是 color 字段', () => {
      const engine = new GridRenderingEngine(createTestConfig(true));

      // 模拟用户报告的情况：colorMappingValue: 2 (橙色) 但 color: "transparent"
      const cell: CellData = {
        id: 'test-cell',
        row: 12,
        col: 28,
        x: 12,
        y: 4,
        index: 424,
        color: 'transparent', // 错误的颜色值
        colorMappingValue: 2, // 正确的映射值（橙色）
        level: 1,
        group: 'B',
        isActive: true,
        number: 424,
      };

      const renderData = engine.getCellRenderData(cell);

      // 应该根据 colorMappingValue: 2 返回橙色，而不是 transparent
      expect(renderData.color).toBe('#f97316'); // 橙色的十六进制值
      expect(renderData.style.backgroundColor).toBe('#f97316');
    });

    it('当 colorMappingValue 无效时，应该使用 color 字段', () => {
      const engine = new GridRenderingEngine(createTestConfig(true));
      const cell = createTestCell('transparent');
      cell.colorMappingValue = 0; // 无效的映射值

      const renderData = engine.getCellRenderData(cell);

      // 应该使用 color 字段的值
      expect(renderData.color).toBe('transparent');
      expect(renderData.style.backgroundColor).toBe('transparent');
    });

    it('应该正确处理 colorMappingValue = 2 (橙色)', () => {
      const engine = new GridRenderingEngine(createTestConfig(true));

      const cell = createTestCell('transparent');
      cell.colorMappingValue = 2;

      const renderData = engine.getCellRenderData(cell);

      expect(renderData.color).toBe('#f97316'); // 橙色
      expect(renderData.style.backgroundColor).toBe('#f97316');
    });

    it('应该正确处理 colorMappingValue = 1 (红色)', () => {
      const engine = new GridRenderingEngine(createTestConfig(true));

      const cell = createTestCell('transparent');
      cell.colorMappingValue = 1;

      const renderData = engine.getCellRenderData(cell);

      expect(renderData.color).toBe('#ef4444'); // 红色
      expect(renderData.style.backgroundColor).toBe('#ef4444');
    });
  });

  describe('缓存行为', () => {
    it('相同的透明颜色单元格应该使用缓存', () => {
      const engine = new GridRenderingEngine(createTestConfig(true));
      const cell = createTestCell('transparent');

      const renderData1 = engine.getCellRenderData(cell);
      const renderData2 = engine.getCellRenderData(cell);

      // 应该返回相同的对象（缓存命中）
      expect(renderData1).toBe(renderData2);
    });

    it('不同颜色的单元格应该有不同的缓存', () => {
      const engine = new GridRenderingEngine(createTestConfig(true));
      const transparentCell = createTestCell('transparent');
      const redCell = createTestCell('#ff0000');

      const transparentData = engine.getCellRenderData(transparentCell);
      const redData = engine.getCellRenderData(redCell);

      expect(transparentData).not.toBe(redData);
      expect(transparentData.color).toBe('transparent');
      expect(redData.color).toBe('#ff0000');
    });
  });
});

/**
 * Map 序列化测试
 * 测试 Zustand persist 中间件对 Map 对象的序列化/反序列化处理
 */

import { describe, it, expect, beforeEach } from 'vitest';
import { generateMatrixData } from '@/lib/utils/matrixUtils';
import type { MatrixData } from '@/lib/types/matrix';

describe('Map 序列化测试', () => {
  let matrixData: MatrixData;

  beforeEach(() => {
    matrixData = generateMatrixData();
  });

  it('应该生成包含 Map 对象的矩阵数据', () => {
    expect(matrixData).toBeDefined();
    expect(matrixData.byCoordinate).toBeInstanceOf(Map);
    expect(matrixData.byCoordinate.size).toBeGreaterThan(0);
  });

  it('Map 对象应该有 forEach 方法', () => {
    expect(typeof matrixData.byCoordinate.forEach).toBe('function');
    
    // 测试 forEach 方法是否正常工作
    let count = 0;
    matrixData.byCoordinate.forEach(() => {
      count++;
    });
    
    expect(count).toBeGreaterThan(0);
    expect(count).toBe(matrixData.byCoordinate.size);
  });

  it('应该能够正确序列化和反序列化 Map 对象', () => {
    // 模拟 Zustand persist 的序列化过程
    const serializable = {
      state: {
        matrixData: {
          ...matrixData,
          byCoordinate: Array.from(matrixData.byCoordinate.entries())
        }
      }
    };

    const serialized = JSON.stringify(serializable);
    const parsed = JSON.parse(serialized);

    // 模拟反序列化过程
    const restoredMap = new Map(parsed.state.matrixData.byCoordinate);

    expect(restoredMap).toBeInstanceOf(Map);
    expect(restoredMap.size).toBe(matrixData.byCoordinate.size);
    expect(typeof restoredMap.forEach).toBe('function');

    // 验证数据完整性
    let originalCount = 0;
    let restoredCount = 0;

    matrixData.byCoordinate.forEach(() => originalCount++);
    restoredMap.forEach(() => restoredCount++);

    expect(restoredCount).toBe(originalCount);
  });

  it('应该能够从数组格式恢复 Map 对象', () => {
    // 将 Map 转换为数组格式（序列化后的格式）
    const arrayFormat = Array.from(matrixData.byCoordinate.entries());
    
    // 从数组格式恢复 Map
    const restoredMap = new Map(arrayFormat);
    
    expect(restoredMap).toBeInstanceOf(Map);
    expect(restoredMap.size).toBe(matrixData.byCoordinate.size);
    
    // 验证数据一致性
    matrixData.byCoordinate.forEach((value, key) => {
      expect(restoredMap.has(key)).toBe(true);
      expect(restoredMap.get(key)).toEqual(value);
    });
  });

  it('应该能够从普通对象格式恢复 Map 对象', () => {
    // 将 Map 转换为普通对象格式
    const objectFormat: Record<string, any> = {};
    matrixData.byCoordinate.forEach((value, key) => {
      objectFormat[key] = value;
    });
    
    // 从对象格式恢复 Map
    const restoredMap = new Map(Object.entries(objectFormat));
    
    expect(restoredMap).toBeInstanceOf(Map);
    expect(restoredMap.size).toBe(matrixData.byCoordinate.size);
    
    // 验证数据一致性
    matrixData.byCoordinate.forEach((value, key) => {
      expect(restoredMap.has(key)).toBe(true);
      expect(restoredMap.get(key)).toEqual(value);
    });
  });
});

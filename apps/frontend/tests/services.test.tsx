/**
 * 服务类测试
 * 测试ColorMappingService和SpecialCoordinateService的新方法
 */

import { describe, it, expect } from 'vitest';
import { ColorMappingService } from '@/lib/services/ColorMappingService';
import { SpecialCoordinateService } from '@/lib/services/SpecialCoordinateService';
import { DEFAULT_COLOR_VALUES } from '@/stores/constants/matrix';

describe('ColorMappingService', () => {
  it('getNumericValue应该返回正确的映射值', () => {
    const result = ColorMappingService.getNumericValue('red', 1);
    expect(typeof result).toBe('number');
    expect(result).toBeGreaterThanOrEqual(0);
  });

  it('getColorForValue应该返回正确的颜色值', () => {
    const result = ColorMappingService.getColorForValue('red', 1, DEFAULT_COLOR_VALUES);
    expect(result).toBe(DEFAULT_COLOR_VALUES.red.hex);
  });

  it('getDisplayValue应该返回正确的显示值', () => {
    const result = ColorMappingService.getDisplayValue('red', 1);
    expect(typeof result).toBe('string');
  });

  it('对于黑色应该返回null或0', () => {
    const numericValue = ColorMappingService.getNumericValue('black', 1);
    expect(numericValue).toBe(0);
    
    const displayValue = ColorMappingService.getDisplayValue('black', 1);
    expect(displayValue).toBeNull();
  });
});

describe('SpecialCoordinateService', () => {
  it('getCharacterForCoordinate应该是getCharacter的别名', () => {
    const x = 0, y = 0;
    const result1 = SpecialCoordinateService.getCharacter(x, y);
    const result2 = SpecialCoordinateService.getCharacterForCoordinate(x, y);
    expect(result1).toBe(result2);
  });

  it('getNumericValueForCharacter应该返回正确的数值', () => {
    expect(SpecialCoordinateService.getNumericValueForCharacter('A')).toBe(1);
    expect(SpecialCoordinateService.getNumericValueForCharacter('B')).toBe(2);
    expect(SpecialCoordinateService.getNumericValueForCharacter('M')).toBe(13);
    expect(SpecialCoordinateService.getNumericValueForCharacter('Z')).toBe(0); // 无效字符
    expect(SpecialCoordinateService.getNumericValueForCharacter('')).toBe(0); // 空字符
  });

  it('应该处理小写字符', () => {
    expect(SpecialCoordinateService.getNumericValueForCharacter('a')).toBe(1);
    expect(SpecialCoordinateService.getNumericValueForCharacter('m')).toBe(13);
  });

  it('应该处理无效输入', () => {
    expect(SpecialCoordinateService.getNumericValueForCharacter('123')).toBe(0);
    expect(SpecialCoordinateService.getNumericValueForCharacter('!')).toBe(0);
  });
});

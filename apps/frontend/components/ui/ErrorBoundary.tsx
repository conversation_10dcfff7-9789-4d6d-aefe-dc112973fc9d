import React, { Component, ErrorInfo, ReactNode } from 'react';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
}

interface State {
  hasError: boolean;
  error?: Error;
}

/**
 * UI组件错误边界
 * 用于捕获和处理组件初始化错误
 */
export class UIErrorBoundary extends Component<Props, State> {
  public state: State = {
    hasError: false,
  };

  public static getDerivedStateFromError(error: Error): State {
    // 更新 state 使下一次渲染能够显示降级后的 UI
    return { hasError: true, error };
  }

  public componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    process.env.NODE_ENV === 'development' && console.error('UI组件错误边界捕获到错误:', error, errorInfo);
    
    // 特殊处理模块初始化错误
    if (error.message.includes('Cannot access') && error.message.includes('before initialization')) {
      process.env.NODE_ENV === 'development' && console.warn('检测到模块初始化顺序问题，尝试重新渲染...');
      
      // 延迟重试渲染
      setTimeout(() => {
        this.setState({ hasError: false, error: undefined });
      }, 100);
    }
  }

  public render() {
    if (this.state.hasError) {
      // 自定义降级 UI
      return this.props.fallback || (
        <div className="p-4 border border-red-200 rounded-md bg-red-50">
          <h3 className="text-red-800 font-medium">组件加载错误</h3>
          <p className="text-red-600 text-sm mt-1">
            {this.state.error?.message || '组件初始化失败，请刷新页面重试'}
          </p>
          <button
            onClick={() => this.setState({ hasError: false, error: undefined })}
            className="mt-2 px-3 py-1 text-xs bg-red-100 text-red-700 rounded hover:bg-red-200"
          >
            重试
          </button>
        </div>
      );
    }

    return this.props.children;
  }
}

/**
 * HOC 包装器，为组件添加错误边界
 */
export function withErrorBoundary<P extends object>(
  Component: React.ComponentType<P>,
  fallback?: ReactNode
) {
  const WrappedComponent = (props: P) => (
    <UIErrorBoundary fallback={fallback}>
      <Component {...props} />
    </UIErrorBoundary>
  );
  
  WrappedComponent.displayName = `withErrorBoundary(${Component.displayName || Component.name})`;
  
  return WrappedComponent;
}

/**
 * Progress组件
 * 基于Radix UI + tailwind-variants构建
 */

import * as React from "react"
import * as ProgressPrimitive from "@radix-ui/react-progress"
import { tv, type VariantProps } from "tailwind-variants"
import { cn } from "@/utils/cn"

const progressVariants = tv({
  base: "relative h-4 w-full overflow-hidden rounded-full bg-secondary",
  variants: {
    size: {
      sm: "h-2",
      default: "h-4",
      lg: "h-6",
    },
  },
  defaultVariants: {
    size: "default",
  },
})

const progressIndicatorVariants = tv({
  base: "h-full w-full flex-1 bg-primary transition-all",
  variants: {
    variant: {
      default: "bg-primary",
      success: "bg-green-500",
      warning: "bg-yellow-500",
      destructive: "bg-red-500",
    },
  },
  defaultVariants: {
    variant: "default",
  },
})

interface ProgressProps
  extends React.ComponentPropsWithoutRef<typeof ProgressPrimitive.Root>,
    VariantProps<typeof progressVariants>,
    VariantProps<typeof progressIndicatorVariants> {}

const Progress = React.forwardRef<
  React.ElementRef<typeof ProgressPrimitive.Root>,
  ProgressProps
>(({ className, value, size, variant, ...props }, ref) => (
  <ProgressPrimitive.Root
    ref={ref}
    className={cn(progressVariants({ size }), className)}
    {...props}
  >
    <ProgressPrimitive.Indicator
      className={cn(progressIndicatorVariants({ variant }))}
      style={{ transform: `translateX(-${100 - (value || 0)}%)` }}
    />
  </ProgressPrimitive.Root>
))
Progress.displayName = ProgressPrimitive.Root.displayName

export { Progress }

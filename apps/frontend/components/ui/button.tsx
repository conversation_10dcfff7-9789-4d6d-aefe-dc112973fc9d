// 导入React核心库
import * as React from "react"
// 导入Radix UI的Slot组件，用于组件组合
import { Slot } from "@radix-ui/react-slot"
// 导入tailwind-variants用于样式变体管理
import { tv, type VariantProps } from "tailwind-variants"
// 导入工具函数，用于合并类名
import { cn } from "@/utils/cn"

// 定义按钮样式变体配置
const buttonVariants = tv({
  // 基础样式：内联弹性布局，居中对齐，圆角，字体设置，聚焦和禁用状态样式
  base: "inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",
  // 变体配置
  variants: {
    // 按钮样式变体
    variant: {
      // 默认样式：主色背景，主色前景文字，悬停时背景透明度90%
      default: "bg-primary text-primary-foreground hover:bg-primary/90",
      // 危险样式：危险色背景，危险色前景文字，悬停时背景透明度90%
      destructive: "bg-destructive text-destructive-foreground hover:bg-destructive/90",
      // 轮廓样式：边框，背景色，悬停时强调色背景和前景
      outline: "border border-input bg-background hover:bg-accent hover:text-accent-foreground",
      // 次要样式：次要色背景，次要色前景文字，悬停时背景透明度80%
      secondary: "bg-secondary text-secondary-foreground hover:bg-secondary/80",
      // 幽灵样式：透明背景，悬停时显示强调色背景和前景
      ghost: "hover:bg-accent hover:text-accent-foreground",
      // 链接样式：主色文字，下划线偏移，悬停时显示下划线
      link: "text-primary underline-offset-4 hover:underline",
    },
    // 按钮尺寸变体
    size: {
      // 默认尺寸：高度10，水平内边距4，垂直内边距2
      default: "h-10 px-4 py-2",
      // 小尺寸：高度9，圆角，水平内边距3
      sm: "h-9 rounded-md px-3",
      // 大尺寸：高度11，圆角，水平内边距8
      lg: "h-11 rounded-md px-8",
      // 图标尺寸：高度和宽度都为10（正方形）
      icon: "h-10 w-10",
    },
  },
  // 默认变体配置
  defaultVariants: {
    // 默认使用default样式变体
    variant: "default",
    // 默认使用default尺寸变体
    size: "default",
  },
})

// 导出按钮组件属性接口，继承原生button元素属性和样式变体属性
export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  // 是否作为子组件渲染（使用Slot组件）
  asChild?: boolean
}

// 使用forwardRef创建按钮组件，支持ref转发
const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  // 解构props参数，设置asChild默认值为false
  ({ className, variant, size, asChild = false, ...props }, ref) => {
    // 根据asChild决定使用Slot还是原生button元素
    const Comp = asChild ? Slot : "button"
    // 返回组件JSX
    return (
      <Comp
        // 合并样式变体类名和自定义类名
        className={cn(buttonVariants({ variant, size, className }))}
        // 转发ref引用
        ref={ref}
        // 传递其余props
        {...props}
      />
    )
  }
)
// 设置组件显示名称，用于React DevTools调试
Button.displayName = "Button"

// 导出按钮组件和样式变体配置
export { Button, buttonVariants }

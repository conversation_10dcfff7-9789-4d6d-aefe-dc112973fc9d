/**
 * 组件统一导出 - Features架构兼容性
 * 🎯 核心价值：提供向后兼容的组件导入路径
 * 📦 架构设计：从features模块重新导出组件，保持现有导入路径可用
 * ✅ 迁移状态：支持新旧导入路径并存
 */

// === Features模块组件导出 ===

// 样式管理组件
export {
  StylePanel,
  StyleManagementComponents,
} from '@/features/style-management/components';



// 网格系统组件 (纯UI组件)
export {
  GridMatrix,
  GridCell,
  GridErrorBoundary,
  GridLoadingState,
} from './grid-system';

// 网格系统hooks (纯UI)
export {
  useGridData,
  useGridAnimation,
} from './grid-system';

// 网格系统业务组件 (从features导出)
export {
  GridContainer,
  GridControlPanel,
  GridOverlay,
} from '@/features/grid-system';

// 网格系统业务hooks (从features导出)
export {
  useGridDataManager,
  useCellDataManager,
  useGridInteraction,
} from '@/features/grid-system';



// 开发工具组件（暂时注释掉，因为模块不存在）
// export {
//   DevToolsComponents,
// } from '@/features/dev-tools/components';

// === 向后兼容性别名导出 ===



// === 原有组件导出 ===

// ControlPanel组件类型（保持原有路径，用于向后兼容）
export type { VersionPanelProps } from './control-panel/types';

// UI组件（保持原有路径）
export * from './ui';

// === 组件配置 ===

export const COMPONENTS_CONFIG = {
  features: {
    'style-management': 'StylePanel',
    'grid-system': 'GridContainer,GridCell,GridOverlay',
  },
  compatibility: {},
} as const;

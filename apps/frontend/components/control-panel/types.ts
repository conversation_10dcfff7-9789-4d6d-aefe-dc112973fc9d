import { ReactNode } from 'react';

// 颜色系统类型（用于颜色面板）- 与stores中的ColorType保持一致
export type ColorSystemType = 'red' | 'cyan' | 'yellow' | 'purple' | 'orange' | 'green' | 'blue' | 'pink' | 'black';

// 面板配置类型
export interface PanelConfig {
  key: string;
  label: string;
  description?: string;
  icon?: string;
  category?: 'main' | 'secondary' | 'temp';
}

// 控制面板容器Props（R2新架构 - 纯容器组件）
export interface ControlPanelContainerProps {
  // 菜单栏导航相关
  activePanel: string;
  setActivePanel: (panel: string) => void;

  // 面板配置
  panelConfigs: PanelConfig[];

  // 面板内容渲染
  children: Record<string, ReactNode>;

  // 可选的自定义样式函数
  getTabStyle?: (tabKey: string, isActive: boolean) => string;
}

// 控制面板提供者Props
export interface ControlPanelProviderProps {
  activePanel: string;
  setActivePanel: (panel: string) => void;
  versionProps?: VersionPanelProps; // 可选，用于向后兼容
  getTabStyle?: (tabKey: string, isActive: boolean) => string;
}

// 版本管理面板Props
export interface VersionPanelProps {
  currentVersion: string;
  onVersionChange: (version: string) => void;
  versions: Array<{
    id: string;
    name: string;
    description: string;
  }>;
  onSaveVersion: () => void;
  onDeleteVersion: (versionName: string) => void;
  onExportData: () => void;
  onImportData: (event: React.ChangeEvent<HTMLInputElement>) => void;
}

// 颜色系统面板Props - 统一所有8种颜色
export interface ColorSystemPanelProps {
  colorType: ColorSystemType;
  onColorTypeChange: (type: ColorSystemType) => void;
  
  // 级别控制
  showLevels: Record<1 | 2 | 3 | 4, boolean>;
  onLevelToggle: (level: 1 | 2 | 3 | 4) => void;
  
  // 分组控制
  selectedGroups: Set<number>;
  onGroupToggle: (group: number) => void;
  
  // 组别互换
  swapGroup1: number;
  onSwapGroup1Change: (group: number) => void;
  swapGroup2: number;
  onSwapGroup2Change: (group: number) => void;
  onSwapGroups: () => void;
  
  // 显示控制
  showCells: boolean;
  onShowCellsToggle: () => void;
  
  // 保持R0性能优化的必要依赖
  colorCoordinates: any; // 实际类型根据需要定义
}



// 颜色级别切换组件Props
export interface ColorLevelToggleProps {
  colorType: ColorSystemType;
  showLevels: Record<1 | 2 | 3 | 4, boolean>;
  onLevelToggle: (level: 1 | 2 | 3 | 4) => void;
  onHideAllGroups: () => void;
}

// 颜色分组选择器Props
export interface ColorGroupSelectorProps {
  colorType: ColorSystemType;
  selectedGroups: Set<number>;
  onGroupToggle: (group: number) => void;
  onShowAllGroups: () => void;
  onHideAllGroups: () => void;
} 
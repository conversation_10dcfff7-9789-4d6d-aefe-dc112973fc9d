import React, { memo, Suspense } from 'react';
import { ControlPanelContainer } from './ControlPanelContainer';
import { PanelConfig, ControlPanelProviderProps } from './types';

// 动态导入业务面板组件
const StylePanel = React.lazy(() =>
  import('@/features/style-management/components').then(module => ({ default: module.StylePanel }))
);







// 加载中组件
const PanelLoading = () => (
  <div className="flex items-center justify-center py-8">
    <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
    <span className="ml-2 text-sm text-gray-600">加载中...</span>
  </div>
);

// 面板错误边界
class PanelErrorBoundary extends React.Component<
  { children: React.ReactNode; panelName: string },
  { hasError: boolean }
> {
  constructor(props: { children: React.ReactNode; panelName: string }) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError() {
    return { hasError: true };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    process.env.NODE_ENV === 'development' && console.error(`面板 ${this.props.panelName} 加载失败:`, error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="text-center text-red-500 py-8">
          <div className="text-sm">面板加载失败</div>
          <div className="text-xs mt-1">{this.props.panelName}</div>
          <button 
            className="mt-2 px-3 py-1 text-xs bg-red-100 text-red-700 rounded"
            onClick={() => this.setState({ hasError: false })}
          >
            重试
          </button>
        </div>
      );
    }

    return this.props.children;
  }
}

/**
 * 控制面板提供者组件
 * 🎯 核心价值：管理具体业务面板的加载和渲染
 * ⚡ 性能优化：动态加载、错误边界、Suspense
 * 🔧 架构：连接纯容器组件和具体业务面板
 */
export const ControlPanelProvider = memo<ControlPanelProviderProps>((props) => {
  const { activePanel, setActivePanel, getTabStyle } = props;

  // 面板配置
  const panelConfigs: PanelConfig[] = [
    {
      key: 'r2-style',
      label: '样式',
      description: '统一管理基础样式和动态样式配置',
      icon: '🎨',
      category: 'main'
    },



  ];

  // 面板内容渲染
  const panelChildren = {
    'r2-style': (
      <PanelErrorBoundary panelName="样式面板">
        <Suspense fallback={<PanelLoading />}>
          <div>
            <div className="mb-3 p-2 bg-blue-50 rounded border border-blue-200">
              <div className="text-sm font-medium text-blue-800">🎨 样式面板</div>
              <div className="text-xs text-blue-600">统一管理基础样式和动态样式配置</div>
            </div>
            <StylePanel />
          </div>
        </Suspense>
      </PanelErrorBoundary>
    ),



  };

  return (
    <ControlPanelContainer
      activePanel={activePanel}
      setActivePanel={setActivePanel}
      panelConfigs={panelConfigs}
      getTabStyle={getTabStyle}
    >
      {panelChildren}
    </ControlPanelContainer>
  );
});

ControlPanelProvider.displayName = 'ControlPanelProvider';

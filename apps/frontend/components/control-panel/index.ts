/**
 * 控制面板组件统一导出
 * 🎯 核心价值：提供通用的面板容器和业务面板管理
 * ⚡ 性能优化：动态加载、错误边界、memo优化
 * 🔧 架构：纯容器组件 + 业务面板提供者
 */

// 主要组件导出
export { ControlPanelContainer } from './ControlPanelContainer';
export { ControlPanelProvider } from './ControlPanelProvider';




// 类型导出
export type {
  ControlPanelContainerProps,
  ControlPanelProviderProps,
  PanelConfig,
  VersionPanelProps,
  ColorSystemType,
  ColorSystemPanelProps,
  ColorLevelToggleProps,
  ColorGroupSelectorProps,
} from './types';

// 默认导出（向后兼容）
export { ControlPanelProvider as default } from './ControlPanelProvider';
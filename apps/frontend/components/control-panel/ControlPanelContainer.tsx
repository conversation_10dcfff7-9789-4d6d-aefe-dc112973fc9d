import React, { memo } from 'react';
import { ControlPanelContainerProps } from './types';

/**
 * 控制面板容器组件 - 纯容器组件
 * 🎯 核心价值：提供通用的面板容器和导航功能
 * ⚡ 性能优化：使用memo包装提升性能
 * 🔧 架构：纯容器组件，不依赖具体业务面板
 * 📦 职责：面板导航、布局管理、内容渲染
 */
export const ControlPanelContainer = memo<ControlPanelContainerProps>((props) => {
  const {
    activePanel,
    setActivePanel,
    panelConfigs,
    children,
    getTabStyle
  } = props;

  return (
    <div
      className="w-80 bg-white flex flex-col overflow-hidden border-l border-gray-300 pl-2"
      role="region"
      aria-label="控制面板"
    >
      {/* 现代卡片式菜单栏 */}
      <div className="space-y-1 px-1 py-2">
        {/* 按类别分组显示面板 */}
        {['main', 'secondary', 'temp'].map((category) => {
          const categoryPanels = panelConfigs.filter(panel =>
            (panel.category || 'main') === category
          );

          if (categoryPanels.length === 0) return null;

          const categoryLabels = {
            main: '主面板',
            secondary: '辅助面板',
            temp: '临时面板'
          };

          return (
            <div key={category} className="border-b border-gray-200 pb-2">
              <div className="text-xs font-medium text-gray-500 mb-1 px-1">
                {categoryLabels[category as keyof typeof categoryLabels]}
              </div>
              <div className={`grid gap-1 ${
                categoryPanels.length <= 3 ? 'grid-cols-3' : 'grid-cols-2'
              }`}>
                {categoryPanels.map((panel) => (
                  <button
                    key={panel.key}
                    onClick={() => setActivePanel?.(panel.key)}
                    onKeyDown={(e) => {
                      if (e.key === 'Enter' || e.key === ' ') {
                        e.preventDefault();
                        setActivePanel?.(panel.key);
                      }
                    }}
                    role="tab"
                    aria-selected={activePanel === panel.key}
                    tabIndex={0}
                    className={
                      getTabStyle
                        ? getTabStyle(panel.key, activePanel === panel.key)
                        : `px-2 py-2 text-xs font-medium rounded-md transition-all duration-300 ${
                            panel.category === 'temp'
                              ? activePanel === panel.key
                                ? 'bg-green-100 text-green-700 border border-green-200'
                                : 'bg-green-50 text-green-600 border border-green-200 hover:bg-green-100'
                              : activePanel === panel.key
                                ? 'bg-blue-100 text-blue-700 border border-blue-200'
                                : 'bg-gray-50 text-gray-600 border border-gray-200 hover:bg-gray-100'
                          }`
                    }
                  >
                    <div className="text-center">
                      {panel.icon && <div className="text-sm mb-1">{panel.icon}</div>}
                      <div className="text-xs">{panel.label}</div>
                    </div>
                  </button>
                ))}
              </div>
            </div>
          );
        })}
      </div>

      {/* 控制面板内容 */}
      <div className="flex-1 p-3 overflow-y-auto scrollbar-thin bg-white" role="tabpanel">
        {children[activePanel] || (
          <div className="text-center text-gray-500 mt-8">
            <div className="text-sm">未找到对应的面板内容</div>
            <div className="text-xs mt-1">请检查面板配置</div>
          </div>
        )}
      </div>
    </div>
  );
});

ControlPanelContainer.displayName = 'ControlPanelContainer'; 

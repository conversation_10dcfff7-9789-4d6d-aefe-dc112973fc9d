/**
 * Grid System Component Types
 *
 * 网格系统组件的类型定义
 * 🎯 核心价值：为网格系统组件提供类型安全
 * 📦 类型范围：组件props、配置、状态等
 */

import type { GridUIConfig } from '@/lib/types/grid';
import {
  type GridDisplayMode as BaseDisplayMode,
  type CellShape,
  DEFAULT_UI_GRID_CONFIG
} from '@/stores/constants/grid';

// 重新导出基础显示模式类型（避免重复定义）
export type { BaseDisplayMode };

// 网格配置（扩展基础UI配置）
export interface GridConfig extends Omit<GridUIConfig, 'displayMode'> {
  displayMode: BaseDisplayMode;
  grayModeEnabled?: boolean;
}

// 遗留配置类型（用于迁移）
export interface LegacyGridConfig {
  displayMode?: string;
  grayMode?: boolean;
  fontSize?: number;
  matrixMargin?: number;
  gridColor?: string;
  cellShape?: CellShape;
}

// 默认网格配置（基于统一的UI配置）
export const DEFAULT_GRID_CONFIG: GridConfig = {
  ...DEFAULT_UI_GRID_CONFIG,
  displayMode: 'coordinates' as BaseDisplayMode,
  grayModeEnabled: false,
};

// 配置迁移函数
export function migrateDisplayModeConfig(legacyConfig: Partial<LegacyGridConfig>): GridConfig {
  const config: GridConfig = { ...DEFAULT_GRID_CONFIG };

  // 迁移显示模式
  if (legacyConfig.displayMode) {
    switch (legacyConfig.displayMode) {
      case 'gray':
        config.displayMode = 'coordinates';
        config.grayModeEnabled = true;
        break;
      case 'coordinates':
      case 'value':
      case 'color':
        config.displayMode = legacyConfig.displayMode as BaseDisplayMode;
        break;
      default:
        config.displayMode = 'coordinates';
    }
  }

  // 迁移其他配置（使用统一的属性名）
  if (legacyConfig.fontSize !== undefined) {
    config.fontSize = legacyConfig.fontSize;
  }

  if (legacyConfig.matrixMargin !== undefined) {
    config.matrixMargin = legacyConfig.matrixMargin;
  }

  if (legacyConfig.gridColor !== undefined) {
    config.gridColor = legacyConfig.gridColor;
  }

  if (legacyConfig.cellShape !== undefined) {
    config.cellShape = legacyConfig.cellShape;
  }

  if (legacyConfig.grayMode !== undefined) {
    config.grayModeEnabled = legacyConfig.grayMode;
  }

  return config;
}

// 验证配置函数
export function validateGridConfig(config: Partial<GridConfig>): GridConfig {
  const validatedConfig: GridConfig = { ...DEFAULT_GRID_CONFIG, ...config };
  
  // 验证字体大小
  if (validatedConfig.fontSize < 8 || validatedConfig.fontSize > 24) {
    validatedConfig.fontSize = DEFAULT_GRID_CONFIG.fontSize;
  }
  
  // 验证边距
  if (validatedConfig.matrixMargin < 0 || validatedConfig.matrixMargin > 50) {
    validatedConfig.matrixMargin = DEFAULT_GRID_CONFIG.matrixMargin;
  }
  
  // 验证显示模式
  const validDisplayModes: BaseDisplayMode[] = ['coordinates', 'value', 'color'];
  if (!validDisplayModes.includes(validatedConfig.displayMode)) {
    validatedConfig.displayMode = DEFAULT_GRID_CONFIG.displayMode;
  }
  
  // 验证单元格形状
  const validCellShapes = ['rounded', 'circle', 'square'];
  if (!validCellShapes.includes(validatedConfig.cellShape)) {
    validatedConfig.cellShape = DEFAULT_GRID_CONFIG.cellShape;
  }
  
  return validatedConfig;
}

// 导出类型
export type {
  GridUIConfig,
} from '@/lib/types/grid';

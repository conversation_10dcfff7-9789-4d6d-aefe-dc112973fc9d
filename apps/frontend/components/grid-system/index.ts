/**
 * Grid System Components - 纯UI版本
 * 
 * 导出纯UI网格组件，不包含业务逻辑
 * 🎯 核心价值：提供高性能的网格UI组件
 * 📦 功能范围：UI渲染、事件传递、样式控制
 * 🔄 重构说明：业务逻辑已迁移至 @/features/grid-system
 */

// 核心UI组件
export { GridMatrix } from './GridMatrix';
export { GridCell } from './GridCell';
export { GridErrorBoundary } from './GridErrorBoundary';
export { GridLoadingState } from './GridLoadingState';

// UI Hooks
export { useGridData, useGridAnimation } from './hooks';

// 类型导出
export type { GridMatrixProps, UIGridConfig } from './GridMatrix/types';
export type { GridCellProps } from './GridCell/types';
export type { UseGridDataReturn, UseGridAnimationReturn } from './hooks';

// 组件类型导出
export type {
  BaseDisplayMode,
  GridConfig,
  LegacyGridConfig,
  GridUIConfig
} from './types';

// 配置工具函数导出
export {
  DEFAULT_GRID_CONFIG,
  migrateDisplayModeConfig,
  validateGridConfig
} from './types';

// 向后兼容导出（指向features中的业务逻辑）
export {
  // 业务逻辑组件从features导出
  GridContainer,
  GridControlPanel,
  GridOverlay,
} from '@/features/grid-system';

export type {
  // 业务逻辑类型从features导出
  GridContainerProps,
  GridControlPanelProps,
  GridOverlayProps,
  GridBusinessConfig,
} from '@/features/grid-system';

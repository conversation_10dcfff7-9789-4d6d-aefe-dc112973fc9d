/**
 * useGridAnimation Hook - 纯UI版本
 * 
 * 为纯UI组件提供动画效果的简化hook
 * 🎯 核心价值：提供UI动画和视觉效果，不包含业务逻辑
 * 📦 功能范围：动画状态、CSS变量、缩放效果
 */

import { useState, useCallback, useMemo } from 'react';
import type { UIGridConfig } from '../GridMatrix/types';

export interface UseGridAnimationReturn {
  animationClass: string;
  isAnimating: boolean;
  triggerAnimation: (type: 'scale' | 'fade' | 'slide') => void;
  animationStyle: React.CSSProperties;
  getScaleStyle: (isHovered: boolean) => React.CSSProperties;
  cssVariables: React.CSSProperties;
}

export const useGridAnimation = (config: UIGridConfig): UseGridAnimationReturn => {
  const [isAnimating, setIsAnimating] = useState(false);
  const [animationType, setAnimationType] = useState<'scale' | 'fade' | 'slide' | null>(null);

  // 触发动画
  const triggerAnimation = useCallback((type: 'scale' | 'fade' | 'slide') => {
    if (!config.animation.enabled) return;

    setAnimationType(type);
    setIsAnimating(true);

    setTimeout(() => {
      setIsAnimating(false);
      setAnimationType(null);
    }, config.animation.duration);
  }, [config.animation.enabled, config.animation.duration]);

  // 动画CSS类
  const animationClass = useMemo(() => {
    if (!isAnimating || !animationType) return '';
    
    return `grid-animation-${animationType}`;
  }, [isAnimating, animationType]);

  // 动画样式
  const animationStyle = useMemo((): React.CSSProperties => {
    if (!config.animation.enabled) return {};

    return {
      transition: `all ${config.animation.duration}ms ease-in-out`,
    };
  }, [config.animation.enabled, config.animation.duration]);

  // 获取缩放样式
  const getScaleStyle = useCallback((isHovered: boolean): React.CSSProperties => {
    if (!config.scale.enabled || !isHovered) return {};

    return {
      transform: `scale(${config.scale.factor})`,
      zIndex: 10,
    };
  }, [config.scale.enabled, config.scale.factor]);

  // CSS变量
  const cssVariables = useMemo((): React.CSSProperties => ({
    ['--animation-duration' as any]: `${config.animation.duration}ms`,
    ['--scale-factor' as any]: config.scale.factor.toString(),
    ['--cell-size' as any]: `${config.size}px`,
    ['--cell-gap' as any]: `${config.gap}px`,
    ['--cell-padding' as any]: `${config.padding}px`,
    ['--font-size' as any]: `${config.fontSize}px`,
  }), [config]);

  return {
    animationClass,
    isAnimating,
    triggerAnimation,
    animationStyle,
    getScaleStyle,
    cssVariables,
  };
};

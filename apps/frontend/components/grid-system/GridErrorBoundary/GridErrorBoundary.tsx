/**
 * GridErrorBoundary Component - 纯UI版本
 * 
 * 简化的错误边界组件，专注于UI错误处理
 */

'use client';

import React, { Component, ErrorInfo, ReactNode } from 'react';

interface GridErrorBoundaryProps {
  children: ReactNode;
  fallback?: ReactNode | ((error: Error | null, resetError: () => void) => ReactNode);
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
}

interface GridErrorBoundaryState {
  hasError: boolean;
  error: Error | null;
}

export class GridErrorBoundary extends Component<GridErrorBoundaryProps, GridErrorBoundaryState> {
  constructor(props: GridErrorBoundaryProps) {
    super(props);
    this.state = { hasError: false, error: null };
  }

  static getDerivedStateFromError(error: Error): GridErrorBoundaryState {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    this.props.onError?.(error, errorInfo);
  }

  resetError = () => {
    this.setState({ hasError: false, error: null });
  };

  render() {
    if (this.state.hasError) {
      if (this.props.fallback) {
        if (typeof this.props.fallback === 'function') {
          return this.props.fallback(this.state.error, this.resetError);
        }
        return this.props.fallback;
      }

      return (
        <div className="grid-error-boundary p-4 border border-red-300 bg-red-50 rounded-md">
          <h3 className="text-red-700 font-medium mb-2">网格组件错误</h3>
          <p className="text-red-600 mb-3">网格渲染时发生错误，请尝试刷新页面。</p>
          <button
            onClick={this.resetError}
            className="px-3 py-1 bg-red-100 text-red-700 rounded hover:bg-red-200"
          >
            重试
          </button>
        </div>
      );
    }

    return this.props.children;
  }
}

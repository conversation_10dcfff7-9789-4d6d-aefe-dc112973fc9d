/**
 * GridMatrix Component Styles
 * 
 * 纯UI网格矩阵组件的样式定义
 * 🎯 核心价值：提供网格矩阵的视觉样式和动画效果
 * 📦 样式范围：布局、动画、状态、响应式
 */

/* 网格矩阵容器 */
.gridMatrix {
  position: relative;
  display: grid;
  background-color: var(--grid-background, #ffffff);
  border-radius: var(--grid-border-radius, 8px);
  box-shadow: var(--grid-shadow, 0 2px 8px rgba(0, 0, 0, 0.1));
  overflow: hidden;
  user-select: none;
  
  /* 焦点样式 */
  &:focus {
    outline: 2px solid var(--grid-focus-color, #3b82f6);
    outline-offset: 2px;
  }
  
  /* 高对比度模式 */
  @media (prefers-contrast: high) {
    border: 2px solid var(--grid-border-color, #000000);
    box-shadow: none;
  }
  
  /* 减少动画模式 */
  @media (prefers-reduced-motion: reduce) {
    transition: none;
    
    .cell {
      transition: none;
    }
  }
}

/* 动画状态 */
.animating {
  transition: all var(--animation-duration, 200ms) ease-in-out;
}

/* 单元格基础样式 */
.cell {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all var(--animation-duration, 200ms) ease-in-out;
  border: 1px solid var(--cell-border-color, transparent);
  
  /* 文字样式 */
  font-family: var(--grid-font-family, 'Inter', sans-serif);
  font-weight: var(--cell-font-weight, 500);
  line-height: 1;
  text-align: center;
  color: var(--cell-text-color, #374151);
  
  /* 防止文字选择 */
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

/* 单元格形状变体 */
.cellSquare {
  border-radius: var(--cell-border-radius-square, 2px);
}

.cellRounded {
  border-radius: var(--cell-border-radius-rounded, 6px);
}

/* 单元格状态样式 */
.cellActive {
  opacity: 1;
  transform: scale(var(--cell-scale-active, 1));
}

.cellActive:not(.cellHovered) {
  box-shadow: var(--cell-shadow-active, 0 1px 3px rgba(0, 0, 0, 0.1));
}

.cellHovered {
  transform: scale(var(--cell-scale-hover, 1.1));
  z-index: 10;
  box-shadow: var(--cell-shadow-hover, 0 4px 12px rgba(0, 0, 0, 0.15));
  border-color: var(--cell-border-color-hover, #3b82f6);
}

.cellFocused {
  outline: 2px solid var(--cell-focus-color, #3b82f6);
  outline-offset: 1px;
  z-index: 5;
}

/* 非激活单元格 */
.cell:not(.cellActive) {
  opacity: var(--cell-opacity-inactive, 0.3);
  cursor: default;
  
  &:hover {
    transform: none;
    box-shadow: none;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .gridMatrix {
    padding: var(--grid-padding-mobile, 8px);
    gap: var(--grid-gap-mobile, 1px);
  }
  
  .cell {
    font-size: var(--cell-font-size-mobile, 10px);
  }
}

@media (max-width: 480px) {
  .gridMatrix {
    padding: var(--grid-padding-small, 4px);
    gap: var(--grid-gap-small, 0px);
  }
  
  .cell {
    font-size: var(--cell-font-size-small, 8px);
  }
}

/* 打印样式 */
@media print {
  .gridMatrix {
    box-shadow: none;
    border: 1px solid #000;
  }
  
  .cell {
    transition: none;
    box-shadow: none;
  }
  
  .cellHovered,
  .cellFocused {
    transform: none;
    outline: none;
    box-shadow: none;
  }
}

/* 深色主题支持 */
@media (prefers-color-scheme: dark) {
  .gridMatrix {
    --grid-background: #1f2937;
    --grid-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
    --cell-text-color: #f3f4f6;
    --cell-border-color-hover: #60a5fa;
    --cell-focus-color: #60a5fa;
  }
}

/* CSS变量已移动到全局样式文件 */

/**
 * GridMatrix Component Types
 *
 * 纯UI网格矩阵组件的类型定义
 * 🎯 核心价值：为纯UI组件提供类型安全
 * 📦 类型范围：仅UI相关的props和配置
 */

import type { CellData, GridUIConfig } from '@/lib/types/grid';
import type { GridDisplayMode, CellShape } from '@/stores/constants/grid';

// 纯UI网格配置（基于基础配置，但使用业务显示模式）
export interface UIGridConfig extends Required<Omit<GridUIConfig, 'displayMode'>> {
  displayMode: GridDisplayMode; // 使用统一的显示模式类型
}

// 默认UI配置（专门为GridMatrix组件）
export const DEFAULT_MATRIX_UI_CONFIG: UIGridConfig = {
  fontSize: 12,
  matrixMargin: 16,
  gridColor: '#e5e7eb',
  cellShape: 'square' as CellShape,
  displayMode: 'coordinates' as GridDisplayMode,
  size: 24,
  gap: 2,
  padding: 16,
  scale: {
    enabled: true,
    factor: 1.1,
  },
  animation: {
    enabled: true,
    duration: 200,
  },
};

// GridMatrix组件Props
export interface GridMatrixProps {
  // 数据props（由外部提供）
  cells?: CellData[][];            // 单元格数据（可选，用于外部数据注入）

  // 配置props
  config?: Partial<UIGridConfig>;  // UI配置
  
  // 样式props
  className?: string;              // 自定义CSS类
  style?: React.CSSProperties;     // 内联样式
  
  // 事件处理props
  onCellClick?: (cell: CellData, event: React.MouseEvent) => void;
  onCellHover?: (cell: CellData | null) => void;
  onCellFocus?: (cell: CellData) => void;
  onCellDoubleClick?: (cell: CellData, event: React.MouseEvent) => void;
  
  // 渲染控制props
  getCellContent?: (cell: CellData, displayMode: string) => string | null;
  getCellStyle?: (cell: CellData) => React.CSSProperties;
  getCellClassName?: (cell: CellData) => string;
  
  // 可访问性props
  ariaLabel?: string;
  ariaDescription?: string;
  
  // 性能优化props
  enableVirtualization?: boolean;  // 启用虚拟化（暂未实现）
  renderBatchSize?: number;        // 渲染批次大小
}

// 默认UI配置（从统一常量导入）
export { DEFAULT_UI_GRID_CONFIG } from '@/stores/constants/grid';

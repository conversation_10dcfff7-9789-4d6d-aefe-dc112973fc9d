/**
 * GridCell Component - 纯UI版本
 * 
 * 纯UI网格单元格组件，专注于渲染和视觉呈现
 * 🎯 核心价值：提供高性能的单元格UI渲染，不包含业务逻辑
 * 📦 功能范围：UI渲染、事件传递、样式控制、可访问性
 */

'use client';

import React, { memo, useCallback, useMemo } from 'react';
import { cn } from '@/lib/utils/cn';
import { useGridAnimation } from '../hooks/useGridAnimation';
import type { GridCellProps } from './types';
import styles from './GridCell.module.css';

/**
 * GridCell - 纯UI网格单元格组件
 * 专注于单元格的视觉渲染和用户交互
 */
export const GridCell = memo<GridCellProps>(({
  cell,
  config,
  isActive,
  onClick,
  onDoubleClick,
  onHover,
  onFocus,
  isFocused = false,
  id,
  getCellContent,
  getCellStyle,
  getCellClassName,
}) => {
  // 动画hook
  const { getScaleStyle, cssVariables } = useGridAnimation(config);

  // 处理点击事件
  const handleClick = useCallback((event: React.MouseEvent) => {
    onClick?.(cell, event);
  }, [onClick, cell]);

  // 处理双击事件
  const handleDoubleClick = useCallback((event: React.MouseEvent) => {
    onDoubleClick?.(cell, event);
  }, [onDoubleClick, cell]);

  // 处理鼠标进入
  const handleMouseEnter = useCallback(() => {
    onHover?.(cell);
  }, [onHover, cell]);

  // 处理鼠标离开
  const handleMouseLeave = useCallback(() => {
    onHover?.(null);
  }, [onHover]);

  // 处理焦点
  const handleFocus = useCallback(() => {
    onFocus?.(cell);
  }, [onFocus, cell]);

  // 获取单元格内容
  const cellContent = useMemo(() => {
    if (getCellContent) {
      return getCellContent(cell, config.displayMode);
    }
    return null;
  }, [getCellContent, cell, config.displayMode]);

  // 获取单元格样式
  const cellStyle = useMemo((): React.CSSProperties => {
    const baseStyle: React.CSSProperties = {
      width: `${config.size}px`,
      height: `${config.size}px`,
      fontSize: `${config.fontSize}px`,
      backgroundColor: cell.color,
      ...cssVariables,
    };

    // 应用外部样式
    if (getCellStyle) {
      Object.assign(baseStyle, getCellStyle(cell));
    }

    return baseStyle;
  }, [config.size, config.fontSize, cell.color, cssVariables, getCellStyle, cell]);

  // 获取单元格CSS类名
  const cellClassName = useMemo(() => {
    const baseClasses = [
      styles.cell,
      config.cellShape === 'rounded' ? styles.cellRounded : styles.cellSquare,
    ];

    // 状态类名
    if (isActive) baseClasses.push(styles.cellActive);
    if (isFocused) baseClasses.push(styles.cellFocused);

    // 应用外部类名
    if (getCellClassName) {
      baseClasses.push(getCellClassName(cell));
    }

    return cn(...baseClasses);
  }, [
    config.cellShape,
    isActive,
    isFocused,
    getCellClassName,
    cell,
  ]);

  return (
    <div
      id={id}
      className={cellClassName}
      style={cellStyle}
      onClick={handleClick}
      onDoubleClick={handleDoubleClick}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      onFocus={handleFocus}
      role="gridcell"
      tabIndex={isActive ? 0 : -1}
      aria-label={`单元格 (${cell.x}, ${cell.y})`}
      aria-selected={isFocused}
      aria-disabled={!isActive}
      data-x={cell.x}
      data-y={cell.y}
      data-active={isActive}
      data-focused={isFocused}
      data-testid={`grid-cell-${cell.x}-${cell.y}`}
    >
      {cellContent && (
        <span className={styles.cellContent}>
          {cellContent}
        </span>
      )}
    </div>
  );
});

GridCell.displayName = 'GridCell';

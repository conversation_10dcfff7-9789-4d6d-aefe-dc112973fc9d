/**
 * GridCell Component Styles
 * 
 * 纯UI网格单元格组件的样式定义
 */

/* 单元格基础样式 */
.cell {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all var(--animation-duration, 200ms) ease-in-out;
  border: 1px solid var(--cell-border-color, transparent);
  
  /* 文字样式 */
  font-family: var(--grid-font-family, 'Inter', sans-serif);
  font-weight: var(--cell-font-weight, 500);
  line-height: 1;
  text-align: center;
  color: var(--cell-text-color, #374151);
  
  /* 防止文字选择 */
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

/* 单元格形状变体 */
.cellSquare {
  border-radius: var(--cell-border-radius-square, 2px);
}

.cellRounded {
  border-radius: var(--cell-border-radius-rounded, 6px);
}

/* 单元格状态样式 */
.cellActive {
  opacity: 1;
  transform: scale(var(--cell-scale-active, 1));
}

.cellActive:hover {
  transform: scale(var(--cell-scale-hover, 1.1));
  z-index: 10;
  box-shadow: var(--cell-shadow-hover, 0 4px 12px rgba(0, 0, 0, 0.15));
  border-color: var(--cell-border-color-hover, #3b82f6);
}

.cellFocused {
  outline: 2px solid var(--cell-focus-color, #3b82f6);
  outline-offset: 1px;
  z-index: 5;
}

/* 非激活单元格 */
.cell:not(.cellActive) {
  opacity: var(--cell-opacity-inactive, 0.3);
  cursor: default;
}

.cell:not(.cellActive):hover {
  transform: none;
  box-shadow: none;
}

/* 单元格内容 */
.cellContent {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  font-size: inherit;
  font-weight: inherit;
  color: inherit;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .cell {
    font-size: var(--cell-font-size-mobile, 10px);
  }
}

@media (max-width: 480px) {
  .cell {
    font-size: var(--cell-font-size-small, 8px);
  }
}

/* 减少动画模式 */
@media (prefers-reduced-motion: reduce) {
  .cell {
    transition: none;
  }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
  .cell {
    border: 1px solid var(--cell-border-color, #000000);
  }
}

/* 深色主题支持 */
@media (prefers-color-scheme: dark) {
  .cell {
    --cell-text-color: #f3f4f6;
    --cell-border-color-hover: #60a5fa;
    --cell-focus-color: #60a5fa;
  }
}

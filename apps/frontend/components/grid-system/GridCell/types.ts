/**
 * GridCell Component Types
 * 
 * 纯UI网格单元格组件的类型定义
 */

import type { CellData } from '@/lib/types/grid';
import type { UIGridConfig } from '../GridMatrix/types';

// GridCell组件Props
export interface GridCellProps {
  // 数据props
  cell: CellData;
  config: UIGridConfig;
  isActive: boolean;
  
  // 事件处理props
  onClick?: (cell: CellData, event: React.MouseEvent) => void;
  onDoubleClick?: (cell: CellData, event: React.MouseEvent) => void;
  onHover?: (cell: CellData | null) => void;
  onFocus?: (cell: CellData) => void;
  
  // 状态props
  isFocused?: boolean;
  id?: string;
  
  // 渲染控制props
  getCellContent?: (cell: CellData, displayMode: string) => string | null;
  getCellStyle?: (cell: CellData) => React.CSSProperties;
  getCellClassName?: (cell: CellData) => string;
}

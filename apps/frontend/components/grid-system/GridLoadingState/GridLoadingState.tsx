/**
 * GridLoadingState Component - 纯UI版本
 * 
 * 简化的加载状态组件，专注于UI状态展示
 */

'use client';

import React, { memo } from 'react';

interface GridLoadingStateProps {
  isLoading?: boolean;
  error?: Error | null;
  isEmpty?: boolean;
  loadingMessage?: string;
  errorMessage?: string;
  emptyMessage?: string;
  onRetry?: () => void;
  className?: string;
}

export const GridLoadingState = memo<GridLoadingStateProps>(({
  isLoading = false,
  error = null,
  isEmpty = false,
  loadingMessage = '加载中...',
  errorMessage = '加载失败',
  emptyMessage = '暂无数据',
  onRetry,
  className = '',
}) => {
  if (isLoading) {
    return (
      <div className={`grid-loading-state flex items-center justify-center p-8 ${className}`}>
        <div className="flex flex-col items-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mb-3"></div>
          <div className="text-gray-600">{loadingMessage}</div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`grid-error-state flex items-center justify-center p-8 ${className}`}>
        <div className="flex flex-col items-center text-center">
          <div className="text-red-500 mb-2">⚠️</div>
          <div className="text-red-600 mb-3">{errorMessage}</div>
          {onRetry && (
            <button
              onClick={onRetry}
              className="px-4 py-2 bg-red-100 text-red-700 rounded hover:bg-red-200"
            >
              重试
            </button>
          )}
        </div>
      </div>
    );
  }

  if (isEmpty) {
    return (
      <div className={`grid-empty-state flex items-center justify-center p-8 ${className}`}>
        <div className="flex flex-col items-center text-center">
          <div className="text-gray-400 mb-2">📊</div>
          <div className="text-gray-500">{emptyMessage}</div>
        </div>
      </div>
    );
  }

  return null;
});

GridLoadingState.displayName = 'GridLoadingState';

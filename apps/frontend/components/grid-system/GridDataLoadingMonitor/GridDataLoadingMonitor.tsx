/**
 * 网格数据加载监控组件
 * 🎯 核心价值：实时监控数据加载性能，提供详细的加载状态和性能指标
 * 📦 功能范围：加载进度显示、性能指标监控、错误状态处理、缓存状态显示
 * ⚡ 性能优化：轻量级监控，不影响主要渲染性能
 */

'use client';

import React, { useState, useEffect, memo } from 'react';
import { cn } from '@/lib/utils/cn';
import { globalDataInitializer } from '@/lib/data/OptimizedDataInitializer';
import type { InitializationProgress, InitializationMetrics } from '@/lib/data/OptimizedDataInitializer';

export interface GridDataLoadingMonitorProps {
  className?: string;
  showDetailedMetrics?: boolean;
  showProgressBar?: boolean;
  showCacheStats?: boolean;
  onLoadingComplete?: (metrics: InitializationMetrics) => void;
}

/**
 * 网格数据加载监控组件
 */
export const GridDataLoadingMonitor = memo<GridDataLoadingMonitorProps>(({
  className,
  showDetailedMetrics = false,
  showProgressBar = true,
  showCacheStats = false,
  onLoadingComplete
}) => {
  const [progress, setProgress] = useState<InitializationProgress | null>(null);
  const [metrics, setMetrics] = useState<InitializationMetrics | null>(null);
  const [cacheStats, setCacheStats] = useState<any>(null);
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    // 订阅进度更新
    const unsubscribe = globalDataInitializer.onProgress((progressData) => {
      setProgress(progressData);
      setIsVisible(true);

      // 如果完成，延迟隐藏
      if (progressData.phase === 'complete') {
        const finalMetrics = globalDataInitializer.getMetrics();
        setMetrics(finalMetrics);
        onLoadingComplete?.(finalMetrics);

        setTimeout(() => {
          setIsVisible(false);
        }, 2000);
      }

      // 如果出错，保持显示
      if (progressData.phase === 'error') {
        setTimeout(() => {
          setIsVisible(false);
        }, 5000);
      }
    });

    // 获取初始指标
    setMetrics(globalDataInitializer.getMetrics());
    
    if (showCacheStats) {
      setCacheStats(globalDataInitializer.getCacheStats());
    }

    return unsubscribe;
  }, [onLoadingComplete, showCacheStats]);

  // 如果不可见且没有进度，不渲染
  if (!isVisible && !progress) {
    return null;
  }

  const getPhaseIcon = (phase: string) => {
    switch (phase) {
      case 'starting': return '🚀';
      case 'matrix-data': return '📊';
      case 'grid-data': return '🔲';
      case 'validation': return '✅';
      case 'caching': return '💾';
      case 'complete': return '🎉';
      case 'error': return '❌';
      default: return '⏳';
    }
  };

  const getPhaseColor = (phase: string) => {
    switch (phase) {
      case 'complete': return 'text-green-600';
      case 'error': return 'text-red-600';
      case 'caching': return 'text-blue-600';
      case 'validation': return 'text-yellow-600';
      default: return 'text-gray-600';
    }
  };

  return (
    <div className={cn(
      'fixed top-4 right-4 z-50 bg-white border border-gray-200 rounded-lg shadow-lg p-4 min-w-80',
      'transition-all duration-300 ease-in-out',
      isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 -translate-y-2',
      className
    )}>
      {/* 标题 */}
      <div className="flex items-center gap-2 mb-3">
        <span className="text-lg">📈</span>
        <h3 className="font-semibold text-gray-800">数据加载监控</h3>
      </div>

      {/* 当前进度 */}
      {progress && (
        <div className="mb-4">
          <div className="flex items-center gap-2 mb-2">
            <span className="text-xl">{getPhaseIcon(progress.phase)}</span>
            <span className={cn('font-medium', getPhaseColor(progress.phase))}>
              {progress.message}
            </span>
          </div>

          {/* 进度条 */}
          {showProgressBar && progress.phase !== 'complete' && progress.phase !== 'error' && (
            <div className="w-full bg-gray-200 rounded-full h-2 mb-2">
              <div 
                className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                style={{ width: `${progress.progress}%` }}
              />
            </div>
          )}

          {/* 当前步骤和预估时间 */}
          <div className="flex justify-between text-sm text-gray-600">
            <span>步骤: {progress.currentStep}</span>
            {progress.estimatedTimeRemaining > 0 && (
              <span>预计剩余: {(progress.estimatedTimeRemaining / 1000).toFixed(1)}s</span>
            )}
          </div>
        </div>
      )}

      {/* 详细指标 */}
      {showDetailedMetrics && metrics && (
        <div className="border-t pt-3 mt-3">
          <h4 className="font-medium text-gray-700 mb-2">性能指标</h4>
          <div className="grid grid-cols-2 gap-2 text-sm">
            <div>
              <span className="text-gray-600">总初始化次数:</span>
              <span className="ml-1 font-medium">{metrics.totalInitializations}</span>
            </div>
            <div>
              <span className="text-gray-600">成功率:</span>
              <span className="ml-1 font-medium text-green-600">
                {(metrics.successfulInitializations / Math.max(metrics.totalInitializations, 1) * 100).toFixed(1)}%
              </span>
            </div>
            <div>
              <span className="text-gray-600">平均耗时:</span>
              <span className="ml-1 font-medium">{metrics.averageInitializationTime.toFixed(1)}ms</span>
            </div>
            <div>
              <span className="text-gray-600">缓存命中率:</span>
              <span className="ml-1 font-medium text-blue-600">
                {(metrics.cacheHitRate * 100).toFixed(1)}%
              </span>
            </div>
            <div>
              <span className="text-gray-600">错误率:</span>
              <span className={cn(
                'ml-1 font-medium',
                metrics.errorRate > 0.1 ? 'text-red-600' : 'text-green-600'
              )}>
                {(metrics.errorRate * 100).toFixed(1)}%
              </span>
            </div>
            <div>
              <span className="text-gray-600">最近耗时:</span>
              <span className="ml-1 font-medium">{metrics.lastInitializationTime.toFixed(1)}ms</span>
            </div>
          </div>
        </div>
      )}

      {/* 缓存统计 */}
      {showCacheStats && cacheStats && (
        <div className="border-t pt-3 mt-3">
          <h4 className="font-medium text-gray-700 mb-2">缓存状态</h4>
          <div className="grid grid-cols-2 gap-2 text-sm">
            <div>
              <span className="text-gray-600">缓存大小:</span>
              <span className="ml-1 font-medium">{cacheStats.size || 0}</span>
            </div>
            <div>
              <span className="text-gray-600">命中次数:</span>
              <span className="ml-1 font-medium text-green-600">{cacheStats.hits || 0}</span>
            </div>
            <div>
              <span className="text-gray-600">未命中:</span>
              <span className="ml-1 font-medium text-orange-600">{cacheStats.misses || 0}</span>
            </div>
            <div>
              <span className="text-gray-600">内存使用:</span>
              <span className="ml-1 font-medium">
                {cacheStats.memoryUsage ? `${(cacheStats.memoryUsage / 1024 / 1024).toFixed(1)}MB` : '0MB'}
              </span>
            </div>
          </div>
        </div>
      )}

      {/* 操作按钮 */}
      <div className="flex gap-2 mt-4 pt-3 border-t">
        <button
          onClick={() => setIsVisible(false)}
          className="px-3 py-1 text-sm bg-gray-100 hover:bg-gray-200 rounded transition-colors"
        >
          隐藏
        </button>
        {showCacheStats && (
          <button
            onClick={() => {
              globalDataInitializer.clearCache();
              setCacheStats(globalDataInitializer.getCacheStats());
            }}
            className="px-3 py-1 text-sm bg-red-100 hover:bg-red-200 text-red-700 rounded transition-colors"
          >
            清除缓存
          </button>
        )}
        <button
          onClick={() => {
            setMetrics(globalDataInitializer.getMetrics());
            if (showCacheStats) {
              setCacheStats(globalDataInitializer.getCacheStats());
            }
          }}
          className="px-3 py-1 text-sm bg-blue-100 hover:bg-blue-200 text-blue-700 rounded transition-colors"
        >
          刷新
        </button>
      </div>
    </div>
  );
});

GridDataLoadingMonitor.displayName = 'GridDataLoadingMonitor';

/**
 * 简化版加载监控组件（仅显示进度）
 */
export const SimpleGridDataLoadingMonitor = memo<Pick<GridDataLoadingMonitorProps, 'className' | 'onLoadingComplete'>>(({
  className,
  onLoadingComplete
}) => {
  return (
    <GridDataLoadingMonitor
      className={className}
      showDetailedMetrics={false}
      showProgressBar={true}
      showCacheStats={false}
      onLoadingComplete={onLoadingComplete}
    />
  );
});

SimpleGridDataLoadingMonitor.displayName = 'SimpleGridDataLoadingMonitor';

/**
 * 开发者版加载监控组件（显示所有信息）
 */
export const DeveloperGridDataLoadingMonitor = memo<Pick<GridDataLoadingMonitorProps, 'className' | 'onLoadingComplete'>>(({
  className,
  onLoadingComplete
}) => {
  return (
    <GridDataLoadingMonitor
      className={className}
      showDetailedMetrics={true}
      showProgressBar={true}
      showCacheStats={true}
      onLoadingComplete={onLoadingComplete}
    />
  );
});

DeveloperGridDataLoadingMonitor.displayName = 'DeveloperGridDataLoadingMonitor';

/**
 * styleStore - 样式配置状态管理
 * 🎯 核心价值：管理全局样式配置、主题设置、样式预设
 * 📦 功能范围：样式配置、主题切换、预设管理、样式持久化
 * 🔄 架构设计：基于zustand的状态管理，支持持久化
 */

import { create } from 'zustand';
import { persist } from 'zustand/middleware';

// 样式配置接口
export interface StyleConfig {
  // 字体配置
  fontSize: number;
  fontFamily: string;
  fontWeight: number;
  
  // 颜色配置
  primaryColor: string;
  secondaryColor: string;
  backgroundColor: string;
  textColor: string;
  borderColor: string;
  
  // 布局配置
  cellSize: number;
  cellGap: number;
  borderRadius: number;
  padding: number;
  margin: number;
  
  // 动画配置
  animationDuration: number;
  animationEasing: string;
  enableAnimations: boolean;
  
  // 其他配置
  showGrid: boolean;
  showBorders: boolean;
  enableShadows: boolean;
  opacity: number;
}

// 主题类型
export type ThemeType = 'light' | 'dark' | 'auto';

// 样式预设接口
export interface StylePreset {
  id: string;
  name: string;
  description?: string;
  config: StyleConfig;
  isDefault?: boolean;
  createdAt: number;
  updatedAt: number;
}

// Store状态接口
export interface StyleStoreState {
  // 当前样式配置
  config: StyleConfig;
  
  // 主题设置
  theme: ThemeType;
  
  // 样式预设
  presets: StylePreset[];
  currentPresetId: string | null;
  
  // UI状态
  isEditing: boolean;
  isDirty: boolean;
  
  // 操作方法
  updateConfig: (updates: Partial<StyleConfig>) => void;
  resetConfig: () => void;
  setTheme: (theme: ThemeType) => void;
  
  // 预设管理
  addPreset: (preset: Omit<StylePreset, 'id' | 'createdAt' | 'updatedAt'>) => void;
  updatePreset: (id: string, updates: Partial<StylePreset>) => void;
  deletePreset: (id: string) => void;
  loadPreset: (id: string) => void;
  
  // 编辑状态
  setEditing: (editing: boolean) => void;
  markDirty: () => void;
  markClean: () => void;
}

// 默认样式配置
const defaultStyleConfig: StyleConfig = {
  // 字体配置
  fontSize: 12,
  fontFamily: 'Inter, system-ui, sans-serif',
  fontWeight: 400,
  
  // 颜色配置
  primaryColor: '#3b82f6',
  secondaryColor: '#64748b',
  backgroundColor: '#ffffff',
  textColor: '#1f2937',
  borderColor: '#e5e7eb',
  
  // 布局配置
  cellSize: 24,
  cellGap: 1,
  borderRadius: 4,
  padding: 8,
  margin: 4,
  
  // 动画配置
  animationDuration: 200,
  animationEasing: 'ease-in-out',
  enableAnimations: true,
  
  // 其他配置
  showGrid: true,
  showBorders: true,
  enableShadows: false,
  opacity: 1.0,
};

// 默认预设
const defaultPresets: StylePreset[] = [
  {
    id: 'default',
    name: '默认样式',
    description: '系统默认的样式配置',
    config: defaultStyleConfig,
    isDefault: true,
    createdAt: Date.now(),
    updatedAt: Date.now(),
  },
];

/**
 * 样式配置Store
 */
export const useStyleStore = create<StyleStoreState>()(
  persist(
    (set, get) => ({
      // 初始状态
      config: defaultStyleConfig,
      theme: 'light',
      presets: defaultPresets,
      currentPresetId: 'default',
      isEditing: false,
      isDirty: false,
      
      // 配置更新
      updateConfig: (updates) => {
        set((state) => ({
          config: { ...state.config, ...updates },
          isDirty: true,
        }));
      },
      
      resetConfig: () => {
        set({
          config: defaultStyleConfig,
          isDirty: false,
        });
      },
      
      setTheme: (theme) => {
        set({ theme });
      },
      
      // 预设管理
      addPreset: (presetData) => {
        const preset: StylePreset = {
          ...presetData,
          id: `preset_${Date.now()}`,
          createdAt: Date.now(),
          updatedAt: Date.now(),
        };
        
        set((state) => ({
          presets: [...state.presets, preset],
        }));
      },
      
      updatePreset: (id, updates) => {
        set((state) => ({
          presets: state.presets.map(preset =>
            preset.id === id
              ? { ...preset, ...updates, updatedAt: Date.now() }
              : preset
          ),
        }));
      },
      
      deletePreset: (id) => {
        set((state) => ({
          presets: state.presets.filter(preset => preset.id !== id),
          currentPresetId: state.currentPresetId === id ? null : state.currentPresetId,
        }));
      },
      
      loadPreset: (id) => {
        const preset = get().presets.find(p => p.id === id);
        if (preset) {
          set({
            config: preset.config,
            currentPresetId: id,
            isDirty: false,
          });
        }
      },
      
      // 编辑状态
      setEditing: (editing) => {
        set({ isEditing: editing });
      },
      
      markDirty: () => {
        set({ isDirty: true });
      },
      
      markClean: () => {
        set({ isDirty: false });
      },
    }),
    {
      name: 'style-store',
      version: 1,
      partialize: (state) => ({
        config: state.config,
        theme: state.theme,
        presets: state.presets,
        currentPresetId: state.currentPresetId,
      }),
      migrate: (persistedState: any, version: number) => {
        // 处理状态迁移
        if (version === 0) {
          // 从版本0迁移到版本1，确保所有必需字段存在
          return {
            config: persistedState.config || defaultStyleConfig,
            theme: persistedState.theme || 'light',
            presets: persistedState.presets || defaultPresets,
            currentPresetId: persistedState.currentPresetId || 'default',
          };
        }
        return persistedState;
      },
    }
  )
);

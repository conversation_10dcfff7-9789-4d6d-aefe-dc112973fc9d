/**
 * Zustand状态管理Store统一导出
 * 🎯 核心价值：集中管理核心状态Store，简化导入和维护
 * ⚡ 性能优化：统一导出减少重复导入
 * 🔄 架构设计：基于zustand的状态管理，支持持久化
 */

// === 核心Store架构导出 ===

// 🎨 样式Store - 状态管理
export {
  useStyleStore,
  type StyleConfig,
  type ThemeType,
  type StylePreset,
  type StyleStoreState,
} from './styleStore';

// 🔧 动态样式Store - 动态样式、响应式、交互状态
export {
  useDynamicStyleStore,
  type DynamicStyleConfig,
  type DeviceInfo,
  type InteractionState,
  type DynamicStyleStoreState,
} from './dynamicStyleStore';

// 📊 基础数据Store - 颜色坐标数据管理
export {
  useBasicDataStore,
  useMatrixData,
  useColorValues,
  useColorVisibility,
  useGroupVisibility,
  useBlackCellData,
  useDataPointsAt,
  useGroupData,
  useColorData,
  useLevelData,
  useGridData,
  useCellAt,
  // 重新导出类型
  type BasicColorType,
  type GroupType,
  type ColorLevel,
  type ColorValue,
  type ColorVisibility,
  type BlackCellData,
  type MatrixDataPoint,
  type MatrixData,
  type CellData,
} from './basicDataStore';

// 从新位置导出常量和工具函数
export {
  DEFAULT_COLOR_VALUES,
  AVAILABLE_LEVELS as COLOR_LEVELS,
  SPECIAL_COORDINATES,
  GROUP_A_DATA,
  GROUP_OFFSET_CONFIGS,
  GRID_DIMENSIONS,
  GRID_CENTER,
} from '@/stores/constants/matrix';

export {
  getAllColorTypes,
  getAllGroupTypes,
  generateDefaultColorVisibility as DEFAULT_COLOR_VISIBILITY,
} from '@/lib/utils/matrixHelpers';

// 🎛️ 网格配置Store - 显示模式和灰色模式管理
export {
  useGridConfigStore,
  useBaseDisplayMode,
  useGridConfig,
  useGridConfigActions,
  type GridConfigStore,
} from './gridConfigStore';
// === 常量和类型导出 ===

// 🎨 颜色常量和类型
export type { ColorType } from '@/lib/types/color';
export {
  COLOR_CSS_MAP,
  BLACK_CSS_MAP,
  COLOR_NAMES,
  COLOR_SHORT_NAMES,
  COLOR_NUMBER_MAP,
  COLOR_PRIORITY_ORDER,
} from './constants/colors';

// 🔧 样式常量
export {
  SIZE_STYLES,
  VARIANT_STYLES,
  BASE_BUTTON_STYLES,
  BUTTON_STYLES,
  TAB_STYLES,
  INPUT_STYLES,
  GRID_STYLES,
  DEFAULT_MATRIX_STYLES,
  DEFAULT_CONTROL_PANEL_STYLES,
  DEFAULT_BUTTON_STYLES,
  DEFAULT_COLOR_SCHEME,
} from './constants/styles';

// 🛠️ 工具函数
export {
  getColorCSS,
  getColorCSSMap,
  getBlackCSS,
  getColorName,
  getColorDisplayName,
  getColorNumber,
  getColorPriority,
  getCellStyle,
  getTabStyle as getTabStyleUtil,
} from '@/lib/utils/colorSystem';

export {
  getButtonStyle,
  getTabStyle,
  getGridStyle,
  type ButtonVariant,
  type ButtonSize,
  type ButtonState,
  type ButtonConfig,
} from '@/lib/utils/buttonUtils';

// === 向后兼容性导出 ===

// 为了保持向后兼容，提供一些别名导出
export { useStyleStore as useColorStore } from './styleStore';
export { useBasicDataStore as useGridStore } from './basicDataStore';


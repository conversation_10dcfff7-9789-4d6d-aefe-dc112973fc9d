/**
 * dynamicStyleStore - 动态样式状态管理
 * 🎯 核心价值：管理运行时动态样式、响应式样式、交互样式
 * 📦 功能范围：动态样式计算、响应式布局、交互状态样式、性能优化
 * 🔄 架构设计：基于zustand的状态管理，支持实时样式更新
 */

import { create } from 'zustand';

// 动态样式配置接口
export interface DynamicStyleConfig {
  // 响应式配置
  breakpoints: {
    mobile: number;
    tablet: number;
    desktop: number;
    wide: number;
  };
  
  // 交互状态样式
  hoverEffects: {
    enabled: boolean;
    scale: number;
    opacity: number;
    duration: number;
  };
  
  clickEffects: {
    enabled: boolean;
    scale: number;
    duration: number;
    ripple: boolean;
  };
  
  focusEffects: {
    enabled: boolean;
    outlineWidth: number;
    outlineColor: string;
    outlineOffset: number;
  };
  
  // 动画配置
  transitions: {
    default: string;
    fast: string;
    slow: string;
  };
  
  // 自适应配置
  autoResize: boolean;
  autoScale: boolean;
  maintainAspectRatio: boolean;
  
  // 性能配置
  enableGPUAcceleration: boolean;
  enableVirtualization: boolean;
  debounceDelay: number;
}

// 当前设备信息
export interface DeviceInfo {
  width: number;
  height: number;
  devicePixelRatio: number;
  isMobile: boolean;
  isTablet: boolean;
  isDesktop: boolean;
  orientation: 'portrait' | 'landscape';
}

// 交互状态
export interface InteractionState {
  hoveredElements: Set<string>;
  clickedElements: Set<string>;
  focusedElement: string | null;
  draggedElement: string | null;
}

// Store状态接口
export interface DynamicStyleStoreState {
  // 动态样式配置
  config: DynamicStyleConfig;
  
  // 设备信息
  deviceInfo: DeviceInfo;
  
  // 交互状态
  interactionState: InteractionState;
  
  // 计算样式缓存
  styleCache: Map<string, any>;
  
  // 性能监控
  performanceMetrics: {
    renderTime: number;
    updateCount: number;
    cacheHitRate: number;
  };
  
  // 操作方法
  updateConfig: (updates: Partial<DynamicStyleConfig>) => void;
  updateDeviceInfo: (deviceInfo: Partial<DeviceInfo>) => void;
  
  // 交互状态管理
  setHovered: (elementId: string, hovered: boolean) => void;
  setClicked: (elementId: string, clicked: boolean) => void;
  setFocused: (elementId: string | null) => void;
  setDragged: (elementId: string | null) => void;
  
  // 样式计算
  getElementStyle: (elementId: string, baseStyle: any) => any;
  clearStyleCache: () => void;
  
  // 性能优化
  updatePerformanceMetrics: (metrics: Partial<DynamicStyleStoreState['performanceMetrics']>) => void;
}

// 默认动态样式配置
const defaultDynamicStyleConfig: DynamicStyleConfig = {
  // 响应式配置
  breakpoints: {
    mobile: 768,
    tablet: 1024,
    desktop: 1440,
    wide: 1920,
  },
  
  // 交互状态样式
  hoverEffects: {
    enabled: true,
    scale: 1.05,
    opacity: 0.8,
    duration: 200,
  },
  
  clickEffects: {
    enabled: true,
    scale: 0.95,
    duration: 150,
    ripple: true,
  },
  
  focusEffects: {
    enabled: true,
    outlineWidth: 2,
    outlineColor: '#3b82f6',
    outlineOffset: 2,
  },
  
  // 动画配置
  transitions: {
    default: 'all 0.2s ease-in-out',
    fast: 'all 0.1s ease-in-out',
    slow: 'all 0.3s ease-in-out',
  },
  
  // 自适应配置
  autoResize: true,
  autoScale: true,
  maintainAspectRatio: true,
  
  // 性能配置
  enableGPUAcceleration: true,
  enableVirtualization: false,
  debounceDelay: 16, // 60fps
};

// 默认设备信息
const defaultDeviceInfo: DeviceInfo = {
  width: 1920,
  height: 1080,
  devicePixelRatio: 1,
  isMobile: false,
  isTablet: false,
  isDesktop: true,
  orientation: 'landscape',
};

/**
 * 动态样式Store
 */
export const useDynamicStyleStore = create<DynamicStyleStoreState>((set, get) => ({
  // 初始状态
  config: defaultDynamicStyleConfig,
  deviceInfo: defaultDeviceInfo,
  interactionState: {
    hoveredElements: new Set(),
    clickedElements: new Set(),
    focusedElement: null,
    draggedElement: null,
  },
  styleCache: new Map(),
  performanceMetrics: {
    renderTime: 0,
    updateCount: 0,
    cacheHitRate: 0,
  },
  
  // 配置更新
  updateConfig: (updates) => {
    set((state) => ({
      config: { ...state.config, ...updates },
    }));
  },
  
  updateDeviceInfo: (updates) => {
    set((state) => ({
      deviceInfo: { ...state.deviceInfo, ...updates },
    }));
  },
  
  // 交互状态管理
  setHovered: (elementId, hovered) => {
    set((state) => {
      const newHoveredElements = new Set(state.interactionState.hoveredElements);
      if (hovered) {
        newHoveredElements.add(elementId);
      } else {
        newHoveredElements.delete(elementId);
      }
      
      return {
        interactionState: {
          ...state.interactionState,
          hoveredElements: newHoveredElements,
        },
      };
    });
  },
  
  setClicked: (elementId, clicked) => {
    set((state) => {
      const newClickedElements = new Set(state.interactionState.clickedElements);
      if (clicked) {
        newClickedElements.add(elementId);
      } else {
        newClickedElements.delete(elementId);
      }
      
      return {
        interactionState: {
          ...state.interactionState,
          clickedElements: newClickedElements,
        },
      };
    });
  },
  
  setFocused: (elementId) => {
    set((state) => ({
      interactionState: {
        ...state.interactionState,
        focusedElement: elementId,
      },
    }));
  },
  
  setDragged: (elementId) => {
    set((state) => ({
      interactionState: {
        ...state.interactionState,
        draggedElement: elementId,
      },
    }));
  },
  
  // 样式计算
  getElementStyle: (elementId, baseStyle) => {
    const state = get();
    const cacheKey = `${elementId}_${JSON.stringify(baseStyle)}`;
    
    // 检查缓存
    if (state.styleCache.has(cacheKey)) {
      return state.styleCache.get(cacheKey);
    }
    
    // 计算动态样式
    let dynamicStyle = { ...baseStyle };
    
    // 应用交互状态样式
    if (state.interactionState.hoveredElements.has(elementId) && state.config.hoverEffects.enabled) {
      dynamicStyle = {
        ...dynamicStyle,
        transform: `scale(${state.config.hoverEffects.scale})`,
        opacity: state.config.hoverEffects.opacity,
        transition: state.config.transitions.default,
      };
    }
    
    if (state.interactionState.clickedElements.has(elementId) && state.config.clickEffects.enabled) {
      dynamicStyle = {
        ...dynamicStyle,
        transform: `scale(${state.config.clickEffects.scale})`,
        transition: state.config.transitions.fast,
      };
    }
    
    if (state.interactionState.focusedElement === elementId && state.config.focusEffects.enabled) {
      dynamicStyle = {
        ...dynamicStyle,
        outline: `${state.config.focusEffects.outlineWidth}px solid ${state.config.focusEffects.outlineColor}`,
        outlineOffset: `${state.config.focusEffects.outlineOffset}px`,
      };
    }
    
    // 缓存结果
    state.styleCache.set(cacheKey, dynamicStyle);
    
    return dynamicStyle;
  },
  
  clearStyleCache: () => {
    set((state) => ({
      styleCache: new Map(),
    }));
  },
  
  // 性能优化
  updatePerformanceMetrics: (metrics) => {
    set((state) => ({
      performanceMetrics: { ...state.performanceMetrics, ...metrics },
    }));
  },
}));

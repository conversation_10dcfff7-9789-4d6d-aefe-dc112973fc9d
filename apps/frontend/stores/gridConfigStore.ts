/**
 * gridConfigStore - 网格配置状态管理
 * 🎯 核心价值：统一管理网格显示配置和所有显示模式（坐标、颜色渲染、数值映射）
 * 📦 功能范围：显示模式控制、网格配置持久化、cellContent统一管理、isActive状态控制
 * 🔄 架构设计：基于zustand的状态管理，支持持久化和配置迁移，集成现有矩阵映射逻辑
 */

import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { useCallback } from 'react';
import {
  DEFAULT_GRID_CONFIG
} from '@/components/grid-system/types';
import type {
  BaseDisplayMode,
  GridConfig
} from '@/components/grid-system/types';


// 单元格数据接口（用于获取映射值）
export interface CellDataForMapping {
  colorMappingValue?: number;
  level?: number;
  group?: number | null;
  color?: string;
  x?: number;
  y?: number;
}

// 网格配置存储接口
export interface GridConfigStore {
  // 状态
  baseDisplayMode: BaseDisplayMode;
  gridConfig: GridConfig;
  colorModeEnabled: boolean; // 颜色模式开关

  // 统一显示模式操作
  setDisplayMode: (mode: BaseDisplayMode) => void;

  // 颜色模式操作
  setColorModeEnabled: (enabled: boolean) => void;
  toggleColorMode: () => void;

  // 网格配置操作
  updateGridConfig: (updates: Partial<GridConfig>) => void;
  resetGridConfig: () => void;

  // 选择器
  getFullGridConfig: () => GridConfig;

  // 单元格激活状态检查（保留，因为与业务逻辑相关）
  isCellActive: (x: number, y: number) => boolean;
}

// 创建网格配置存储
export const useGridConfigStore = create<GridConfigStore>()(
  persist(
    (set, get) => ({
      // 初始状态
      baseDisplayMode: 'coordinates',
      gridConfig: DEFAULT_GRID_CONFIG,
      colorModeEnabled: false,

      // 统一显示模式操作
      setDisplayMode: (mode: BaseDisplayMode) => {
        set((state) => ({
          baseDisplayMode: mode,
          gridConfig: {
            ...state.gridConfig,
            displayMode: mode
          }
        }));
      },

      // 颜色模式操作
      setColorModeEnabled: (enabled: boolean) => {
        set(() => ({ colorModeEnabled: enabled }));
      },

      toggleColorMode: () => {
        set((state) => ({ colorModeEnabled: !state.colorModeEnabled }));
      },

      // 网格配置操作
      updateGridConfig: (updates: Partial<GridConfig>) => {
        set((state) => {
          const newConfig = { ...state.gridConfig, ...updates };

          // 同步基础显示模式
          const newBaseDisplayMode = newConfig.displayMode;

          return {
            gridConfig: newConfig,
            baseDisplayMode: newBaseDisplayMode,
          };
        });
      },

      resetGridConfig: () => {
        set(() => ({
          baseDisplayMode: 'coordinates',
          gridConfig: DEFAULT_GRID_CONFIG
        }));
      },

      // 选择器
      getFullGridConfig: () => {
        const state = get();
        return {
          ...state.gridConfig,
          displayMode: state.baseDisplayMode,
        };
      },



      // 单元格激活状态检查
      isCellActive: (_x: number, _y: number): boolean => {
        const state = get();
        switch (state.baseDisplayMode) {
          case 'coordinates':
            return true; // 坐标模式总是激活
          case 'color':
            return true; // 颜色渲染模式：isActive改成true
          case 'value':
            return true; // 数值模式：isActive改成true才能使用按键
          default:
            return false;
        }
      },



    }),
    {
      name: 'grid-config-store',
      version: 2, // 升级版本，移除向后兼容逻辑
    }
  )
);

// 选择器钩子
export const useBaseDisplayMode = () => useGridConfigStore((state) => state.baseDisplayMode);
export const useGridConfig = () => useGridConfigStore((state) => state.gridConfig);
export const useColorModeEnabled = () => useGridConfigStore((state) => state.colorModeEnabled);

// 操作钩子（简化版，移除重复的渲染逻辑）
export const useGridConfigActions = () => {
  const setDisplayMode = useGridConfigStore((state) => state.setDisplayMode);
  const setColorModeEnabled = useGridConfigStore((state) => state.setColorModeEnabled);
  const toggleColorMode = useGridConfigStore((state) => state.toggleColorMode);
  const updateGridConfig = useGridConfigStore((state) => state.updateGridConfig);
  const resetGridConfig = useGridConfigStore((state) => state.resetGridConfig);
  const isCellActive = useGridConfigStore((state) => state.isCellActive);

  return useCallback(() => ({
    setDisplayMode,
    setColorModeEnabled,
    toggleColorMode,
    updateGridConfig,
    resetGridConfig,
    isCellActive,
  }), [
    setDisplayMode,
    setColorModeEnabled,
    toggleColorMode,
    updateGridConfig,
    resetGridConfig,
    isCellActive,
  ])();
};

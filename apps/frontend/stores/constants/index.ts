/**
 * 常量统一导出
 * 🎯 核心价值：统一常量管理，为UI组件量化做准备
 * 📦 数据源：colors.ts、styles.ts、matrix.ts、grid.ts
 * ✅ 重构：常量统一管理
 */

// 颜色常量导出
export {
  COLOR_CSS_MAP,
  BLACK_CSS_MAP,
  COLOR_NAMES,
  COLOR_SHORT_NAMES,
  COLOR_NUMBER_MAP,
  COLOR_PRIORITY_ORDER,
} from './colors';

// 样式常量导出
export {
  SIZE_STYLES,
  VARIANT_STYLES,
  BASE_BUTTON_STYLES,
  BUTTON_STYLES,
  TAB_STYLES,
  INPUT_STYLES,
  GRID_STYLES,
  DEFAULT_MATRIX_STYLES,
  DEFAULT_CONTROL_PANEL_STYLES,
  DEFAULT_BUTTON_STYLES,
  DEFAULT_COLOR_SCHEME,
} from './styles';

// 矩阵常量导出
export {
  GROUP_A_DATA,
  AVAILABLE_LEVELS,
  DEFAULT_COLOR_VALUES,
  SPECIAL_COORDINATES,
  MAX_LEVEL,
  MATRIX_SIZE,
  GRID_DIMENSIONS,
  GRID_CENTER,
  GROUP_OFFSET_CONFIGS,
} from './matrix';

// 网格系统常量导出
export {
  GRID_CONSTANTS,
  DEFAULT_UI_GRID_CONFIG,
  DEFAULT_BUSINESS_GRID_CONFIG,
  GRID_DISPLAY_MODES,
  CELL_SHAPES,
  INTERACTION_MODES,
  SELECTION_MODES,
  GRID_EVENT_TYPES,
  GRID_CSS_CLASSES,
  GRID_DATA_ATTRIBUTES,
  PERFORMANCE_THRESHOLDS,
  VALIDATION_RULES,
} from './grid';

// 网格系统类型导出
export type {
  GridDisplayMode,
  CellShape,
  InteractionMode,
  SelectionMode,
  GridEventType,
} from './grid';

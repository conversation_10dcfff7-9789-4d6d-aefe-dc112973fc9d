/**
 * Grid System Constants
 * 
 * 网格系统相关的常量定义
 * 🎯 核心价值：统一管理网格系统的常量，避免重复定义
 * 📦 常量范围：UI配置、业务配置、默认值、限制值等
 */

import type { GridUIConfig } from '@/lib/types/grid';
import type { GridBusinessConfig } from '@/features/grid-system/types';

// 网格基础常量
export const GRID_CONSTANTS = {
  // 网格尺寸
  GRID_SIZE: 33,
  CENTER_OFFSET: 16, // Math.floor(33 / 2)
  
  // 单元格尺寸
  DEFAULT_CELL_SIZE: 24,
  MIN_CELL_SIZE: 12,
  MAX_CELL_SIZE: 48,
  
  // 间距和边距
  DEFAULT_GAP: 2,
  DEFAULT_PADDING: 16,
  
  // 字体
  DEFAULT_FONT_SIZE: 12,
  MIN_FONT_SIZE: 8,
  MAX_FONT_SIZE: 20,
  
  // 动画
  DEFAULT_ANIMATION_DURATION: 200,
  DEFAULT_SCALE_FACTOR: 1.1,
  
  // 性能
  DEFAULT_BATCH_SIZE: 100,
  VIRTUALIZATION_THRESHOLD: 1000,
} as const;

// 统一的网格颜色常量
export const GRID_COLORS = {
  DEFAULT_GRID_COLOR: '#e5e7eb',
  // DEFAULT_GRAY_COLOR: '#f3f4f6',
} as const;

// 默认UI配置
export const DEFAULT_UI_GRID_CONFIG: GridUIConfig = {
  fontSize: GRID_CONSTANTS.DEFAULT_FONT_SIZE,
  matrixMargin: GRID_CONSTANTS.DEFAULT_PADDING,
  gridColor: GRID_COLORS.DEFAULT_GRID_COLOR,
  cellShape: 'square',
  displayMode: 'visible',
  // 扩展属性
  size: GRID_CONSTANTS.DEFAULT_CELL_SIZE,
  gap: GRID_CONSTANTS.DEFAULT_GAP,
  padding: GRID_CONSTANTS.DEFAULT_PADDING,
  scale: {
    enabled: true,
    factor: GRID_CONSTANTS.DEFAULT_SCALE_FACTOR,
  },
  animation: {
    enabled: true,
    duration: GRID_CONSTANTS.DEFAULT_ANIMATION_DURATION,
  },
};

// 默认业务配置
export const DEFAULT_BUSINESS_GRID_CONFIG: GridBusinessConfig = {
  // 基础显示配置
  displayMode: 'value',
  grayModeEnabled: false,
  
  // 交互配置
  interactionMode: 'view',
  selectionMode: 'single',
  autoSave: false,
  realTimeUpdates: true,
  
  // 性能配置
  virtualization: false,
  batchSize: GRID_CONSTANTS.DEFAULT_BATCH_SIZE,
  debounceMs: 300,
};

// 网格显示模式
export const GRID_DISPLAY_MODES = {
  VALUE: 'value',
  COORDINATES: 'coordinates',
  COLOR: 'color',
} as const;

export type GridDisplayMode = typeof GRID_DISPLAY_MODES[keyof typeof GRID_DISPLAY_MODES];

// 单元格形状
export const CELL_SHAPES = {
  SQUARE: 'square',
  ROUNDED: 'rounded',
  CIRCLE: 'circle',
} as const;

export type CellShape = typeof CELL_SHAPES[keyof typeof CELL_SHAPES];

// 交互模式
export const INTERACTION_MODES = {
  VIEW: 'view',
  EDIT: 'edit',
  SELECT: 'select',
} as const;

export type InteractionMode = typeof INTERACTION_MODES[keyof typeof INTERACTION_MODES];

// 选择模式
export const SELECTION_MODES = {
  SINGLE: 'single',
  MULTIPLE: 'multiple',
  RANGE: 'range',
} as const;

export type SelectionMode = typeof SELECTION_MODES[keyof typeof SELECTION_MODES];

// 网格事件类型
export const GRID_EVENT_TYPES = {
  CELL_CLICK: 'cell-click',
  CELL_HOVER: 'cell-hover',
  CELL_FOCUS: 'cell-focus',
  CELL_DOUBLE_CLICK: 'cell-double-click',
  SELECTION_CHANGE: 'selection-change',
  CONFIG_CHANGE: 'config-change',
  DATA_CHANGE: 'data-change',
  ERROR: 'error',
} as const;

export type GridEventType = typeof GRID_EVENT_TYPES[keyof typeof GRID_EVENT_TYPES];

// CSS类名常量
export const GRID_CSS_CLASSES = {
  GRID_CONTAINER: 'grid-container',
  GRID_MATRIX: 'grid-matrix',
  GRID_CELL: 'grid-cell',
  CELL_ACTIVE: 'cell-active',
  CELL_FOCUSED: 'cell-focused',
  CELL_HOVERED: 'cell-hovered',
  CELL_SELECTED: 'cell-selected',
  CELL_EDITING: 'cell-editing',
  GRID_LOADING: 'grid-loading',
  GRID_ERROR: 'grid-error',
  GRID_EMPTY: 'grid-empty',
} as const;

// 数据属性常量
export const GRID_DATA_ATTRIBUTES = {
  CELL_X: 'data-x',
  CELL_Y: 'data-y',
  CELL_ACTIVE: 'data-active',
  CELL_FOCUSED: 'data-focused',
  CELL_SELECTED: 'data-selected',
  GRID_MODE: 'data-grid-mode',
  INTERACTION_MODE: 'data-interaction-mode',
} as const;

// 性能阈值
export const PERFORMANCE_THRESHOLDS = {
  RENDER_WARNING_MS: 16, // 60fps = 16.67ms per frame
  UPDATE_WARNING_MS: 100,
  MEMORY_WARNING_MB: 50,
  CELL_COUNT_WARNING: 2000,
} as const;

// 验证规则
export const VALIDATION_RULES = {
  CELL_SIZE: {
    min: GRID_CONSTANTS.MIN_CELL_SIZE,
    max: GRID_CONSTANTS.MAX_CELL_SIZE,
  },
  FONT_SIZE: {
    min: GRID_CONSTANTS.MIN_FONT_SIZE,
    max: GRID_CONSTANTS.MAX_FONT_SIZE,
  },
  COORDINATES: {
    min: -GRID_CONSTANTS.CENTER_OFFSET,
    max: GRID_CONSTANTS.CENTER_OFFSET,
  },
  ANIMATION_DURATION: {
    min: 0,
    max: 1000,
  },
  SCALE_FACTOR: {
    min: 1,
    max: 2,
  },
} as const;

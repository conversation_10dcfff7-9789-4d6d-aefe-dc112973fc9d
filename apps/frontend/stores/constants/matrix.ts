/**
 * 矩阵系统常量定义
 * 🎯 职责：矩阵相关的静态数据、配置常量、默认值等
 * 📦 重构来源：从 basicDataStore.ts 中提取的常量定义
 * ✅ 集中管理：所有矩阵相关常量的统一管理
 */

import type { BasicColorType, GroupType, ColorValue, GroupOffsetConfig } from '@/lib/types/matrix';

// A组基础数据结构 - 所有组数据的基础
export const GROUP_A_DATA = {
  black: {
    1: [[0, 0] as [number, number]]
  },
  red: {
    1: [[8, 0] as [number, number]],
    2: [[4, 0] as [number, number]],
    3: [[2, 0], [6, 0], [4, 2], [4, -2]] as [number, number][],
    4: [[1, 0], [3, 0], [5, 0], [7, 0], [2, 1], [2, -1], [3, 2], [3, -2], [4, 1], [4, 3], [4, -1], [4, -3], [6, 1], [6, -1], [5, 2], [5, -2]] as [number, number][]
  },
  orange: {
    1: [[4, -4] as [number, number]],
    3: [[6, 2], [2, -2], [-2, -6]] as [number, number][],
    4: [[-3, -5], [-1, -7], [-1, -5], [1, -1], [1, -3], [3, -3], [3, -1], [5, 3], [7, 1], [5, 1]] as [number, number][]
  },
  yellow: {
    1: [[0, -8] as [number, number]],
    2: [[0, -4] as [number, number]],
    3: [[0, -2], [0, -6], [2, -4], [-2, -4]] as [number, number][],
    4: [[0, -1], [-1, -2], [0, -3], [1, -2], [-2, -3], [-3, -4], [-2, -5], [-1, -4], [2, -3], [1, -4], [2, -5], [3, -4], [0, -5], [-1, -6], [0, -7], [1, -6]] as [number, number][]
  },
  green: {
    1: [[-4, -4] as [number, number]],
    3: [[-6, 2], [-2, -2], [2, -6]] as [number, number][],
    4: [[-5, 3], [-7, 1], [-5, 1], [-1, -1], [-3, -1], [-3, -3], [-1, -3], [3, -5], [1, -5], [1, -7]] as [number, number][]
  },
  cyan: {
    1: [[-8, 0] as [number, number]],
    2: [[-4, 0] as [number, number]],
    3: [[-2, 0], [-6, 0], [-4, 2], [-4, -2]] as [number, number][],
    4: [[-7, 0], [-5, 0], [-3, 0], [-1, 0], [-6, 1], [-6, -1], [-5, 2], [-5, -2], [-4, 1], [-4, 3], [-4, -1], [-4, -3], [-2, 1], [-2, -1], [-3, 2], [-3, -2]] as [number, number][]
  },
  blue: {
    1: [[-4, 4] as [number, number]],
    3: [[2, 6], [-2, 2], [-6, -2]] as [number, number][],
    4: [[1, 7], [3, 5], [1, 5], [-3, 3], [-1, 3], [-1, 1], [-3, 1], [-7, -1], [-5, -1], [-5, -3]] as [number, number][]
  },
  purple: {
    1: [[0, 8] as [number, number]],
    2: [[0, 4] as [number, number]],
    3: [[0, 2], [0, 6], [2, 4], [-2, 4]] as [number, number][],
    4: [[0, 1], [0, 3], [0, 5], [0, 7], [1, 2], [1, 4],[1, 6], [-1, 2],[-1, 4], [-1, 6], [2, 3], [2, 5], [-2, 3], [-2, 5], [-3, 4], [3, 4]] as [number, number][]
  },
  pink: {
    1: [[4, 4] as [number, number]],
    3: [[-2, 6], [2, 2], [6, -2]] as [number, number][],
    4: [[-1, 7], [-1, 5], [-3, 5], [3, 3], [3, 1], [1, 1], [1, 3], [7, -1], [5, -3], [5, -1]] as [number, number][]
  }
} as const;

// 可用级别映射 - 统一的权威数据源
export const AVAILABLE_LEVELS: Record<BasicColorType, number[]> = {
  red: [1, 2, 3, 4],
  cyan: [1, 2, 3, 4],
  yellow: [1, 2, 3, 4],
  purple: [1, 2, 3, 4],
  orange: [1, 3, 4],
  green: [1, 3, 4],
  blue: [1, 3, 4],
  pink: [1, 3, 4],
  black: [1], // 黑色只有一个级别
} as const;

// 默认颜色值与颜色数字映射
export const DEFAULT_COLOR_VALUES: Record<BasicColorType, ColorValue> = {
  black: { name: '黑色', hex: '#000000', rgb: [0, 0, 0], hsl: [0, 0, 0] }, // 黑色没有mappingValue
  red: { name: '红色', hex: '#ef4444', rgb: [239, 68, 68], hsl: [0, 84, 60], mappingValue: 1 },
  cyan: { name: '青色', hex: '#06b6d4', rgb: [6, 182, 212], hsl: [189, 94, 43], mappingValue: 5 },
  yellow: { name: '黄色', hex: '#eab308', rgb: [234, 179, 8], hsl: [45, 93, 47], mappingValue: 3 },
  purple: { name: '紫色', hex: '#a855f7', rgb: [168, 85, 247], hsl: [271, 91, 65], mappingValue: 7 },
  orange: { name: '橙色', hex: '#f97316', rgb: [249, 115, 22], hsl: [25, 95, 53], mappingValue: 2 },
  green: { name: '绿色', hex: '#22c55e', rgb: [34, 197, 94], hsl: [142, 71, 45], mappingValue: 4 },
  blue: { name: '蓝色', hex: '#3b82f6', rgb: [59, 130, 246], hsl: [217, 91, 60], mappingValue: 6 },
  pink: { name: '粉色', hex: '#ec4899', rgb: [236, 72, 153], hsl: [327, 82, 60], mappingValue: 8 },
};

// 黑色格子坐标字符映射
export const SPECIAL_COORDINATES = new Map([
  ['0,0', 'A'],
  ['16,0', 'B'],
  ['-16,0', 'C'],
  ['0,16', 'D'],
  ['0,-16', 'E'],
  ['8,8', 'F'],
  ['-8,-8', 'G'],
  ['8,-8', 'H'],
  ['-8,8', 'I'],
  ['16,16', 'J'],
  ['-16,-16', 'K'],
  ['16,-16', 'L'],
  ['-16,16', 'M'],
]);

// 矩阵计算常量
export const MAX_LEVEL = 4;
export const MATRIX_SIZE = 33; // 33x33网格

// 网格尺寸和中心点常量
export const GRID_DIMENSIONS = {
  ROWS: 33,
  COLS: 33
} as const;

export const GRID_CENTER = {
  X: 16, // 中心列索引 (33-1)/2 = 16
  Y: 16  // 中心行索引 (33-1)/2 = 16
} as const;

// 扩展的A到M组偏移配置 - 基于对称性和规律性设计
export const GROUP_OFFSET_CONFIGS: Record<GroupType, GroupOffsetConfig> = {
  A: {
    defaultOffset: [0, 0], // A组无偏移，作为基础
  },
  B: {
    defaultOffset: [16, 0], // 向右偏移
    level1Offsets: {
      red: [0, 0],      // 无偏移
      orange: [8, 8],   // 右上偏移
      yellow: [16, 16], // 右上偏移
      green: [24, 0],   // 右偏移
      cyan: [32, 0],    // 右偏移
      blue: [24, 0],    // 右偏移
      purple: [16, -16],// 右下偏移
      pink: [8, -8],    // 右下偏移
      black: [16, 0],   // 右偏移
    }
  },
  C: {
    defaultOffset: [-16, 0], // 向左偏移
    level1Offsets: {
      red: [32, 0],     // 右偏移
      orange: [-24, 8], // 左上偏移
      yellow: [-16, 16],// 左上偏移
      green: [-8, 8],   // 左上偏移
      cyan: [0, 0],     // 无偏移
      blue: [-8, -8],   // 左下偏移
      purple: [-16, -16],// 左下偏移
      pink: [-24, -8],  // 左下偏移
      black: [-16, 0],  // 左偏移
    }
  },
  D: {
    defaultOffset: [0, 16],
    level1Offsets: {
      red: [-16, 16],
      orange: [-8, 24],
      yellow: [0, 32],
      green: [8, 24],
      cyan: [16, 16],
      blue: [8, 8],
      purple: [0, 0],
      pink: [-8, 8],
      black: [0, 16],
    }
  },
  E: {
    defaultOffset: [0, -16],
    level1Offsets: {
      red: [-16, -16],
      orange: [-8, -8],
      yellow: [0, -16],
      green: [8, -8],
      cyan: [16, -16],
      blue: [8, -24],
      purple: [-32, 0],
      pink: [-8, -24],
      black: [0, -16],
    }
  },
  F: {
    defaultOffset: [8, 8],
    level1Offsets: {
      red: [0, 0],
      orange: [8, 8],
      yellow: [16, 16],
      green: [16, 16],
      cyan: [16, 16],
      blue: [8, 8],
      purple: [0, 0],
      pink: [0, 0],
      black: [8, 8],
    }
  },
  G: {
    defaultOffset: [-8, -8],
    level1Offsets: {
      red: [-16, -16],
      orange: [-8, -8],
      yellow: [0, 0],
      green: [0, 0],
      cyan: [0, 0],
      blue: [-8, -8],
      purple: [-16, -16],
      pink: [-16, -16],
      black: [-8, -8],
    }
  },
  H: {
    defaultOffset: [8, -8],
    level1Offsets: {
      red: [0, 0],
      orange: [0, 0],
      yellow: [0, 0],
      green: [8, -8],
      cyan: [16, -16],
      blue: [16, -16],
      purple: [16, -16],
      pink: [8, -8],
      black: [8, -8],
    }
  },
  I: {
    defaultOffset: [-8, 8],
    level1Offsets: {
      red: [-16, 16],
      orange: [-16, 16],
      yellow: [-16, 16],
      green: [-8, 8],
      cyan: [0, 0],
      blue: [0, 0],
      purple: [0, 0],
      pink: [-8, 8],
      black: [-8, 8],
    }
  },
  J: {
    defaultOffset: [16, 16],
    level1Offsets: {
      red: [16, 16],
      orange: [16, 16],
      yellow: [16, 16],
      green: [16, 16],
      cyan: [16, 16],
      blue: [16, 16],
      purple: [16, 16],
      pink: [16, 16],
      black: [16, 16],
    }
  },
  K: {
    defaultOffset: [-16, -16],
    level1Offsets: {
      red: [-16, -16],
      orange: [-16, -16],
      yellow: [-16, -16],
      green: [-16, -16],
      cyan: [-16, -16],
      blue: [-16, -16],
      purple: [-16, -16],
      pink: [-16, -16],
      black: [-16, -16],
    }
  },
  L: {
    defaultOffset: [16, -16],
    level1Offsets: {
      red: [16, -16],
      orange: [16, -16],
      yellow: [16, -16],
      green: [16, -16],
      cyan: [16, -16],
      blue: [16, -16],
      purple: [16, -16],
      pink: [16, -16],
      black: [16, -16],
    }
  },
  M: {
    defaultOffset: [-16, 16],
    level1Offsets: {
      red: [-16, 16],
      orange: [-16, 16],
      yellow: [-16, 16],
      green: [-16, 16],
      cyan: [-16, 16],
      blue: [-16, 16],
      purple: [-16, 16],
      pink: [-16, 16],
      black: [-16, 16],
    }
  },
};

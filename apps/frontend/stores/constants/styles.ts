/**
 * 样式常量定义 - 静态样式配置集中管理
 * 🎯 职责：按钮样式、UI配置、主题配置等纯常量定义
 * 📦 数据源：原constants/styles.ts，移动到stores统一管理
 * ✅ Phase 4.7.1: 职责分离 - 静态常量与状态管理分离
 * 🔄 重构：常量统一管理，为UI组件量化做准备
 */

// 定义本地类型，避免循环依赖
export type ButtonVariant = 'default' | 'primary' | 'secondary' | 'outline' | 'ghost' | 'destructive';
export type ButtonSize = 'sm' | 'md' | 'lg' | 'xl';

export interface MatrixStyles {
  container: string;
  gridLayout: string;
  cellBase: string;
  cellHover: string;
}

export interface ControlPanelStyles {
  container: string;
  header: string;
  content: string;
  footer: string;
}

export interface ButtonStyles {
  base: string;
  variants: Record<ButtonVariant, string>;
  sizes: Record<ButtonSize, string>;
}

export interface ColorScheme {
  primary: string;
  secondary: string;
  background: string;
  foreground: string;
  muted: string;
  accent: string;
  border: string;
}

// 按钮尺寸样式 - 根据不同尺寸定义对应的CSS类
export const SIZE_STYLES: Record<ButtonSize, string> = {
  sm: 'px-3 py-1.5 text-sm',    // 小尺寸：3px内边距、1.5px上下边距、小文字
  md: 'px-4 py-2 text-sm',      // 中等尺寸：4px内边距、2px上下边距、小文字
  lg: 'px-6 py-3 text-base',    // 大尺寸：6px内边距、3px上下边距、基础文字
  xl: 'px-8 py-4 text-lg',      // 超大尺寸：8px内边距、4px上下边距、大文字
};

// 按钮变体样式 - 根据不同变体定义对应的颜色和状态样式
export const VARIANT_STYLES: Record<ButtonVariant, string> = {
  default: 'bg-white text-gray-600 border-gray-200 hover:bg-gray-50',         // 默认按钮：白色背景、灰色文字
  primary: 'bg-blue-600 text-white border-blue-500 hover:bg-blue-700',        // 主要按钮：蓝色背景、白色文字
  secondary: 'bg-gray-100 text-gray-700 border-gray-200 hover:bg-gray-200',   // 次要按钮：灰色背景、深灰文字
  outline: 'bg-transparent text-gray-700 border-gray-300 hover:bg-gray-50',   // 轮廓按钮：透明背景、边框
  ghost: 'bg-transparent text-gray-600 border-transparent hover:bg-gray-100', // 幽灵按钮：透明背景、无边框
  destructive: 'bg-red-600 text-white border-red-500 hover:bg-red-700',       // 危险按钮：红色背景、白色文字
};

// 按钮基础样式 - 所有按钮共用的基础样式类
export const BASE_BUTTON_STYLES = 'rounded border transition-colors duration-200 flex items-center justify-center font-medium focus:outline-none focus:ring-2 focus:ring-offset-2';

// 按钮状态样式组合（保持向后兼容） - 旧版本的按钮样式配置
export const BUTTON_STYLES = {
  active: 'bg-gray-200 text-gray-800 border-gray-400',                    // 激活状态样式
  inactive: 'bg-white text-gray-600 border-gray-200 hover:bg-gray-50',    // 非激活状态样式
  primary: 'bg-blue-600 text-white border-blue-500 hover:bg-blue-700',    // 主要状态样式
  secondary: 'bg-gray-100 text-gray-700 border-gray-200 hover:bg-gray-200', // 次要状态样式
  danger: 'bg-red-600 text-white border-red-500 hover:bg-red-700',        // 危险状态样式
  success: 'bg-green-600 text-white border-green-500 hover:bg-green-700', // 成功状态样式
} as const;

// Tab切换样式 - 标签页切换的样式配置
export const TAB_STYLES = {
  active: 'bg-white text-gray-800 border-gray-400 shadow-sm',         // 激活标签：白色背景、阴影效果
  inactive: 'bg-gray-100 text-gray-600 border-gray-200 hover:bg-gray-200', // 非激活标签：灰色背景、悬停效果
} as const;

// 输入框样式 - 表单输入框的样式配置
export const INPUT_STYLES = {
  default: 'px-2 py-1 text-xs border border-gray-200 rounded focus:border-gray-400 focus:outline-none', // 默认输入框样式
  center: 'text-center', // 文字居中样式
} as const;

// 网格布局样式 - CSS Grid布局的列数配置
export const GRID_STYLES = {
  cols2: 'grid-cols-2', // 2列网格
  cols3: 'grid-cols-3', // 3列网格
  cols4: 'grid-cols-4', // 4列网格
  cols5: 'grid-cols-5', // 5列网格
} as const;

// 默认样式配置 - 矩阵组件的默认样式
export const DEFAULT_MATRIX_STYLES: MatrixStyles = {
  container: 'flex-1 relative overflow-hidden bg-gray-100',     // 容器样式：弹性布局、相对定位、隐藏溢出、灰色背景
  gridLayout: 'grid gap-1 p-4',                                // 网格布局：Grid显示、1px间距、4px内边距
  cellBase: 'aspect-square flex items-center justify-center text-xs font-medium cursor-pointer border border-gray-300 transition-all duration-200', // 单元格基础样式
  cellHover: 'hover:scale-105 hover:z-10',                     // 单元格悬停样式：放大效果、提升层级
} as const;

// 默认控制面板样式 - 控制面板组件的样式配置
export const DEFAULT_CONTROL_PANEL_STYLES: ControlPanelStyles = {
  container: 'w-80 h-full bg-white border-l border-gray-200 flex flex-col shadow-lg', // 容器：固定宽度、全高、白色背景、左边框、弹性列布局、阴影
  header: 'p-4 border-b border-gray-200 bg-gray-50',         // 头部：内边距、下边框、浅灰背景
  content: 'flex-1 p-4 overflow-y-auto',                     // 内容区：弹性增长、内边距、垂直滚动
  footer: 'p-4 border-t border-gray-200 bg-gray-50',         // 底部：内边距、上边框、浅灰背景
} as const;

// 默认按钮样式 - 通用按钮组件的样式配置
export const DEFAULT_BUTTON_STYLES: ButtonStyles = {
  base: BASE_BUTTON_STYLES,
  variants: VARIANT_STYLES,
  sizes: SIZE_STYLES,
} as const;

// 默认颜色方案 - 应用的默认配色方案
export const DEFAULT_COLOR_SCHEME: ColorScheme = {
  primary: '#3b82f6',     // 主要颜色：蓝色
  secondary: '#64748b',   // 次要颜色：灰蓝色
  background: '#ffffff',  // 背景颜色：白色
  foreground: '#0f172a',  // 前景颜色：深灰色
  muted: '#f1f5f9',      // 静音颜色：浅灰色
  accent: '#3b82f6',     // 强调颜色：蓝色
  border: '#e2e8f0',     // 边框颜色：浅灰色
} as const;

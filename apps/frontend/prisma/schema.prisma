// Cube1_Group 全栈架构 - Prisma Schema
// 🎯 基于现有Zustand stores设计的数据库模型
// 🔄 支持SQLite开发环境 + PostgreSQL生产环境

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = "file:./dev.db"
}

// === 项目管理（已移除用户关联） ===
// User model 已被移除

model Project {
  id          String   @id @default(cuid())
  name        String
  description String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // 创建者信息（可选，不强制关联）
  creatorName String?

  // 关联的数据
  colorData       ColorData[]
  gridData        GridData[]
  blackCellData   BlackCellData[]
  versions        Version[]
  settings        ProjectSettings?


  @@map("projects")
}

// === 核心数据模型 ===

// 颜色坐标数据 (基于basicDataStore)
model ColorData {
  id        String   @id @default(cuid())
  projectId String
  project   Project  @relation(fields: [projectId], references: [id], onDelete: Cascade)

  // 颜色类型 (red, cyan, yellow, purple, orange, green, blue, pink)
  colorType String

  // 层级 (1-4)
  level     Int

  // 坐标数据 (JSON格式存储坐标数组)
  coordinates String // JSON: [[x1,y1], [x2,y2], ...]

  // 颜色值配置
  colorValue String // 颜色值 (如 "red", "#ff0000")

  // 可见性控制
  visible   Boolean @default(true)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@unique([projectId, colorType, level])
  @@map("color_data")
}

// 网格数据 (基于basicDataStore的gridData)
model GridData {
  id        String   @id @default(cuid())
  projectId String
  project   Project  @relation(fields: [projectId], references: [id], onDelete: Cascade)

  // 网格位置
  row       Int
  col       Int
  x         Int      // x坐标
  y         Int      // y坐标

  // 格子属性
  number    Int
  color     String
  level     Int
  groupId   Int?     // 分组ID，可为空

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@unique([projectId, row, col])
  @@unique([projectId, x, y])
  @@map("grid_data")
}

// 黑色格子数据 (基于basicDataStore的blackCellData)
model BlackCellData {
  id        String   @id @default(cuid())
  projectId String
  project   Project  @relation(fields: [projectId], references: [id], onDelete: Cascade)

  // 坐标
  x         Int
  y         Int

  // 字母标识
  letter    String

  // 可见性
  visible   Boolean @default(true)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@unique([projectId, x, y])
  @@map("black_cell_data")
}

// 版本管理 (基于businessDataStore的版本系统)
model Version {
  id          String   @id @default(cuid())
  projectId   String
  project     Project  @relation(fields: [projectId], references: [id], onDelete: Cascade)

  // 版本信息
  name        String
  description String?
  versionType String   // 'default', 'group', 'mixed', 'matrix'

  // 版本数据 (JSON格式存储完整状态)
  data        String   // JSON: 完整的状态快照

  // 是否为当前版本
  isCurrent   Boolean @default(false)

  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@unique([projectId, name, versionType])
  @@map("versions")
}

// 项目设置 (基于styleStore和dynamicStyleStore)
model ProjectSettings {
  id        String   @id @default(cuid())
  projectId String   @unique
  project   Project  @relation(fields: [projectId], references: [id], onDelete: Cascade)

  // 样式设置 (基于styleStore)
  currentTheme    String  @default("light")
  showBlackCells  Boolean @default(true)
  matrixStyles    String  // JSON: MatrixStyles
  colorScheme     String  // JSON: ColorScheme

  // 动态样式设置 (基于dynamicStyleStore)
  fontSize            Int     @default(12)
  matrixMargin        Int     @default(4)
  cellShape           String  @default("rounded") // 'square', 'rounded', 'circle'
  displayMode         String  @default("number")  // 'number', 'coordinate', 'hidden'
  enableCircleScale   Boolean @default(true)
  circleScaleFactor   Float   @default(1.4)

  // 全局显示控制
  showAllNumbers Boolean @default(false)
  showAllColors  Boolean @default(true)
  showAllLevel1  Boolean @default(true)
  showAllLevel2  Boolean @default(true)
  showAllLevel3  Boolean @default(true)
  showAllLevel4  Boolean @default(true)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("project_settings")
}



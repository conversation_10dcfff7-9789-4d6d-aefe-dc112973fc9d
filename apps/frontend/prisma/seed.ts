/**
 * Prisma数据库种子文件
 * 🎯 核心价值：初始化开发环境数据，创建示例项目
 * 🔄 开发支持：提供一致的开发环境数据
 * ⚡ 快速启动：新开发者可以快速获得可用的测试数据
 */

import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function main() {
  process.env.NODE_ENV === 'development' && console.log('🌱 开始数据库种子初始化...');

  // 创建示例项目
  const sampleProject = await prisma.project.upsert({
    where: { id: 'sample-project-id' },
    update: {},
    create: {
      id: 'sample-project-id',
      name: 'Cube1_Group 示例项目',
      description: '8进制编码辅助系统示例项目，包含完整的演示数据',
      creatorName: 'Cube1 开发团队',
    },
  });

  process.env.NODE_ENV === 'development' && console.log('✅ 创建示例项目:', sampleProject.name);

  // 创建项目设置
  await prisma.projectSettings.upsert({
    where: { projectId: sampleProject.id },
    update: {},
    create: {
      projectId: sampleProject.id,
      currentTheme: 'light',
      showBlackCells: true,
      matrixStyles: JSON.stringify({
        backgroundColor: '#f8f9fa',
        borderColor: '#dee2e6',
        cellSpacing: 2,
      }),
      colorScheme: JSON.stringify({
        primary: '#007bff',
        secondary: '#6c757d',
        success: '#28a745',
        danger: '#dc3545',
        warning: '#ffc107',
        info: '#17a2b8',
      }),
      fontSize: 12,
      matrixMargin: 4,
      cellShape: 'rounded',
      displayMode: 'number',
      enableCircleScale: true,
      circleScaleFactor: 1.4,
      showAllNumbers: false,
      showAllColors: true,
      showAllLevel1: true,
      showAllLevel2: true,
      showAllLevel3: true,
      showAllLevel4: true,
    },
  });

  process.env.NODE_ENV === 'development' && console.log('✅ 创建项目设置');



  process.env.NODE_ENV === 'development' && console.log('✅ 创建组合数据');

  // 创建示例颜色数据
  const colorTypes = ['red', 'cyan', 'yellow', 'purple', 'orange', 'green', 'blue', 'pink'];
  const colorValues = {
    red: '#ff0000',
    cyan: '#00ffff',
    yellow: '#ffff00',
    purple: '#800080',
    orange: '#ffa500',
    green: '#008000',
    blue: '#0000ff',
    pink: '#ffc0cb',
  };

  for (const colorType of colorTypes) {
    for (let level = 1; level <= 4; level++) {
      // 生成示例坐标数据
      const sampleCoordinates = generateSampleCoordinates(colorType, level);
      
      await prisma.colorData.upsert({
        where: {
          projectId_colorType_level: {
            projectId: sampleProject.id,
            colorType,
            level,
          },
        },
        update: {},
        create: {
          projectId: sampleProject.id,
          colorType,
          level,
          coordinates: JSON.stringify(sampleCoordinates),
          colorValue: colorValues[colorType as keyof typeof colorValues],
          visible: true,
        },
      });
    }
  }

  process.env.NODE_ENV === 'development' && console.log('✅ 创建示例颜色数据');

  // 创建黑色格子数据
  const blackCellCoordinates = [
    { x: 0, y: 0, letter: 'A' },
    { x: 16, y: 0, letter: 'B' },
    { x: -16, y: 0, letter: 'C' },
    { x: 0, y: 16, letter: 'D' },
    { x: 0, y: -16, letter: 'E' },
    { x: 8, y: 8, letter: 'F' },
    { x: -8, y: -8, letter: 'G' },
    { x: 8, y: -8, letter: 'H' },
    { x: -8, y: 8, letter: 'I' },
    { x: 16, y: 16, letter: 'J' },
    { x: -16, y: -16, letter: 'K' },
    { x: 16, y: -16, letter: 'L' },
    { x: -16, y: 16, letter: 'M' },
  ];

  for (const blackCell of blackCellCoordinates) {
    await prisma.blackCellData.upsert({
      where: {
        projectId_x_y: {
          projectId: sampleProject.id,
          x: blackCell.x,
          y: blackCell.y,
        },
      },
      update: {},
      create: {
        projectId: sampleProject.id,
        x: blackCell.x,
        y: blackCell.y,
        letter: blackCell.letter,
        visible: true,
      },
    });
  }

  process.env.NODE_ENV === 'development' && console.log('✅ 创建黑色格子数据');

  // 创建示例版本
  await prisma.version.upsert({
    where: {
      projectId_name_versionType: {
        projectId: sampleProject.id,
        name: '初始版本',
        versionType: 'default',
      },
    },
    update: {},
    create: {
      projectId: sampleProject.id,
      name: '初始版本',
      description: '项目的初始配置版本',
      versionType: 'default',
      data: JSON.stringify({
        timestamp: Date.now(),
        colorData: 'initial',
        settings: 'default',
      }),
      isCurrent: true,
    },
  });

  process.env.NODE_ENV === 'development' && console.log('✅ 创建示例版本');

  process.env.NODE_ENV === 'development' && console.log('🎉 数据库种子初始化完成!');
}

// 生成示例坐标数据
function generateSampleCoordinates(colorType: string, level: number): [number, number][] {
  const coordinates: [number, number][] = [];
  const baseOffset = level * 2;
  
  // 根据颜色类型和层级生成不同的坐标模式
  switch (colorType) {
    case 'red':
      coordinates.push([baseOffset, baseOffset], [-baseOffset, -baseOffset]);
      break;
    case 'cyan':
      coordinates.push([baseOffset, -baseOffset], [-baseOffset, baseOffset]);
      break;
    case 'yellow':
      coordinates.push([0, baseOffset], [0, -baseOffset]);
      break;
    case 'purple':
      coordinates.push([baseOffset, 0], [-baseOffset, 0]);
      break;
    case 'orange':
      coordinates.push([baseOffset * 2, baseOffset], [baseOffset, baseOffset * 2]);
      break;
    case 'green':
      coordinates.push([-baseOffset * 2, -baseOffset], [-baseOffset, -baseOffset * 2]);
      break;
    case 'blue':
      coordinates.push([baseOffset * 2, -baseOffset], [-baseOffset * 2, baseOffset]);
      break;
    case 'pink':
      coordinates.push([baseOffset + 1, baseOffset + 1], [-baseOffset - 1, -baseOffset - 1]);
      break;
  }
  
  return coordinates;
}

main()
  .then(async () => {
    await prisma.$disconnect();
  })
  .catch(async (e) => {
    process.env.NODE_ENV === 'development' && console.error('❌ 种子初始化失败:', e);
    await prisma.$disconnect();
    process.exit(1);
  });

"""
Alembic环境配置 - 数据库迁移环境设置
🎯 核心价值：配置Alembic数据库迁移环境
🔧 功能：异步数据库连接、模型导入、迁移配置
⚡ 特性：支持SQLite和PostgreSQL、异步操作
"""

import asyncio
from logging.config import fileConfig

from alembic import context
from sqlalchemy import pool
from sqlalchemy.engine import Connection
from sqlalchemy.ext.asyncio import async_engine_from_config
from sqlmodel import SQLModel

# 导入应用配置和模型
from app.config import settings
from app.models import *  # noqa: F401, F403

# Alembic配置对象
config = context.config

# 设置数据库URL
config.set_main_option("sqlalchemy.url", settings.database.database_url)

# 解释配置文件的Python日志记录
if config.config_file_name is not None:
    fileConfig(config.config_file_name)

# 添加模型的MetaData对象以支持'autogenerate'
target_metadata = SQLModel.metadata


def run_migrations_offline() -> None:
    """
    在'离线'模式下运行迁移

    这将配置上下文，只使用URL而不是Engine，
    尽管这里也需要一个Engine，但我们不创建连接；
    我们只是将URL传递给context.configure()。
    """
    url = config.get_main_option("sqlalchemy.url")
    context.configure(
        url=url,
        target_metadata=target_metadata,
        literal_binds=True,
        dialect_opts={"paramstyle": "named"},
        compare_type=True,
        compare_server_default=True,
    )

    with context.begin_transaction():
        context.run_migrations()


def do_run_migrations(connection: Connection) -> None:
    """运行迁移的核心函数"""
    context.configure(
        connection=connection,
        target_metadata=target_metadata,
        compare_type=True,
        compare_server_default=True,
    )

    with context.begin_transaction():
        context.run_migrations()


async def run_async_migrations() -> None:
    """
    在'在线'模式下运行异步迁移

    在这种情况下，我们需要创建一个Engine并将连接与上下文关联。
    """
    # 获取数据库URL并转换为异步版本
    database_url = settings.database.database_url

    # 转换为异步URL
    if database_url.startswith("sqlite:"):
        async_url = database_url.replace("sqlite:", "sqlite+aiosqlite:")
    elif database_url.startswith("postgresql:"):
        async_url = database_url.replace("postgresql:", "postgresql+asyncpg:")
    else:
        async_url = database_url

    # 创建异步引擎配置
    configuration = config.get_section(config.config_ini_section)
    configuration["sqlalchemy.url"] = async_url

    # 为SQLite添加特殊配置
    if "sqlite" in async_url:
        configuration["sqlalchemy.poolclass"] = "StaticPool"
        configuration["sqlalchemy.connect_args"] = {"check_same_thread": False}

    connectable = async_engine_from_config(
        configuration,
        prefix="sqlalchemy.",
        poolclass=pool.NullPool,
    )

    async with connectable.connect() as connection:
        await connection.run_sync(do_run_migrations)

    await connectable.dispose()


def run_migrations_online() -> None:
    """
    在'在线'模式下运行迁移

    在这种情况下，我们需要创建一个Engine并将连接与上下文关联。
    """
    asyncio.run(run_async_migrations())


if context.is_offline_mode():
    run_migrations_offline()
else:
    run_migrations_online()

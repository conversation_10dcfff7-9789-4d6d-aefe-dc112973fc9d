# Cube1 Group - FastAPI Backend

🎯 **现代化网格数据可视化系统的Python后端实现**

基于FastAPI 0.116.1 + SQLModel + Python 3.11的现代化后端架构，提供高性能的API服务和数据管理功能。

## 🚀 快速开始

### 环境要求

- **Python 3.11** (必需，已升级到3.11)
- Poetry 1.0+ (依赖管理)
- SQLite (开发环境) / PostgreSQL (生产环境)
- Redis 5.0+ (可选，缓存和异步任务)

### 一键启动

```bash
# 克隆项目后进入backend目录
cd backend

# 运行启动脚本
./scripts/start.sh
```

### 手动启动

```bash
# 0. 检查Python环境 (推荐)
poetry run python scripts/check_python_version.py

# 1. 安装依赖
poetry install

# 2. 复制环境变量文件
cp .env.example .env

# 3. 数据库迁移
poetry run alembic upgrade head

# 4. 启动服务
poetry run uvicorn app.main:app --reload
```

### Docker启动

```bash
# 启动完整开发环境
docker-compose up -d

# 仅启动后端服务
docker-compose up backend
```

## 📚 API文档

启动服务后访问：

- **Swagger UI**: http://localhost:8000/docs
- **ReDoc**: http://localhost:8000/redoc
- **健康检查**: http://localhost:8000/health

## 🏗️ 项目结构

```
backend/
├── app/                    # 应用主目录
│   ├── __init__.py
│   ├── main.py            # FastAPI应用入口
│   ├── config.py          # 配置管理
│   ├── database.py        # 数据库连接
│   ├── api/               # API路由
│   │   └── v1/
│   │       ├── router.py  # 主路由
│   │       └── endpoints/ # 具体端点
│   ├── core/              # 核心功能
│   │   ├── exceptions.py  # 异常处理
│   │   ├── logging.py     # 日志配置
│   │   └── security.py    # 安全认证
│   ├── models/            # 数据模型
│   │   ├── user.py        # 用户模型
│   │   ├── project.py     # 项目模型
│   │   └── ...
│   ├── services/          # 业务逻辑
│   ├── tasks/             # 异步任务
│   └── utils/             # 工具函数
├── tests/                 # 测试文件
├── alembic/              # 数据库迁移
├── scripts/              # 脚本文件
├── docker/               # Docker配置
├── pyproject.toml        # Poetry配置
├── Dockerfile
├── docker-compose.yml
└── README.md
```

## 🛠️ 技术栈

### 核心框架
- **FastAPI**: 高性能Web框架
- **SQLModel**: 类型安全的ORM
- **Pydantic**: 数据验证和序列化
- **Alembic**: 数据库迁移

### 数据存储
- **PostgreSQL**: 生产数据库
- **SQLite**: 开发数据库
- **Redis**: 缓存和消息队列

### 认证安全
- **Passlib**: 密码哈希
- **python-jose**: JWT处理
- **bcrypt**: 密码加密

### 异步任务
- **Celery**: 分布式任务队列
- **Redis**: 消息代理

### 开发工具
- **Ruff**: 快速代码检查
- **Black**: 代码格式化
- **isort**: 导入排序
- **MyPy**: 类型检查
- **Pytest**: 测试框架

## 🔧 开发指南

### 代码质量

```bash
# 导入排序
poetry run isort .

# 代码格式化
poetry run black .

# 代码检查
poetry run ruff check .

# 类型检查
poetry run mypy .

# 运行测试
poetry run pytest

# 一键格式化和检查
poetry run isort . && poetry run black . && poetry run ruff check .
```

### 数据库操作

```bash
# 创建迁移
poetry run alembic revision --autogenerate -m "描述"

# 执行迁移
poetry run alembic upgrade head

# 回滚迁移
poetry run alembic downgrade -1

# 查看迁移历史
poetry run alembic history
```

### 异步任务

```bash
# 启动Celery Worker
poetry run celery -A app.tasks.celery_app worker --loglevel=info

# 启动Celery Beat (定时任务)
poetry run celery -A app.tasks.celery_app beat --loglevel=info

# 监控任务 (Flower)
poetry run celery -A app.tasks.celery_app flower
```

## 📊 API端点

### 🏠 根路径和信息
- `GET /` - 应用信息和状态
- `GET /api/v1/` - API版本信息和功能列表

### 📋 项目管理
- `GET /api/v1/projects/` - 获取项目列表
- `POST /api/v1/projects/` - 创建新项目
- `GET /api/v1/projects/{id}` - 获取项目详情
- `PUT /api/v1/projects/{id}` - 更新项目信息
- `DELETE /api/v1/projects/{id}` - 删除项目
- `GET /api/v1/projects/{id}/stats` - 获取项目统计信息
- `POST /api/v1/projects/{id}/export` - 导出项目数据

### 📊 数据管理
- `GET /api/v1/data/colors/` - 获取颜色数据
- `POST /api/v1/data/colors/` - 创建颜色数据
- `GET /api/v1/data/grid/` - 获取网格数据
- `POST /api/v1/data/grid/` - 创建网格数据
- `GET /api/v1/data/versions/` - 获取版本数据
- `POST /api/v1/data/migration/` - 数据迁移操作

### 🏥 系统健康检查
- `GET /health` - 基础健康检查
- `GET /api/v1/health/` - 基础健康状态
- `GET /api/v1/health/detailed` - 详细健康检查
- `GET /api/v1/health/database` - 数据库连接检查
- `GET /api/v1/health/redis` - Redis连接检查（如果配置）

## 🐳 Docker部署

### 开发环境

```bash
# 启动所有服务
docker-compose up -d

# 查看日志
docker-compose logs -f backend

# 停止服务
docker-compose down
```

### 生产环境

```bash
# 构建生产镜像
docker build --target production -t cube1-backend:latest .

# 运行生产容器
docker run -d \
  --name cube1-backend \
  -p 8000:8000 \
  -e DATABASE_URL=postgresql://... \
  -e REDIS_URL=redis://... \
  cube1-backend:latest
```

## 🔒 安全配置

### 环境变量

生产环境请确保设置以下安全配置：

```bash
# 强密钥
SECRET_KEY=your-production-secret-key
JWT_SECRET_KEY=your-production-jwt-secret

# 数据库连接
DATABASE_URL=postgresql://user:pass@host:port/db

# Redis连接
REDIS_URL=redis://user:pass@host:port/db

# CORS限制
CORS_ORIGINS=["https://yourdomain.com"]
```

## 📈 性能监控

### 健康检查端点

- `/health` - 基础状态
- `/api/v1/health/detailed` - 详细状态
- `/api/v1/health/system` - 系统资源

### 日志监控

日志文件位置：
- 应用日志: `logs/app.log`
- 错误日志: `logs/error.log`

### 性能指标

- 请求响应时间
- 数据库查询性能
- Redis缓存命中率
- 系统资源使用率

## 🧪 测试

```bash
# 运行所有测试
poetry run pytest

# 运行特定测试
poetry run pytest tests/test_users.py

# 生成覆盖率报告
poetry run pytest --cov=app --cov-report=html

# 运行性能测试
poetry run pytest tests/performance/
```

## 📝 开发规范

### 代码风格
- 使用isort进行导入排序
- 使用Black进行代码格式化
- 使用Ruff进行代码检查
- 使用MyPy进行类型检查
- 遵循PEP 8规范

### 提交规范
- feat: 新功能
- fix: 修复bug
- docs: 文档更新
- style: 代码格式
- refactor: 重构
- test: 测试相关
- chore: 构建过程或辅助工具的变动

## 🤝 贡献指南

1. Fork项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建Pull Request

## 📄 许可证

MIT License

## 🆘 支持

如有问题，请提交Issue或联系开发团队。

---

**维护者**: Augment Agent
**最后更新**: 2025年7月17日
**技术支持**: 查看项目根目录README.md获取完整信息

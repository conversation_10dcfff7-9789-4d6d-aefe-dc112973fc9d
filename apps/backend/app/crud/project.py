"""
项目CRUD操作
🎯 核心价值：封装项目相关的数据库操作
⚡ 特性：用户项目查询、项目状态管理、关联数据加载
🔧 功能：项目的增删改查和特殊业务操作
"""

from typing import List, Optional

from sqlalchemy import and_, select
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload

from app.crud.base import CRUDBase
from app.models.project import Project, ProjectCreate, ProjectUpdate


class CRUDProject(CRUDBase[Project, ProjectCreate, ProjectUpdate]):
    """项目CRUD操作类"""

    async def get_by_user(
        self,
        db: AsyncSession,
        *,
        user_id: str,
        skip: int = 0,
        limit: int = 100,
        is_active: Optional[bool] = None,
    ) -> List[Project]:
        """获取用户的项目列表"""
        stmt = select(Project).where(Project.user_id == user_id)

        if is_active is not None:
            stmt = stmt.where(Project.is_active == is_active)

        stmt = stmt.offset(skip).limit(limit).order_by(Project.updated_at.desc())

        result = await db.execute(stmt)
        return result.scalars().all()

    async def get_user_project(
        self, db: AsyncSession, *, project_id: str, user_id: str
    ) -> Optional[Project]:
        """获取用户的特定项目"""
        stmt = select(Project).where(
            and_(Project.id == project_id, Project.user_id == user_id)
        )
        result = await db.execute(stmt)
        return result.scalar_one_or_none()

    async def get_with_data(
        self, db: AsyncSession, *, project_id: str
    ) -> Optional[Project]:
        """获取项目及其关联数据"""
        stmt = (
            select(Project)
            .options(
                selectinload(Project.color_data),
                selectinload(Project.grid_data),
                selectinload(Project.black_cell_data),
                selectinload(Project.versions),
                selectinload(Project.settings),

            )
            .where(Project.id == project_id)
        )

        result = await db.execute(stmt)
        return result.scalar_one_or_none()

    async def create_for_user(
        self, db: AsyncSession, *, obj_in: ProjectCreate, user_id: str
    ) -> Project:
        """为用户创建项目"""
        create_data = obj_in.dict()
        create_data["user_id"] = user_id

        db_obj = Project(**create_data)
        db.add(db_obj)
        await db.commit()
        await db.refresh(db_obj)
        return db_obj

    async def update_access_time(
        self, db: AsyncSession, *, project_id: str
    ) -> Optional[Project]:
        """更新项目最后访问时间"""
        from datetime import datetime

        project = await self.get(db, project_id)
        if project:
            project.last_accessed_at = datetime.utcnow()
            db.add(project)
            await db.commit()
            await db.refresh(project)
        return project

    async def activate_project(
        self, db: AsyncSession, *, project_id: str
    ) -> Optional[Project]:
        """激活项目"""
        project = await self.get(db, project_id)
        if project:
            project.is_active = True
            db.add(project)
            await db.commit()
            await db.refresh(project)
        return project

    async def deactivate_project(
        self, db: AsyncSession, *, project_id: str
    ) -> Optional[Project]:
        """停用项目"""
        project = await self.get(db, project_id)
        if project:
            project.is_active = False
            db.add(project)
            await db.commit()
            await db.refresh(project)
        return project

    async def count_user_projects(
        self, db: AsyncSession, *, user_id: str, is_active: Optional[bool] = None
    ) -> int:
        """统计用户项目数量"""
        stmt = select(Project).where(Project.user_id == user_id)

        if is_active is not None:
            stmt = stmt.where(Project.is_active == is_active)

        result = await db.execute(stmt)
        return len(result.scalars().all())

    async def get_recent_projects(
        self, db: AsyncSession, *, user_id: str, limit: int = 5
    ) -> List[Project]:
        """获取用户最近访问的项目"""
        stmt = (
            select(Project)
            .where(
                and_(
                    Project.user_id == user_id,
                    Project.is_active == True,
                    Project.last_accessed_at.isnot(None),
                )
            )
            .order_by(Project.last_accessed_at.desc())
            .limit(limit)
        )

        result = await db.execute(stmt)
        return result.scalars().all()


# 创建项目CRUD实例
crud_project = CRUDProject(Project)

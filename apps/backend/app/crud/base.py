"""
基础CRUD操作类
🎯 核心价值：提供通用的CRUD操作基类，减少重复代码
⚡ 特性：泛型支持、异步操作、类型安全
🔧 功能：标准化的Create、Read、Update、Delete操作
"""

from typing import Any, Dict, Generic, List, Optional, Type, TypeVar, Union
from uuid import uuid4

from fastapi.encoders import jsonable_encoder
from pydantic import BaseModel
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession
from sqlmodel import SQLModel

ModelType = TypeVar("ModelType", bound=SQLModel)
CreateSchemaType = TypeVar("CreateSchemaType", bound=BaseModel)
UpdateSchemaType = TypeVar("UpdateSchemaType", bound=BaseModel)


class CRUDBase(Generic[ModelType, CreateSchemaType, UpdateSchemaType]):
    """基础CRUD操作类"""

    def __init__(self, model: Type[ModelType]):
        """
        初始化CRUD对象

        Args:
            model: SQLModel模型类
        """
        self.model = model

    async def get(self, db: AsyncSession, id: Any) -> Optional[ModelType]:
        """根据ID获取单个记录"""
        stmt = select(self.model).where(self.model.id == id)
        result = await db.execute(stmt)
        return result.scalar_one_or_none()

    async def get_multi(
        self, db: AsyncSession, *, skip: int = 0, limit: int = 100
    ) -> List[ModelType]:
        """获取多个记录"""
        stmt = select(self.model).offset(skip).limit(limit)
        result = await db.execute(stmt)
        return result.scalars().all()

    async def create(self, db: AsyncSession, *, obj_in: CreateSchemaType) -> ModelType:
        """创建新记录"""
        obj_in_data = jsonable_encoder(obj_in)

        # 如果模型有id字段且未提供，自动生成UUID
        if hasattr(self.model, "id") and "id" not in obj_in_data:
            obj_in_data["id"] = str(uuid4())

        db_obj = self.model(**obj_in_data)
        db.add(db_obj)
        await db.commit()
        await db.refresh(db_obj)
        return db_obj

    async def update(
        self,
        db: AsyncSession,
        *,
        db_obj: ModelType,
        obj_in: Union[UpdateSchemaType, Dict[str, Any]],
    ) -> ModelType:
        """更新记录"""
        obj_data = jsonable_encoder(db_obj)

        if isinstance(obj_in, dict):
            update_data = obj_in
        else:
            update_data = obj_in.dict(exclude_unset=True)

        for field in obj_data:
            if field in update_data:
                setattr(db_obj, field, update_data[field])

        db.add(db_obj)
        await db.commit()
        await db.refresh(db_obj)
        return db_obj

    async def remove(self, db: AsyncSession, *, id: Any) -> Optional[ModelType]:
        """删除记录"""
        obj = await self.get(db, id)
        if obj:
            await db.delete(obj)
            await db.commit()
        return obj

    async def count(self, db: AsyncSession) -> int:
        """获取记录总数"""
        stmt = select(self.model)
        result = await db.execute(stmt)
        return len(result.scalars().all())

    async def exists(self, db: AsyncSession, id: Any) -> bool:
        """检查记录是否存在"""
        obj = await self.get(db, id)
        return obj is not None

"""
数据库连接和会话管理 - SQLModel + AsyncIO
🎯 核心价值：异步数据库操作，连接池管理，会话生命周期
🔧 功能：数据库初始化、会话管理、连接池配置、健康检查
⚡ 特性：异步操作、自动重连、连接池优化、事务管理
"""

from contextlib import asynccontextmanager
import logging
from typing import AsyncGenerator, Optional

from sqlalchemy.ext.asyncio import (
    AsyncEngine,
    AsyncSession,
    async_sessionmaker,
    create_async_engine,
)
from sqlalchemy.pool import StaticPool
from sqlmodel import SQLModel, text

from app.config import settings

# 导入所有模型以确保它们被注册到SQLModel.metadata
from app.models import *  # noqa: F401, F403

logger = logging.getLogger(__name__)

# 全局变量
engine: Optional[AsyncEngine] = None
async_session_maker: Optional[async_sessionmaker[AsyncSession]] = None


def get_database_url() -> str:
    """获取数据库连接URL"""
    db_url = settings.database.database_url

    # 转换为异步URL
    if db_url.startswith("sqlite:"):
        return db_url.replace("sqlite:", "sqlite+aiosqlite:")
    elif db_url.startswith("postgresql:"):
        return db_url.replace("postgresql:", "postgresql+asyncpg:")

    return db_url


def create_engine() -> AsyncEngine:
    """创建数据库引擎"""
    database_url = get_database_url()

    # 基础引擎配置
    engine_kwargs = {
        "echo": settings.database.echo_sql,
        "future": True,
    }

    # SQLite特殊配置
    if "sqlite" in database_url:
        engine_kwargs.update(
            {
                "poolclass": StaticPool,
                "connect_args": {
                    "check_same_thread": False,
                    "timeout": 20,
                },
            }
        )
    else:
        # PostgreSQL配置
        engine_kwargs.update(
            {
                "pool_size": settings.database.pool_size,
                "max_overflow": settings.database.max_overflow,
                "pool_timeout": settings.database.pool_timeout,
                "pool_recycle": settings.database.pool_recycle,
                "pool_pre_ping": True,  # 连接前检查
            }
        )

    logger.info(f"创建数据库引擎: {database_url}")
    return create_async_engine(database_url, **engine_kwargs)


async def init_db() -> None:
    """初始化数据库"""
    global engine, async_session_maker

    try:
        # 创建引擎
        engine = create_engine()

        # 创建会话工厂
        async_session_maker = async_sessionmaker(
            engine,
            class_=AsyncSession,
            expire_on_commit=False,
            autoflush=False,
            autocommit=False,
        )

        # 创建所有表
        async with engine.begin() as conn:
            await conn.run_sync(SQLModel.metadata.create_all)
            logger.info("✅ 数据库表创建完成")

        # 测试连接
        await test_connection()
        logger.info("✅ 数据库初始化成功")

    except Exception as e:
        logger.error(f"❌ 数据库初始化失败: {e}")
        raise


async def close_db() -> None:
    """关闭数据库连接"""
    global engine

    if engine:
        await engine.dispose()
        logger.info("✅ 数据库连接已关闭")


async def test_connection() -> bool:
    """测试数据库连接"""
    try:
        async with get_session() as session:
            result = await session.execute(text("SELECT 1"))
            result.scalar()
            return True
    except Exception as e:
        logger.error(f"数据库连接测试失败: {e}")
        return False


@asynccontextmanager
async def get_session() -> AsyncGenerator[AsyncSession, None]:
    """获取数据库会话（上下文管理器）"""
    if not async_session_maker:
        raise RuntimeError("数据库未初始化，请先调用 init_db()")

    async with async_session_maker() as session:
        try:
            yield session
        except Exception:
            await session.rollback()
            raise
        finally:
            await session.close()


async def get_db_session() -> AsyncGenerator[AsyncSession, None]:
    """获取数据库会话（依赖注入）"""
    async with get_session() as session:
        yield session


# === 数据库健康检查 ===


async def health_check() -> dict:
    """数据库健康检查"""
    try:
        start_time = time.time()

        # 测试连接
        is_connected = await test_connection()

        response_time = time.time() - start_time

        if is_connected:
            return {
                "status": "healthy",
                "database": "connected",
                "response_time": f"{response_time:.3f}s",
                "engine_info": {
                    "url": get_database_url().split("@")[-1]
                    if "@" in get_database_url()
                    else get_database_url(),
                    "pool_size": getattr(engine.pool, "size", "N/A")
                    if engine
                    else "N/A",
                    "checked_out": getattr(engine.pool, "checkedout", "N/A")
                    if engine
                    else "N/A",
                },
            }
        else:
            return {
                "status": "unhealthy",
                "database": "disconnected",
                "response_time": f"{response_time:.3f}s",
                "error": "无法连接到数据库",
            }

    except Exception as e:
        return {"status": "unhealthy", "database": "error", "error": str(e)}


# === 事务管理工具 ===


@asynccontextmanager
async def transaction() -> AsyncGenerator[AsyncSession, None]:
    """事务上下文管理器"""
    async with get_session() as session:
        async with session.begin():
            try:
                yield session
            except Exception:
                await session.rollback()
                raise


# === 批量操作工具 ===


async def bulk_insert(session: AsyncSession, objects: list) -> None:
    """批量插入"""
    session.add_all(objects)
    await session.commit()


async def bulk_update(session: AsyncSession, model_class, updates: list[dict]) -> None:
    """批量更新"""
    for update_data in updates:
        stmt = (
            model_class.__table__.update()
            .where(model_class.id == update_data["id"])
            .values(**{k: v for k, v in update_data.items() if k != "id"})
        )

        await session.execute(stmt)

    await session.commit()


# === 分页工具 ===


async def paginate(
    session: AsyncSession,
    query,
    page: int = 1,
    per_page: int = 20,
    max_per_page: int = 100,
) -> dict:
    """分页查询"""
    # 限制每页数量
    per_page = min(per_page, max_per_page)

    # 计算偏移量
    offset = (page - 1) * per_page

    # 获取总数
    count_query = query.statement.with_only_columns(text("COUNT(*)"))
    total = await session.scalar(count_query)

    # 获取数据
    items = await session.execute(query.offset(offset).limit(per_page))
    items = items.scalars().all()

    # 计算分页信息
    total_pages = (total + per_page - 1) // per_page
    has_prev = page > 1
    has_next = page < total_pages

    return {
        "items": items,
        "pagination": {
            "page": page,
            "per_page": per_page,
            "total": total,
            "total_pages": total_pages,
            "has_prev": has_prev,
            "has_next": has_next,
        },
    }


import time

"""
日志配置 - 统一日志管理
🎯 核心价值：标准化日志记录，支持多种输出格式和级别
🔧 功能：日志配置、格式化、文件输出、控制台输出
⚡ 特性：结构化日志、异步日志、日志轮转、性能监控
"""

import logging
import logging.config
from pathlib import Path
import sys
from typing import Any, Dict

from app.config import settings


def setup_logging() -> None:
    """设置应用日志配置"""

    # 创建日志目录
    log_dir = Path("logs")
    log_dir.mkdir(exist_ok=True)

    # 日志配置
    logging_config: Dict[str, Any] = {
        "version": 1,
        "disable_existing_loggers": False,
        "formatters": {
            "default": {
                "format": settings.app.log_format,
                "datefmt": "%Y-%m-%d %H:%M:%S",
            },
            "detailed": {
                "format": (
                    "%(asctime)s - %(name)s - %(levelname)s - "
                    "%(filename)s:%(lineno)d - %(funcName)s - %(message)s"
                ),
                "datefmt": "%Y-%m-%d %H:%M:%S",
            },
            "json": {
                "()": "pythonjsonlogger.jsonlogger.JsonFormatter",
                "format": (
                    "%(asctime)s %(name)s %(levelname)s "
                    "%(filename)s %(lineno)d %(funcName)s %(message)s"
                ),
            },
        },
        "handlers": {
            "console": {
                "class": "logging.StreamHandler",
                "level": settings.app.log_level,
                "formatter": "default",
                "stream": sys.stdout,
            },
            "file": {
                "class": "logging.handlers.RotatingFileHandler",
                "level": "INFO",
                "formatter": "detailed",
                "filename": log_dir / "app.log",
                "maxBytes": 10485760,  # 10MB
                "backupCount": 5,
                "encoding": "utf8",
            },
            "error_file": {
                "class": "logging.handlers.RotatingFileHandler",
                "level": "ERROR",
                "formatter": "detailed",
                "filename": log_dir / "error.log",
                "maxBytes": 10485760,  # 10MB
                "backupCount": 5,
                "encoding": "utf8",
            },
        },
        "loggers": {
            "app": {
                "level": settings.app.log_level,
                "handlers": ["console", "file", "error_file"],
                "propagate": False,
            },
            "sqlalchemy.engine": {
                "level": "INFO" if settings.database.echo_sql else "WARNING",
                "handlers": ["console", "file"],
                "propagate": False,
            },
            "uvicorn": {
                "level": "INFO",
                "handlers": ["console", "file"],
                "propagate": False,
            },
            "fastapi": {
                "level": "INFO",
                "handlers": ["console", "file"],
                "propagate": False,
            },
        },
        "root": {
            "level": settings.app.log_level,
            "handlers": ["console", "file"],
        },
    }

    # 生产环境使用JSON格式
    if settings.is_production:
        logging_config["handlers"]["console"]["formatter"] = "json"
        logging_config["handlers"]["file"]["formatter"] = "json"

    # 应用配置
    logging.config.dictConfig(logging_config)

    # 设置第三方库日志级别
    logging.getLogger("httpx").setLevel(logging.WARNING)
    logging.getLogger("httpcore").setLevel(logging.WARNING)

    logger = logging.getLogger(__name__)
    logger.info(f"日志系统初始化完成 - 级别: {settings.app.log_level}")


class StructuredLogger:
    """结构化日志记录器"""

    def __init__(self, name: str):
        self.logger = logging.getLogger(name)

    def info(self, message: str, **kwargs):
        """记录信息日志"""
        extra = {"extra_data": kwargs} if kwargs else {}
        self.logger.info(message, extra=extra)

    def warning(self, message: str, **kwargs):
        """记录警告日志"""
        extra = {"extra_data": kwargs} if kwargs else {}
        self.logger.warning(message, extra=extra)

    def error(self, message: str, **kwargs):
        """记录错误日志"""
        extra = {"extra_data": kwargs} if kwargs else {}
        self.logger.error(message, extra=extra)

    def debug(self, message: str, **kwargs):
        """记录调试日志"""
        extra = {"extra_data": kwargs} if kwargs else {}
        self.logger.debug(message, extra=extra)

    def critical(self, message: str, **kwargs):
        """记录严重错误日志"""
        extra = {"extra_data": kwargs} if kwargs else {}
        self.logger.critical(message, extra=extra)


def get_logger(name: str) -> StructuredLogger:
    """获取结构化日志记录器"""
    return StructuredLogger(name)


# === 性能监控日志 ===


class PerformanceLogger:
    """性能监控日志记录器"""

    def __init__(self):
        self.logger = logging.getLogger("performance")

    def log_request(
        self, method: str, path: str, status_code: int, duration: float, **kwargs
    ):
        """记录请求性能"""
        self.logger.info(
            f"Request: {method} {path} - {status_code} - {duration:.3f}s",
            extra={
                "method": method,
                "path": path,
                "status_code": status_code,
                "duration": duration,
                **kwargs,
            },
        )

    def log_database_query(
        self, query_type: str, table: str, duration: float, **kwargs
    ):
        """记录数据库查询性能"""
        self.logger.info(
            f"DB Query: {query_type} {table} - {duration:.3f}s",
            extra={
                "query_type": query_type,
                "table": table,
                "duration": duration,
                **kwargs,
            },
        )

    def log_cache_operation(
        self, operation: str, key: str, hit: bool, duration: float, **kwargs
    ):
        """记录缓存操作性能"""
        self.logger.info(
            f"Cache: {operation} {key} - {'HIT' if hit else 'MISS'} - {duration:.3f}s",
            extra={
                "operation": operation,
                "key": key,
                "hit": hit,
                "duration": duration,
                **kwargs,
            },
        )


# 全局性能日志记录器
perf_logger = PerformanceLogger()


# === 日志装饰器 ===

import functools
import time
from typing import Any, Callable


def log_execution_time(logger_name: str = "app"):
    """记录函数执行时间的装饰器"""

    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        async def async_wrapper(*args, **kwargs) -> Any:
            logger = logging.getLogger(logger_name)
            start_time = time.time()

            try:
                result = await func(*args, **kwargs)
                duration = time.time() - start_time
                logger.info(f"{func.__name__} 执行完成 - {duration:.3f}s")
                return result
            except Exception as e:
                duration = time.time() - start_time
                logger.error(f"{func.__name__} 执行失败 - {duration:.3f}s - {str(e)}")
                raise

        @functools.wraps(func)
        def sync_wrapper(*args, **kwargs) -> Any:
            logger = logging.getLogger(logger_name)
            start_time = time.time()

            try:
                result = func(*args, **kwargs)
                duration = time.time() - start_time
                logger.info(f"{func.__name__} 执行完成 - {duration:.3f}s")
                return result
            except Exception as e:
                duration = time.time() - start_time
                logger.error(f"{func.__name__} 执行失败 - {duration:.3f}s - {str(e)}")
                raise

        # 检查是否为异步函数
        if hasattr(func, "__code__") and func.__code__.co_flags & 0x80:
            return async_wrapper
        else:
            return sync_wrapper

    return decorator

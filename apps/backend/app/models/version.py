"""
版本管理模型 - Version Model
🎯 核心价值：项目版本控制和历史管理
🔧 功能：版本快照、变更记录、回滚支持
⚡ 特性：自动版本号、变更追踪、数据完整性
"""

from datetime import datetime
import json
from typing import Any, Dict, List, Optional
from uuid import uuid4

from pydantic import validator
from sqlmodel import Field, Relationship, SQLModel


class VersionBase(SQLModel):
    """版本基础模型"""

    version_number: str = Field(description="版本号 (如: 1.0.0, 1.1.0)")
    version_name: Optional[str] = Field(
        default=None, max_length=200, description="版本名称"
    )
    description: Optional[str] = Field(
        default=None, max_length=1000, description="版本描述"
    )
    changelog: Optional[str] = Field(default=None, description="变更日志 (JSON格式)")
    is_major: bool = Field(default=False, description="是否为主要版本")

    @validator("version_number")
    def validate_version_number(cls, v):
        # 简单的版本号验证 (支持语义化版本)
        import re

        pattern = r"^\d+\.\d+\.\d+(-[a-zA-Z0-9]+)?$"
        if not re.match(pattern, v):
            raise ValueError("版本号格式不正确，应为 x.y.z 或 x.y.z-suffix")
        return v

    @validator("changelog")
    def validate_changelog(cls, v):
        if v is not None:
            try:
                json.loads(v)
                return v
            except json.JSONDecodeError:
                raise ValueError("变更日志必须是有效的JSON格式")
        return v


class Version(VersionBase, table=True):
    """版本数据库模型"""

    __tablename__ = "versions"

    id: str = Field(
        default_factory=lambda: str(uuid4()), primary_key=True, description="版本ID"
    )

    # 关联项目
    project_id: str = Field(foreign_key="projects.id", description="所属项目ID")

    # 版本数据快照
    snapshot_data: str = Field(description="版本数据快照 (JSON格式)")

    # 版本状态
    status: str = Field(
        default="active", description="版本状态 (active, archived, deprecated)"
    )

    # 版本标签
    tags: Optional[str] = Field(default=None, description="版本标签 (JSON数组格式)")

    # 创建者信息
    created_by: Optional[str] = Field(default=None, description="创建者ID")

    # 文件大小 (字节)
    data_size: Optional[int] = Field(default=None, description="数据大小 (字节)")

    # 校验和
    checksum: Optional[str] = Field(default=None, description="数据校验和")

    # 时间戳
    created_at: datetime = Field(default_factory=datetime.utcnow, description="创建时间")

    # 关系映射
    project: "Project" = Relationship(back_populates="versions")

    @validator("status")
    def validate_status(cls, v):
        allowed_statuses = ["active", "archived", "deprecated"]
        if v not in allowed_statuses:
            raise ValueError(f'版本状态必须是以下之一: {", ".join(allowed_statuses)}')
        return v

    @validator("tags")
    def validate_tags(cls, v):
        if v is not None:
            try:
                tags = json.loads(v)
                if not isinstance(tags, list):
                    raise ValueError("标签必须是数组格式")
                return v
            except json.JSONDecodeError:
                raise ValueError("标签数据必须是有效的JSON格式")
        return v


class VersionCreate(VersionBase):
    """创建版本请求模型"""

    project_id: str = Field(description="所属项目ID")
    snapshot_data: str = Field(description="版本数据快照 (JSON格式)")
    tags: Optional[List[str]] = Field(default=None, description="版本标签")

    @validator("snapshot_data")
    def validate_snapshot_data(cls, v):
        try:
            json.loads(v)
            return v
        except json.JSONDecodeError:
            raise ValueError("快照数据必须是有效的JSON格式")


class VersionUpdate(SQLModel):
    """更新版本请求模型"""

    version_name: Optional[str] = Field(
        default=None, max_length=200, description="版本名称"
    )
    description: Optional[str] = Field(
        default=None, max_length=1000, description="版本描述"
    )
    status: Optional[str] = Field(default=None, description="版本状态")
    tags: Optional[List[str]] = Field(default=None, description="版本标签")

    @validator("status")
    def validate_status(cls, v):
        if v is not None:
            allowed_statuses = ["active", "archived", "deprecated"]
            if v not in allowed_statuses:
                raise ValueError(f'版本状态必须是以下之一: {", ".join(allowed_statuses)}')
        return v


class VersionResponse(VersionBase):
    """版本响应模型"""

    id: str = Field(description="版本ID")
    project_id: str = Field(description="所属项目ID")
    status: str = Field(description="版本状态")
    created_by: Optional[str] = Field(description="创建者ID")
    data_size: Optional[int] = Field(description="数据大小")
    checksum: Optional[str] = Field(description="数据校验和")
    created_at: datetime = Field(description="创建时间")

    # 解析后的数据
    parsed_changelog: Optional[List[Dict[str, Any]]] = Field(
        default=None, description="解析后的变更日志"
    )
    parsed_tags: Optional[List[str]] = Field(default=None, description="解析后的标签")

    # 统计信息
    restore_count: Optional[int] = Field(default=0, description="恢复次数")

    def __init__(self, **data):
        super().__init__(**data)
        # 自动解析JSON数据
        if self.changelog:
            try:
                self.parsed_changelog = json.loads(self.changelog)
            except json.JSONDecodeError:
                self.parsed_changelog = []

        if self.tags:
            try:
                self.parsed_tags = json.loads(self.tags)
            except json.JSONDecodeError:
                self.parsed_tags = []


class VersionComparison(SQLModel):
    """版本比较模型"""

    project_id: str = Field(description="项目ID")
    source_version_id: str = Field(description="源版本ID")
    target_version_id: str = Field(description="目标版本ID")
    differences: List[Dict[str, Any]] = Field(description="差异列表")
    similarity_score: float = Field(ge=0.0, le=1.0, description="相似度分数 (0-1)")
    change_summary: Dict[str, Any] = Field(description="变更摘要")
    comparison_timestamp: datetime = Field(
        default_factory=datetime.utcnow, description="比较时间"
    )


class VersionRestore(SQLModel):
    """版本恢复模型"""

    version_id: str = Field(description="要恢复的版本ID")
    create_backup: bool = Field(default=True, description="是否创建当前状态的备份")
    restore_options: Dict[str, Any] = Field(default_factory=dict, description="恢复选项")


class VersionStats(SQLModel):
    """版本统计模型"""

    project_id: str = Field(description="项目ID")
    total_versions: int = Field(description="总版本数")
    active_versions: int = Field(description="活跃版本数")
    archived_versions: int = Field(description="归档版本数")
    deprecated_versions: int = Field(description="废弃版本数")
    latest_version: Optional[str] = Field(description="最新版本号")
    oldest_version: Optional[str] = Field(description="最旧版本号")
    total_data_size: int = Field(description="总数据大小")
    average_version_size: float = Field(description="平均版本大小")
    version_frequency: Dict[str, int] = Field(description="版本创建频率")
    last_created: Optional[datetime] = Field(description="最后创建时间")


class VersionExport(SQLModel):
    """版本导出模型"""

    project_id: str = Field(description="项目ID")
    version_ids: Optional[List[str]] = Field(
        default=None, description="要导出的版本ID列表 (为空则导出所有)"
    )
    include_snapshot_data: bool = Field(default=True, description="是否包含快照数据")
    export_format: str = Field(default="json", description="导出格式")
    compression: bool = Field(default=True, description="是否压缩")


class VersionImport(SQLModel):
    """版本导入模型"""

    project_id: str = Field(description="项目ID")
    version_data: List[VersionCreate] = Field(description="要导入的版本数据")
    import_options: Dict[str, Any] = Field(default_factory=dict, description="导入选项")

    # 导入配置
    conflict_resolution: str = Field(
        default="skip", description="冲突解决策略 (skip, overwrite, rename)"
    )
    validate_data: bool = Field(default=True, description="是否验证数据完整性")
    create_backup: bool = Field(default=True, description="是否创建导入前备份")

    @validator("conflict_resolution")
    def validate_conflict_resolution(cls, v):
        allowed_strategies = ["skip", "overwrite", "rename"]
        if v not in allowed_strategies:
            raise ValueError(f'冲突解决策略必须是以下之一: {", ".join(allowed_strategies)}')
        return v


class VersionBranch(SQLModel):
    """版本分支模型"""

    project_id: str = Field(description="项目ID")
    branch_name: str = Field(description="分支名称")
    base_version_id: str = Field(description="基础版本ID")
    description: Optional[str] = Field(default=None, description="分支描述")
    is_active: bool = Field(default=True, description="是否活跃")
    created_at: datetime = Field(default_factory=datetime.utcnow, description="创建时间")

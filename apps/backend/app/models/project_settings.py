"""
项目设置模型 - ProjectSettings Model
🎯 核心价值：项目配置和样式设置的管理
🔧 功能：主题设置、样式配置、显示选项
⚡ 特性：JSON配置存储、默认值管理、验证机制
"""

from datetime import datetime
import json
from typing import Any, Dict, Optional
from uuid import uuid4

from pydantic import validator
from sqlmodel import Field, Relationship, SQLModel


class ProjectSettingsBase(SQLModel):
    """项目设置基础模型"""

    # 主题设置
    current_theme: str = Field(default="light", description="当前主题")

    # 显示设置
    show_black_cells: bool = Field(default=True, description="是否显示黑色单元格")

    # 样式配置
    matrix_styles: str = Field(default="{}", description="矩阵样式配置 (JSON格式)")



    # 网格设置
    grid_settings: str = Field(default="{}", description="网格设置 (JSON格式)")

    # 交互设置
    interaction_settings: str = Field(default="{}", description="交互设置 (JSON格式)")

    # 性能设置
    performance_settings: str = Field(default="{}", description="性能设置 (JSON格式)")

    @validator("current_theme")
    def validate_theme(cls, v):
        allowed_themes = ["light", "dark", "auto", "high-contrast"]
        if v not in allowed_themes:
            raise ValueError(f'主题必须是以下之一: {", ".join(allowed_themes)}')
        return v

    @validator(
        "matrix_styles",
        "color_scheme",
        "grid_settings",
        "interaction_settings",
        "performance_settings",
    )
    def validate_json_fields(cls, v):
        try:
            json.loads(v)
            return v
        except json.JSONDecodeError:
            raise ValueError("配置数据必须是有效的JSON格式")


class ProjectSettings(ProjectSettingsBase, table=True):
    """项目设置数据库模型"""

    __tablename__ = "project_settings"

    id: str = Field(
        default_factory=lambda: str(uuid4()), primary_key=True, description="设置ID"
    )

    # 关联项目 (一对一关系)
    project_id: str = Field(
        foreign_key="projects.id", unique=True, description="所属项目ID"
    )

    # 自定义设置
    custom_settings: Optional[str] = Field(default=None, description="自定义设置 (JSON格式)")

    # 导入导出设置
    export_settings: str = Field(default="{}", description="导出设置 (JSON格式)")
    import_settings: str = Field(default="{}", description="导入设置 (JSON格式)")

    # 快捷键设置
    keyboard_shortcuts: str = Field(default="{}", description="快捷键设置 (JSON格式)")

    # 时间戳
    created_at: datetime = Field(default_factory=datetime.utcnow, description="创建时间")
    updated_at: datetime = Field(default_factory=datetime.utcnow, description="更新时间")

    # 关系映射
    project: "Project" = Relationship(back_populates="settings")


class ProjectSettingsCreate(ProjectSettingsBase):
    """创建项目设置请求模型"""

    project_id: str = Field(description="所属项目ID")
    custom_settings: Optional[str] = Field(default=None, description="自定义设置 (JSON格式)")


class ProjectSettingsUpdate(SQLModel):
    """更新项目设置请求模型"""

    current_theme: Optional[str] = Field(default=None, description="当前主题")
    show_black_cells: Optional[bool] = Field(default=None, description="是否显示黑色单元格")
    matrix_styles: Optional[str] = Field(default=None, description="矩阵样式配置")
    color_scheme: Optional[str] = Field(default=None, description="颜色方案配置")
    grid_settings: Optional[str] = Field(default=None, description="网格设置")
    interaction_settings: Optional[str] = Field(default=None, description="交互设置")
    performance_settings: Optional[str] = Field(default=None, description="性能设置")
    custom_settings: Optional[str] = Field(default=None, description="自定义设置")

    @validator("current_theme")
    def validate_theme(cls, v):
        if v is not None:
            allowed_themes = ["light", "dark", "auto", "high-contrast"]
            if v not in allowed_themes:
                raise ValueError(f'主题必须是以下之一: {", ".join(allowed_themes)}')
        return v

    @validator(
        "matrix_styles",
        "color_scheme",
        "grid_settings",
        "interaction_settings",
        "performance_settings",
        "custom_settings",
    )
    def validate_json_fields(cls, v):
        if v is not None:
            try:
                json.loads(v)
                return v
            except json.JSONDecodeError:
                raise ValueError("配置数据必须是有效的JSON格式")
        return v


class ProjectSettingsResponse(ProjectSettingsBase):
    """项目设置响应模型"""

    id: str = Field(description="设置ID")
    project_id: str = Field(description="所属项目ID")
    created_at: datetime = Field(description="创建时间")
    updated_at: datetime = Field(description="更新时间")

    # 解析后的配置数据
    parsed_matrix_styles: Optional[Dict[str, Any]] = Field(
        default=None, description="解析后的矩阵样式"
    )
    parsed_color_scheme: Optional[Dict[str, Any]] = Field(
        default=None, description="解析后的颜色方案"
    )
    parsed_grid_settings: Optional[Dict[str, Any]] = Field(
        default=None, description="解析后的网格设置"
    )
    parsed_interaction_settings: Optional[Dict[str, Any]] = Field(
        default=None, description="解析后的交互设置"
    )
    parsed_performance_settings: Optional[Dict[str, Any]] = Field(
        default=None, description="解析后的性能设置"
    )
    parsed_custom_settings: Optional[Dict[str, Any]] = Field(
        default=None, description="解析后的自定义设置"
    )

    def __init__(self, **data):
        super().__init__(**data)
        # 自动解析JSON配置
        self._parse_json_field("matrix_styles", "parsed_matrix_styles")
        self._parse_json_field("color_scheme", "parsed_color_scheme")
        self._parse_json_field("grid_settings", "parsed_grid_settings")
        self._parse_json_field("interaction_settings", "parsed_interaction_settings")
        self._parse_json_field("performance_settings", "parsed_performance_settings")
        self._parse_json_field("custom_settings", "parsed_custom_settings")

    def _parse_json_field(self, source_field: str, target_field: str):
        """解析JSON字段"""
        source_value = getattr(self, source_field, None)
        if source_value:
            try:
                setattr(self, target_field, json.loads(source_value))
            except json.JSONDecodeError:
                setattr(self, target_field, {})


class ThemeConfig(SQLModel):
    """主题配置模型"""

    name: str = Field(description="主题名称")
    display_name: str = Field(description="显示名称")
    colors: Dict[str, str] = Field(description="颜色配置")
    fonts: Dict[str, str] = Field(description="字体配置")
    spacing: Dict[str, Any] = Field(description="间距配置")
    borders: Dict[str, Any] = Field(description="边框配置")
    shadows: Dict[str, str] = Field(description="阴影配置")
    animations: Dict[str, Any] = Field(description="动画配置")





class GridDisplayConfig(SQLModel):
    """网格显示配置模型"""

    show_grid_lines: bool = Field(default=True, description="显示网格线")
    grid_line_color: str = Field(default="#cccccc", description="网格线颜色")
    grid_line_width: float = Field(default=1.0, description="网格线宽度")
    cell_size: int = Field(default=20, description="单元格大小")
    cell_spacing: int = Field(default=1, description="单元格间距")
    show_coordinates: bool = Field(default=False, description="显示坐标")
    highlight_hover: bool = Field(default=True, description="悬停高亮")
    animation_enabled: bool = Field(default=True, description="启用动画")


class InteractionConfig(SQLModel):
    """交互配置模型"""

    click_mode: str = Field(default="single", description="点击模式")
    drag_enabled: bool = Field(default=True, description="启用拖拽")
    zoom_enabled: bool = Field(default=True, description="启用缩放")
    pan_enabled: bool = Field(default=True, description="启用平移")
    keyboard_shortcuts: bool = Field(default=True, description="启用快捷键")
    touch_gestures: bool = Field(default=True, description="启用触摸手势")
    double_click_action: str = Field(default="edit", description="双击动作")


class PerformanceConfig(SQLModel):
    """性能配置模型"""

    enable_virtualization: bool = Field(default=False, description="启用虚拟化")
    batch_size: int = Field(default=100, description="批处理大小")
    debounce_delay: int = Field(default=300, description="防抖延迟(ms)")
    cache_enabled: bool = Field(default=True, description="启用缓存")
    lazy_loading: bool = Field(default=True, description="懒加载")
    memory_limit: int = Field(default=100, description="内存限制(MB)")


class SettingsPreset(SQLModel):
    """设置预设模型"""

    name: str = Field(description="预设名称")
    description: Optional[str] = Field(default=None, description="预设描述")
    category: str = Field(default="custom", description="预设分类")
    settings_data: str = Field(description="设置数据 (JSON格式)")
    is_default: bool = Field(default=False, description="是否为默认预设")
    created_at: datetime = Field(default_factory=datetime.utcnow, description="创建时间")


class SettingsExport(SQLModel):
    """设置导出模型"""

    project_id: str = Field(description="项目ID")
    settings_data: ProjectSettingsResponse = Field(description="设置数据")
    export_format: str = Field(default="json", description="导出格式")
    include_presets: bool = Field(default=False, description="包含预设")
    export_timestamp: datetime = Field(
        default_factory=datetime.utcnow, description="导出时间"
    )


class SettingsImport(SQLModel):
    """设置导入模型"""

    project_id: str = Field(description="项目ID")
    settings_data: ProjectSettingsCreate = Field(description="设置数据")
    import_options: Dict[str, Any] = Field(default_factory=dict, description="导入选项")

    # 导入配置
    merge_strategy: str = Field(default="replace", description="合并策略")
    validate_settings: bool = Field(default=True, description="验证设置")
    create_backup: bool = Field(default=True, description="创建备份")

"""
网格数据模型 - GridData Model
🎯 核心价值：33x33网格矩阵的数据模型
🔧 功能：网格状态存储、层级管理、坐标映射
⚡ 特性：JSON数据存储、网格验证、项目关联
"""

from datetime import datetime
import json
from typing import Any, Dict, List, Optional
from uuid import uuid4

from pydantic import validator
from sqlmodel import Field, Relationship, SQLModel


class GridDataBase(SQLModel):
    """网格数据基础模型"""

    level: int = Field(ge=1, le=4, description="层级 (1-4)")
    grid_size: int = Field(default=33, ge=10, le=100, description="网格大小")
    grid_state: str = Field(description="网格状态数据 (JSON格式)")
    meta_data: str = Field(default="{}", description="元数据 (JSON格式)")

    @validator("grid_state")
    def validate_grid_state(cls, v):
        try:
            state = json.loads(v)
            if not isinstance(state, dict):
                raise ValueError("网格状态必须是对象格式")
            return v
        except json.JSONDecodeError:
            raise ValueError("网格状态数据必须是有效的JSON格式")

    @validator("meta_data")
    def validate_meta_data(cls, v):
        try:
            json.loads(v)
            return v
        except json.JSONDecodeError:
            raise ValueError("元数据必须是有效的JSON格式")


class GridData(GridDataBase, table=True):
    """网格数据数据库模型"""

    __tablename__ = "grid_data"

    id: str = Field(
        default_factory=lambda: str(uuid4()), primary_key=True, description="网格数据ID"
    )

    # 关联项目
    project_id: str = Field(foreign_key="projects.id", description="所属项目ID")

    # 版本信息
    version: str = Field(default="1.0", description="数据版本")

    # 状态标记
    is_active: bool = Field(default=True, description="是否激活")

    # 时间戳
    created_at: datetime = Field(default_factory=datetime.utcnow, description="创建时间")
    updated_at: datetime = Field(default_factory=datetime.utcnow, description="更新时间")

    # 关系映射
    project: "Project" = Relationship(back_populates="grid_data")


class GridDataCreate(GridDataBase):
    """创建网格数据请求模型"""

    project_id: str = Field(description="所属项目ID")
    version: Optional[str] = Field(default="1.0", description="数据版本")


class GridDataUpdate(SQLModel):
    """更新网格数据请求模型"""

    grid_state: Optional[str] = Field(default=None, description="网格状态数据 (JSON格式)")
    meta_data: Optional[str] = Field(default=None, description="元数据 (JSON格式)")
    version: Optional[str] = Field(default=None, description="数据版本")
    is_active: Optional[bool] = Field(default=None, description="是否激活")

    @validator("grid_state")
    def validate_grid_state(cls, v):
        if v is not None:
            try:
                state = json.loads(v)
                if not isinstance(state, dict):
                    raise ValueError("网格状态必须是对象格式")
                return v
            except json.JSONDecodeError:
                raise ValueError("网格状态数据必须是有效的JSON格式")
        return v

    @validator("meta_data")
    def validate_meta_data_update(cls, v):
        if v is not None:
            try:
                json.loads(v)
                return v
            except json.JSONDecodeError:
                raise ValueError("元数据必须是有效的JSON格式")
        return v


class GridDataResponse(GridDataBase):
    """网格数据响应模型"""

    id: str = Field(description="网格数据ID")
    project_id: str = Field(description="所属项目ID")
    version: str = Field(description="数据版本")
    is_active: bool = Field(description="是否激活")
    created_at: datetime = Field(description="创建时间")
    updated_at: datetime = Field(description="更新时间")

    # 解析后的数据
    parsed_grid_state: Optional[Dict[str, Any]] = Field(
        default=None, description="解析后的网格状态"
    )
    parsed_meta_data: Optional[Dict[str, Any]] = Field(
        default=None, description="解析后的元数据"
    )

    def __init__(self, **data):
        super().__init__(**data)
        # 自动解析JSON数据
        if self.grid_state:
            try:
                self.parsed_grid_state = json.loads(self.grid_state)
            except json.JSONDecodeError:
                self.parsed_grid_state = {}

        if self.meta_data:
            try:
                self.parsed_meta_data = json.loads(self.meta_data)
            except json.JSONDecodeError:
                self.parsed_meta_data = {}


class GridCellUpdate(SQLModel):
    """单个网格单元更新模型"""

    x: int = Field(ge=0, description="X坐标")
    y: int = Field(ge=0, description="Y坐标")
    color: Optional[str] = Field(default=None, description="颜色")
    state: Optional[str] = Field(default=None, description="状态")
    level: int = Field(ge=1, le=4, description="层级")


class GridBatchUpdate(SQLModel):
    """批量网格更新模型"""

    project_id: str = Field(description="项目ID")
    level: int = Field(ge=1, le=4, description="层级")
    updates: List[GridCellUpdate] = Field(description="更新列表")

    @validator("updates")
    def validate_updates(cls, v):
        if len(v) > 1089:  # 33x33 = 1089
            raise ValueError("更新数量不能超过网格总数")
        return v


class GridDataStats(SQLModel):
    """网格数据统计模型"""

    project_id: str = Field(description="项目ID")
    level: int = Field(description="层级")
    total_cells: int = Field(description="总单元格数")
    filled_cells: int = Field(description="已填充单元格数")
    empty_cells: int = Field(description="空单元格数")
    colors_used: List[str] = Field(description="使用的颜色列表")
    fill_percentage: float = Field(description="填充百分比")
    last_updated: datetime = Field(description="最后更新时间")


class GridDataExport(SQLModel):
    """网格数据导出模型"""

    project_id: str = Field(description="项目ID")
    grid_data: List[GridDataResponse] = Field(description="网格数据列表")
    export_format: str = Field(default="json", description="导出格式 (json, csv, xlsx)")
    include_metadata: bool = Field(default=True, description="是否包含元数据")
    export_timestamp: datetime = Field(
        default_factory=datetime.utcnow, description="导出时间"
    )


class GridDataImport(SQLModel):
    """网格数据导入模型"""

    project_id: str = Field(description="项目ID")
    grid_data: List[GridDataCreate] = Field(description="要导入的网格数据")
    import_options: Dict[str, Any] = Field(default_factory=dict, description="导入选项")

    # 导入配置
    merge_strategy: str = Field(
        default="replace", description="合并策略 (replace, merge, append)"
    )
    validate_grid_size: bool = Field(default=True, description="是否验证网格大小")
    create_backup: bool = Field(default=True, description="是否创建备份")

    @validator("merge_strategy")
    def validate_merge_strategy(cls, v):
        allowed_strategies = ["replace", "merge", "append"]
        if v not in allowed_strategies:
            raise ValueError(f'合并策略必须是以下之一: {", ".join(allowed_strategies)}')
        return v


class GridSnapshot(SQLModel):
    """网格快照模型"""

    project_id: str = Field(description="项目ID")
    level: int = Field(ge=1, le=4, description="层级")
    snapshot_name: str = Field(description="快照名称")
    grid_state: str = Field(description="网格状态 (JSON)")
    description: Optional[str] = Field(default=None, description="快照描述")
    created_at: datetime = Field(default_factory=datetime.utcnow, description="创建时间")


class GridComparison(SQLModel):
    """网格比较模型"""

    project_id: str = Field(description="项目ID")
    level: int = Field(description="层级")
    source_grid_id: str = Field(description="源网格ID")
    target_grid_id: str = Field(description="目标网格ID")
    differences: List[Dict[str, Any]] = Field(description="差异列表")
    similarity_percentage: float = Field(description="相似度百分比")
    comparison_timestamp: datetime = Field(
        default_factory=datetime.utcnow, description="比较时间"
    )

"""
黑色单元格数据模型 - BlackCellData Model
🎯 核心价值：黑色格子数据的专门管理
🔧 功能：黑色单元格坐标存储、层级管理、状态控制
⚡ 特性：坐标验证、批量操作、项目关联
"""

from datetime import datetime
import json
from typing import List, Optional
from uuid import uuid4

from pydantic import validator
from sqlmodel import Field, Relationship, SQLModel


class BlackCellDataBase(SQLModel):
    """黑色单元格数据基础模型"""

    level: int = Field(ge=1, le=4, description="层级 (1-4)")
    coordinates: str = Field(description="黑色单元格坐标 (JSON格式: [[x1,y1], [x2,y2], ...])")
    cell_type: str = Field(default="black", description="单元格类型")
    is_visible: bool = Field(default=True, description="是否可见")

    @validator("coordinates")
    def validate_coordinates(cls, v):
        try:
            coords = json.loads(v)
            if not isinstance(coords, list):
                raise ValueError("坐标必须是数组格式")

            for coord in coords:
                if not isinstance(coord, list) or len(coord) != 2:
                    raise ValueError("每个坐标必须是[x, y]格式")
                if not all(isinstance(c, int) and c >= 0 for c in coord):
                    raise ValueError("坐标值必须是非负整数")
                if coord[0] >= 33 or coord[1] >= 33:  # 33x33网格
                    raise ValueError("坐标值不能超过网格范围(0-32)")

            return v
        except json.JSONDecodeError:
            raise ValueError("坐标数据必须是有效的JSON格式")

    @validator("cell_type")
    def validate_cell_type(cls, v):
        allowed_types = ["black", "blocked", "special"]
        if v not in allowed_types:
            raise ValueError(f'单元格类型必须是以下之一: {", ".join(allowed_types)}')
        return v


class BlackCellData(BlackCellDataBase, table=True):
    """黑色单元格数据数据库模型"""

    __tablename__ = "black_cell_data"

    id: str = Field(
        default_factory=lambda: str(uuid4()), primary_key=True, description="黑色单元格数据ID"
    )

    # 关联项目
    project_id: str = Field(foreign_key="projects.id", description="所属项目ID")

    # 优先级
    priority: int = Field(default=0, description="优先级 (数字越大优先级越高)")

    # 时间戳
    created_at: datetime = Field(default_factory=datetime.utcnow, description="创建时间")
    updated_at: datetime = Field(default_factory=datetime.utcnow, description="更新时间")

    # 关系映射
    project: "Project" = Relationship(back_populates="black_cell_data")


class BlackCellDataCreate(BlackCellDataBase):
    """创建黑色单元格数据请求模型"""

    project_id: str = Field(description="所属项目ID")
    priority: Optional[int] = Field(default=0, description="优先级")


class BlackCellDataUpdate(SQLModel):
    """更新黑色单元格数据请求模型"""

    coordinates: Optional[str] = Field(default=None, description="黑色单元格坐标 (JSON格式)")
    cell_type: Optional[str] = Field(default=None, description="单元格类型")
    is_visible: Optional[bool] = Field(default=None, description="是否可见")
    priority: Optional[int] = Field(default=None, description="优先级")

    @validator("coordinates")
    def validate_coordinates(cls, v):
        if v is not None:
            try:
                coords = json.loads(v)
                if not isinstance(coords, list):
                    raise ValueError("坐标必须是数组格式")

                for coord in coords:
                    if not isinstance(coord, list) or len(coord) != 2:
                        raise ValueError("每个坐标必须是[x, y]格式")
                    if not all(isinstance(c, int) and c >= 0 for c in coord):
                        raise ValueError("坐标值必须是非负整数")
                    if coord[0] >= 33 or coord[1] >= 33:
                        raise ValueError("坐标值不能超过网格范围(0-32)")

                return v
            except json.JSONDecodeError:
                raise ValueError("坐标数据必须是有效的JSON格式")
        return v

    @validator("cell_type")
    def validate_cell_type(cls, v):
        if v is not None:
            allowed_types = ["black", "blocked", "special"]
            if v not in allowed_types:
                raise ValueError(f'单元格类型必须是以下之一: {", ".join(allowed_types)}')
        return v


class BlackCellDataResponse(BlackCellDataBase):
    """黑色单元格数据响应模型"""

    id: str = Field(description="黑色单元格数据ID")
    project_id: str = Field(description="所属项目ID")
    priority: int = Field(description="优先级")
    created_at: datetime = Field(description="创建时间")
    updated_at: datetime = Field(description="更新时间")

    # 解析后的坐标数据
    parsed_coordinates: Optional[List[List[int]]] = Field(
        default=None, description="解析后的坐标数据"
    )

    # 统计信息
    cell_count: Optional[int] = Field(default=None, description="单元格数量")

    def __init__(self, **data):
        super().__init__(**data)
        # 自动解析坐标数据
        if self.coordinates:
            try:
                self.parsed_coordinates = json.loads(self.coordinates)
                self.cell_count = len(self.parsed_coordinates)
            except json.JSONDecodeError:
                self.parsed_coordinates = []
                self.cell_count = 0


class BlackCellBatch(SQLModel):
    """批量黑色单元格操作模型"""

    project_id: str = Field(description="项目ID")
    level: int = Field(ge=1, le=4, description="层级")
    operations: List[dict] = Field(description="操作列表")

    @validator("operations")
    def validate_operations(cls, v):
        for op in v:
            if "action" not in op:
                raise ValueError("每个操作必须包含action字段")

            action = op["action"]
            if action not in ["add", "remove", "toggle", "clear"]:
                raise ValueError("action必须是add、remove、toggle或clear之一")

            if action in ["add", "remove", "toggle"] and "coordinates" not in op:
                raise ValueError(f"{action}操作必须包含coordinates字段")

        return v


class BlackCellPattern(SQLModel):
    """黑色单元格模式模型"""

    name: str = Field(description="模式名称")
    description: Optional[str] = Field(default=None, description="模式描述")
    pattern_data: str = Field(description="模式数据 (JSON格式)")
    grid_size: int = Field(default=33, description="适用的网格大小")
    category: str = Field(default="custom", description="模式分类")

    @validator("pattern_data")
    def validate_pattern_data(cls, v):
        try:
            pattern = json.loads(v)
            if not isinstance(pattern, list):
                raise ValueError("模式数据必须是数组格式")

            for coord in pattern:
                if not isinstance(coord, list) or len(coord) != 2:
                    raise ValueError("每个坐标必须是[x, y]格式")
                if not all(isinstance(c, int) and c >= 0 for c in coord):
                    raise ValueError("坐标值必须是非负整数")

            return v
        except json.JSONDecodeError:
            raise ValueError("模式数据必须是有效的JSON格式")


class BlackCellStats(SQLModel):
    """黑色单元格统计模型"""

    project_id: str = Field(description="项目ID")
    level: int = Field(description="层级")
    total_black_cells: int = Field(description="总黑色单元格数")
    visible_black_cells: int = Field(description="可见黑色单元格数")
    hidden_black_cells: int = Field(description="隐藏黑色单元格数")
    cells_by_type: dict = Field(description="按类型分组的单元格数量")
    coverage_percentage: float = Field(description="覆盖百分比")
    density_map: Optional[dict] = Field(default=None, description="密度分布图")
    last_updated: datetime = Field(description="最后更新时间")


class BlackCellExport(SQLModel):
    """黑色单元格数据导出模型"""

    project_id: str = Field(description="项目ID")
    black_cell_data: List[BlackCellDataResponse] = Field(description="黑色单元格数据列表")
    export_format: str = Field(default="json", description="导出格式")
    include_patterns: bool = Field(default=False, description="是否包含模式数据")
    export_timestamp: datetime = Field(
        default_factory=datetime.utcnow, description="导出时间"
    )


class BlackCellImport(SQLModel):
    """黑色单元格数据导入模型"""

    project_id: str = Field(description="项目ID")
    black_cell_data: List[BlackCellDataCreate] = Field(description="要导入的黑色单元格数据")
    import_options: dict = Field(default_factory=dict, description="导入选项")

    # 导入配置
    merge_strategy: str = Field(
        default="replace", description="合并策略 (replace, merge, add)"
    )
    validate_coordinates: bool = Field(default=True, description="是否验证坐标")
    remove_duplicates: bool = Field(default=True, description="是否移除重复坐标")

    @validator("merge_strategy")
    def validate_merge_strategy(cls, v):
        allowed_strategies = ["replace", "merge", "add"]
        if v not in allowed_strategies:
            raise ValueError(f'合并策略必须是以下之一: {", ".join(allowed_strategies)}')
        return v

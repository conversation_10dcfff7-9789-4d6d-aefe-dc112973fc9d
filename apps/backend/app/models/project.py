"""
项目模型 - Project Model
🎯 核心价值：项目管理和数据组织的核心模型
🔧 功能：项目信息存储、用户关联、数据关联管理
⚡ 特性：SQLModel集成、关系映射、数据统计
"""

from datetime import datetime
from typing import List, Optional
from uuid import uuid4

from pydantic import validator
from sqlmodel import Field, Relationship, SQLModel


class ProjectBase(SQLModel):
    """项目基础模型"""

    name: str = Field(max_length=200, description="项目名称")
    description: Optional[str] = Field(
        default=None, max_length=1000, description="项目描述"
    )

    @validator("name")
    def validate_name(cls, v):
        if not v or len(v.strip()) == 0:
            raise ValueError("项目名称不能为空")
        return v.strip()


class Project(ProjectBase, table=True):
    """项目数据库模型"""

    __tablename__ = "projects"

    id: str = Field(
        default_factory=lambda: str(uuid4()), primary_key=True, description="项目ID"
    )

    # 项目创建者信息（可选，用于记录但不强制关联）
    creator_name: Optional[str] = Field(
        default=None, max_length=100, description="项目创建者名称"
    )

    # 项目状态
    is_active: bool = Field(default=True, description="是否激活")

    # 项目配置
    grid_size: int = Field(default=33, description="网格大小")
    color_count: int = Field(default=8, description="颜色数量")
    level_count: int = Field(default=4, description="层级数量")

    # 时间戳
    created_at: datetime = Field(default_factory=datetime.utcnow, description="创建时间")
    updated_at: datetime = Field(default_factory=datetime.utcnow, description="更新时间")

    # 最后访问时间
    last_accessed_at: Optional[datetime] = Field(default=None, description="最后访问时间")

    # 关系映射（移除用户关联）

    color_data: List["ColorData"] = Relationship(back_populates="project")

    grid_data: List["GridData"] = Relationship(back_populates="project")

    black_cell_data: List["BlackCellData"] = Relationship(back_populates="project")

    versions: List["Version"] = Relationship(back_populates="project")

    settings: Optional["ProjectSettings"] = Relationship(back_populates="project")




class ProjectCreate(ProjectBase):
    """创建项目请求模型"""

    name: str = Field(max_length=200, description="项目名称（必填）")
    description: Optional[str] = Field(
        default=None, max_length=1000, description="项目描述"
    )

    # 可选的初始配置
    grid_size: Optional[int] = Field(
        default=33, ge=10, le=100, description="网格大小（10-100）"
    )
    color_count: Optional[int] = Field(default=8, ge=1, le=16, description="颜色数量（1-16）")
    level_count: Optional[int] = Field(default=4, ge=1, le=10, description="层级数量（1-10）")


class ProjectUpdate(SQLModel):
    """更新项目请求模型"""

    name: Optional[str] = Field(default=None, max_length=200, description="项目名称")
    description: Optional[str] = Field(
        default=None, max_length=1000, description="项目描述"
    )
    is_active: Optional[bool] = Field(default=None, description="是否激活")

    # 配置更新
    grid_size: Optional[int] = Field(
        default=None, ge=10, le=100, description="网格大小（10-100）"
    )
    color_count: Optional[int] = Field(
        default=None, ge=1, le=16, description="颜色数量（1-16）"
    )
    level_count: Optional[int] = Field(
        default=None, ge=1, le=10, description="层级数量（1-10）"
    )

    @validator("name")
    def validate_name(cls, v):
        if v is not None and len(v.strip()) == 0:
            raise ValueError("项目名称不能为空字符串")
        return v.strip() if v else v


class ProjectResponse(ProjectBase):
    """项目响应模型"""

    id: str = Field(description="项目ID")
    creator_name: Optional[str] = Field(description="项目创建者名称")
    is_active: bool = Field(description="是否激活")

    # 项目配置
    grid_size: int = Field(description="网格大小")
    color_count: int = Field(description="颜色数量")
    level_count: int = Field(description="层级数量")

    # 时间戳
    created_at: datetime = Field(description="创建时间")
    updated_at: datetime = Field(description="更新时间")
    last_accessed_at: Optional[datetime] = Field(description="最后访问时间")

    # 统计信息
    color_data_count: Optional[int] = Field(default=None, description="颜色数据数量")
    grid_data_count: Optional[int] = Field(default=None, description="网格数据数量")
    version_count: Optional[int] = Field(default=None, description="版本数量")

    # 创建者信息（可选）
    creator_info: Optional[str] = Field(default=None, description="创建者信息")


class ProjectStats(SQLModel):
    """项目统计信息模型"""

    project_id: str = Field(description="项目ID")

    # 数据统计
    total_cells: int = Field(description="总单元格数")
    colored_cells: int = Field(description="已着色单元格数")
    black_cells: int = Field(description="黑色单元格数")

    # 颜色统计
    color_distribution: dict = Field(description="颜色分布")
    level_distribution: dict = Field(description="层级分布")

    # 版本统计
    version_count: int = Field(description="版本数量")
    latest_version: Optional[str] = Field(description="最新版本")

    # 活动统计
    last_modified: datetime = Field(description="最后修改时间")
    modification_count: int = Field(description="修改次数")


class ProjectExport(SQLModel):
    """项目导出模型"""

    project: ProjectResponse = Field(description="项目信息")
    color_data: List[dict] = Field(description="颜色数据")
    grid_data: List[dict] = Field(description="网格数据")
    black_cell_data: List[dict] = Field(description="黑色单元格数据")
    versions: List[dict] = Field(description="版本数据")
    settings: Optional[dict] = Field(description="项目设置")


    export_timestamp: datetime = Field(
        default_factory=datetime.utcnow, description="导出时间"
    )
    export_version: str = Field(default="1.0", description="导出格式版本")


class ProjectImport(SQLModel):
    """项目导入模型"""

    project_data: ProjectExport = Field(description="项目数据")
    import_options: dict = Field(default_factory=dict, description="导入选项")

    # 导入配置
    overwrite_existing: bool = Field(default=False, description="是否覆盖现有数据")
    create_backup: bool = Field(default=True, description="是否创建备份")
    validate_data: bool = Field(default=True, description="是否验证数据")

"""
API v1 主路由 - 路由聚合和管理
🎯 核心价值：统一管理所有API v1路由，提供清晰的API结构
🔧 功能：路由注册、标签管理、版本控制
⚡ 特性：模块化路由、自动文档生成、统一前缀
"""

from fastapi import APIRouter

from app.api.v1.endpoints import (  # auth,  # 已移除; users,  # 已移除
    data,
    health,
    projects,
)

# 创建API v1路由器
api_router = APIRouter()

# === 认证相关路由（已移除） ===
# api_router.include_router(auth.router, ...)

# === 用户管理路由（已移除） ===
# api_router.include_router(users.router, ...)

# === 项目管理路由 ===
api_router.include_router(
    projects.router,
    prefix="/projects",
    tags=["项目管理 Projects"],
)

# === 数据管理路由 ===
api_router.include_router(
    data.router,
    prefix="/data",
    tags=["数据管理 Data"],
)

# === 系统健康检查路由 ===
api_router.include_router(
    health.router,
    prefix="/health",
    tags=["系统健康 Health"],
)


# === API信息端点 ===
@api_router.get("/", summary="API信息", tags=["API信息"])
async def api_info():
    """获取API版本信息"""
    return {
        "name": "Cube1 Group Backend API",
        "version": "v1",
        "description": "8进制编码辅助系统 Backend API v1",
        "endpoints": {
            # "auth": "/auth - 用户认证和授权",  # 已移除
            # "users": "/users - 用户管理",  # 已移除
            "projects": "/projects - 项目管理",
            "data": "/data - 数据管理（颜色、网格、版本等）",
            "health": "/health - 系统健康检查",
        },
        "documentation": {
            "swagger": "/docs",
            "redoc": "/redoc",
        },
        "features": [
            # "JWT认证",  # 已移除
            # "用户管理",  # 已移除
            "项目管理",
            "33x33网格数据",
            "8色彩分类系统",
            "4层级架构",
            "版本控制",
            "数据导入导出",
            "异步处理",
            "Redis缓存",
        ],
    }

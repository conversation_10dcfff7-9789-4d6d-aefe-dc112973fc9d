"""
健康检查API端点 - Health Check Endpoints
🎯 核心价值：系统健康状态监控和诊断
🔧 功能：数据库连接检查、Redis连接检查、系统状态监控
⚡ 特性：快速响应、详细诊断、性能监控
"""

from datetime import datetime
import time
from typing import Any, Dict

from fastapi import APIRouter, Depends
import psutil
from sqlalchemy.ext.asyncio import AsyncSession

from app.config import settings
from app.core.logging import get_logger
from app.database import get_db_session
from app.database import health_check as db_health_check

router = APIRouter()
logger = get_logger(__name__)


@router.get("/", summary="基础健康检查")
async def basic_health_check():
    """
    基础健康检查

    返回系统基本状态信息
    """
    return {
        "status": "healthy",
        "service": "cube1-backend",
        "version": "0.1.0",
        "timestamp": datetime.utcnow().isoformat(),
        "environment": settings.environment,
    }


@router.get("/detailed", summary="详细健康检查")
async def detailed_health_check(db: AsyncSession = Depends(get_db_session)):
    """
    详细健康检查

    检查所有系统组件的健康状态
    """
    start_time = time.time()

    # 系统信息
    system_info = {
        "cpu_percent": psutil.cpu_percent(interval=1),
        "memory_percent": psutil.virtual_memory().percent,
        "disk_percent": psutil.disk_usage("/").percent,
        "boot_time": datetime.fromtimestamp(psutil.boot_time()).isoformat(),
    }

    # 数据库健康检查
    db_health = await db_health_check()

    # Redis健康检查（如果配置了Redis）
    redis_health = await check_redis_health()

    # 计算响应时间
    response_time = time.time() - start_time

    # 确定整体状态
    overall_status = "healthy"
    if db_health.get("status") != "healthy":
        overall_status = "unhealthy"
    if redis_health.get("status") != "healthy":
        overall_status = "degraded"

    return {
        "status": overall_status,
        "timestamp": datetime.utcnow().isoformat(),
        "response_time": f"{response_time:.3f}s",
        "components": {
            "database": db_health,
            "redis": redis_health,
            "system": system_info,
        },
        "service_info": {
            "name": "cube1-backend",
            "version": "0.1.0",
            "environment": settings.environment,
            "debug": settings.app.debug,
        },
    }


@router.get("/database", summary="数据库健康检查")
async def database_health_check():
    """
    数据库健康检查

    专门检查数据库连接状态
    """
    db_health = await db_health_check()

    return {
        "component": "database",
        "timestamp": datetime.utcnow().isoformat(),
        **db_health,
    }


@router.get("/redis", summary="Redis健康检查")
async def redis_health_check():
    """
    Redis健康检查

    检查Redis连接状态
    """
    redis_health = await check_redis_health()

    return {
        "component": "redis",
        "timestamp": datetime.utcnow().isoformat(),
        **redis_health,
    }


@router.get("/system", summary="系统资源检查")
async def system_health_check():
    """
    系统资源检查

    检查CPU、内存、磁盘使用情况
    """
    try:
        # CPU信息
        cpu_info = {
            "percent": psutil.cpu_percent(interval=1),
            "count": psutil.cpu_count(),
            "count_logical": psutil.cpu_count(logical=True),
        }

        # 内存信息
        memory = psutil.virtual_memory()
        memory_info = {
            "total": memory.total,
            "available": memory.available,
            "percent": memory.percent,
            "used": memory.used,
            "free": memory.free,
        }

        # 磁盘信息
        disk = psutil.disk_usage("/")
        disk_info = {
            "total": disk.total,
            "used": disk.used,
            "free": disk.free,
            "percent": (disk.used / disk.total) * 100,
        }

        # 网络信息
        network = psutil.net_io_counters()
        network_info = {
            "bytes_sent": network.bytes_sent,
            "bytes_recv": network.bytes_recv,
            "packets_sent": network.packets_sent,
            "packets_recv": network.packets_recv,
        }

        # 进程信息
        process_info = {
            "count": len(psutil.pids()),
            "boot_time": datetime.fromtimestamp(psutil.boot_time()).isoformat(),
        }

        # 确定状态
        status = "healthy"
        if cpu_info["percent"] > 90:
            status = "warning"
        if memory_info["percent"] > 90:
            status = "warning"
        if disk_info["percent"] > 90:
            status = "critical"

        return {
            "status": status,
            "timestamp": datetime.utcnow().isoformat(),
            "cpu": cpu_info,
            "memory": memory_info,
            "disk": disk_info,
            "network": network_info,
            "process": process_info,
        }

    except Exception as e:
        logger.error(f"系统健康检查失败: {e}")
        return {
            "status": "error",
            "timestamp": datetime.utcnow().isoformat(),
            "error": str(e),
        }


async def check_redis_health() -> Dict[str, Any]:
    """检查Redis连接健康状态"""
    try:
        import redis.asyncio as redis

        # 创建Redis连接
        redis_client = redis.from_url(
            settings.redis.redis_url, encoding="utf-8", decode_responses=True
        )

        start_time = time.time()

        # 测试连接
        await redis_client.ping()

        # 测试基本操作
        test_key = "health_check_test"
        await redis_client.set(test_key, "test_value", ex=10)
        test_value = await redis_client.get(test_key)
        await redis_client.delete(test_key)

        response_time = time.time() - start_time

        # 获取Redis信息
        info = await redis_client.info()

        await redis_client.close()

        return {
            "status": "healthy",
            "response_time": f"{response_time:.3f}s",
            "redis_version": info.get("redis_version", "unknown"),
            "connected_clients": info.get("connected_clients", 0),
            "used_memory": info.get("used_memory_human", "unknown"),
            "uptime": info.get("uptime_in_seconds", 0),
        }

    except ImportError:
        return {
            "status": "not_configured",
            "message": "Redis客户端未安装",
        }
    except Exception as e:
        logger.error(f"Redis健康检查失败: {e}")
        return {
            "status": "unhealthy",
            "error": str(e),
        }

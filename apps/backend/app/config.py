"""
配置管理 - Pydantic Settings
🎯 核心价值：类型安全的配置管理，支持环境变量和配置文件
🔧 功能：数据库配置、Redis配置、应用配置
⚡ 特性：自动类型验证、环境变量支持、默认值管理
"""

from typing import Optional

from pydantic import Field
from pydantic_settings import BaseSettings, SettingsConfigDict


class DatabaseSettings(BaseSettings):
    """数据库配置"""

    # SQLite (开发环境)
    sqlite_url: str = Field(
        default="sqlite:///./cube1_dev.db", description="SQLite数据库URL"
    )

    # PostgreSQL (生产环境)
    postgres_url: Optional[str] = Field(default=None, description="PostgreSQL数据库URL")

    # 数据库连接池配置
    pool_size: int = Field(default=5, description="连接池大小")
    max_overflow: int = Field(default=10, description="最大溢出连接数")
    pool_timeout: int = Field(default=30, description="连接池超时时间")
    pool_recycle: int = Field(default=3600, description="连接回收时间")

    # 是否启用SQL日志
    echo_sql: bool = Field(default=False, description="是否打印SQL语句")

    @property
    def database_url(self) -> str:
        """获取当前环境的数据库URL"""
        return self.postgres_url or self.sqlite_url


class RedisSettings(BaseSettings):
    """Redis配置"""

    host: str = Field(default="localhost", description="Redis主机")
    port: int = Field(default=6379, description="Redis端口")
    password: Optional[str] = Field(default=None, description="Redis密码")
    db: int = Field(default=0, description="Redis数据库编号")

    # 连接池配置
    max_connections: int = Field(default=10, description="最大连接数")
    retry_on_timeout: bool = Field(default=True, description="超时重试")

    @property
    def redis_url(self) -> str:
        """构建Redis连接URL"""
        auth = f":{self.password}@" if self.password else ""
        return f"redis://{auth}{self.host}:{self.port}/{self.db}"


# JWT认证配置已移除
# class JWTSettings(BaseSettings): ...


class CelerySettings(BaseSettings):
    """Celery异步任务配置"""

    broker_url: str = Field(default="redis://localhost:6379/1", description="消息代理URL")
    result_backend: str = Field(
        default="redis://localhost:6379/2", description="结果后端URL"
    )
    task_serializer: str = Field(default="json", description="任务序列化格式")
    result_serializer: str = Field(default="json", description="结果序列化格式")
    accept_content: list[str] = Field(default=["json"], description="接受的内容类型")
    timezone: str = Field(default="UTC", description="时区")
    enable_utc: bool = Field(default=True, description="启用UTC")


class AppSettings(BaseSettings):
    """应用配置"""

    # 应用基本信息
    app_name: str = Field(default="Cube1 Backend", description="应用名称")
    app_version: str = Field(default="0.1.0", description="应用版本")
    debug: bool = Field(default=False, description="调试模式")

    # API配置
    api_prefix: str = Field(default="/api/v1", description="API前缀")
    docs_url: str = Field(default="/docs", description="API文档URL")
    redoc_url: str = Field(default="/redoc", description="ReDoc文档URL")

    # CORS配置
    cors_origins: list[str] = Field(
        default=["http://localhost:3000", "http://localhost:4096"],
        description="允许的CORS源",
    )
    cors_methods: list[str] = Field(
        default=["GET", "POST", "PUT", "DELETE", "OPTIONS"], description="允许的HTTP方法"
    )
    cors_headers: list[str] = Field(
        default=["Content-Type", "Authorization", "X-Requested-With"],
        description="允许的请求头"
    )

    # 安全配置
    secret_key: str = Field(
        description="应用密钥 - 必须通过环境变量设置"
    )

    # 日志配置
    log_level: str = Field(default="INFO", description="日志级别")
    log_format: str = Field(
        default="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        description="日志格式",
    )


class Settings(BaseSettings):
    """主配置类 - 整合所有配置"""

    model_config = SettingsConfigDict(
        env_file=".env", env_file_encoding="utf-8", case_sensitive=False, extra="ignore"
    )

    # 环境配置
    environment: str = Field(default="development", description="运行环境")

    # 子配置
    app: AppSettings = Field(default_factory=AppSettings)
    database: DatabaseSettings = Field(default_factory=DatabaseSettings)
    redis: RedisSettings = Field(default_factory=RedisSettings)
    # jwt: JWTSettings = Field(default_factory=JWTSettings)  # 已移除
    celery: CelerySettings = Field(default_factory=CelerySettings)

    @property
    def is_development(self) -> bool:
        """是否为开发环境"""
        return self.environment.lower() in ("development", "dev")

    @property
    def is_production(self) -> bool:
        """是否为生产环境"""
        return self.environment.lower() in ("production", "prod")

    @property
    def is_testing(self) -> bool:
        """是否为测试环境"""
        return self.environment.lower() in ("testing", "test")


# 全局配置实例
settings = Settings()

"""
FastAPI主应用 - Cube1 Group Backend
🎯 核心价值：8进制编码辅助系统的FastAPI应用入口
🚀 功能：应用初始化、中间件配置、路由注册、生命周期管理
⚡ 特性：自动API文档、CORS支持、错误处理、性能监控
"""

from contextlib import asynccontextmanager
import logging
import time

from fastapi import FastAPI, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.responses import JSONResponse

from app.api.v1.router import api_router
from app.config import settings
from app.core.exceptions import AppException
from app.core.logging import setup_logging
from app.database import close_db, init_db

# 设置日志
setup_logging()
logger = logging.getLogger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 启动时执行
    logger.info("🚀 启动 Cube1 Backend 应用...")

    # 初始化数据库
    await init_db()
    logger.info("✅ 数据库初始化完成")

    # 这里可以添加其他启动任务
    # - Redis连接初始化
    # - 缓存预热
    # - 后台任务启动

    yield

    # 关闭时执行
    logger.info("🔄 关闭 Cube1 Backend 应用...")

    # 清理资源
    await close_db()
    logger.info("✅ 数据库连接已关闭")


# 创建FastAPI应用实例
app = FastAPI(
    title=settings.app.app_name,
    version=settings.app.app_version,
    description="""
    ## Cube1 Group - 8进制编码辅助系统 Backend API

    🎯 **核心功能**
    - 33x33网格矩阵数据管理
    - 8色彩分类系统
    - 4层级架构支持
    - 版本控制和数据同步
    - 用户和项目管理

    🚀 **技术特性**
    - FastAPI + SQLModel
    - 异步数据库操作
    - JWT认证和权限控制
    - Redis缓存和异步任务
    - 自动API文档生成

    📚 **API文档**
    - [Swagger UI](/docs) - 交互式API文档
    - [ReDoc](/redoc) - 美观的API文档
    """,
    docs_url=settings.app.docs_url,
    redoc_url=settings.app.redoc_url,
    lifespan=lifespan,
    debug=settings.app.debug,
)


# === 中间件配置 ===

# CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.app.cors_origins,
    allow_credentials=True,
    allow_methods=settings.app.cors_methods,
    allow_headers=settings.app.cors_headers,
)

# 可信主机中间件（生产环境安全）
if settings.is_production:
    app.add_middleware(
        TrustedHostMiddleware,
        allowed_hosts=["*.vercel.app", "*.railway.app", "localhost"],
    )


# === 自定义中间件 ===


@app.middleware("http")
async def request_timing_middleware(request: Request, call_next):
    """请求计时中间件"""
    start_time = time.time()

    # 处理请求
    response = await call_next(request)

    # 计算处理时间
    process_time = time.time() - start_time
    response.headers["X-Process-Time"] = str(process_time)

    # 记录慢请求
    if process_time > 1.0:  # 超过1秒的请求
        logger.warning(f"慢请求: {request.method} {request.url} - {process_time:.2f}s")

    return response


@app.middleware("http")
async def request_logging_middleware(request: Request, call_next):
    """请求日志中间件"""
    # 记录请求开始
    logger.info(
        f"请求开始: {request.method} {request.url} "
        f"- Client: {request.client.host if request.client else 'unknown'}"
    )

    try:
        response = await call_next(request)

        # 记录响应
        logger.info(
            f"请求完成: {request.method} {request.url} " f"- Status: {response.status_code}"
        )

        return response

    except Exception as e:
        # 记录错误
        logger.error(f"请求错误: {request.method} {request.url} " f"- Error: {str(e)}")
        raise


# === 全局异常处理 ===


@app.exception_handler(AppException)
async def app_exception_handler(request: Request, exc: AppException):
    """应用异常处理器"""
    logger.error(f"应用异常: {exc.message} - {exc.details}")

    return JSONResponse(
        status_code=exc.status_code,
        content={
            "success": False,
            "error": exc.message,
            "details": exc.details,
            "error_code": exc.error_code,
            "timestamp": time.time(),
        },
    )


@app.exception_handler(Exception)
async def general_exception_handler(request: Request, exc: Exception):
    """通用异常处理器"""
    logger.error(f"未处理异常: {str(exc)}", exc_info=True)

    return JSONResponse(
        status_code=500,
        content={
            "success": False,
            "error": "内部服务器错误",
            "details": str(exc) if settings.app.debug else "请联系管理员",
            "timestamp": time.time(),
        },
    )


# === 路由注册 ===

# 注册API路由
app.include_router(api_router, prefix=settings.app.api_prefix, tags=["API v1"])


# === 根路径和健康检查 ===


@app.get("/", tags=["Root"])
async def root():
    """根路径 - 应用信息"""
    return {
        "message": "🎯 Cube1 Group Backend API",
        "version": settings.app.app_version,
        "environment": settings.environment,
        "docs": settings.app.docs_url,
        "redoc": settings.app.redoc_url,
        "status": "running",
        "timestamp": time.time(),
    }


@app.get("/health", tags=["Health"])
async def health_check():
    """健康检查端点"""
    return {
        "status": "healthy",
        "service": "cube1-backend",
        "version": settings.app.app_version,
        "environment": settings.environment,
        "timestamp": time.time(),
    }


# === 开发环境特殊配置 ===

if settings.is_development:
    # 开发环境下的额外配置
    logger.info("🔧 开发环境模式已启用")

    @app.get("/debug/config", tags=["Debug"])
    async def debug_config():
        """调试：显示配置信息（仅开发环境）"""
        return {
            "environment": settings.environment,
            "debug": settings.app.debug,
            "database_url": settings.database.database_url,
            "redis_url": settings.redis.redis_url,
            "cors_origins": settings.app.cors_origins,
        }


if __name__ == "__main__":
    import uvicorn

    uvicorn.run(
        "app.main:app",
        host="0.0.0.0",
        port=8000,
        reload=settings.is_development,
        log_level=settings.app.log_level.lower(),
    )

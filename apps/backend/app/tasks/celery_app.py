"""
Celery应用配置 - 异步任务处理
🎯 核心价值：分布式异步任务处理和调度
🔧 功能：任务队列、定时任务、结果存储
⚡ 特性：Redis消息代理、任务监控、错误处理
"""

from celery import Celery

from app.config import settings

# 创建Celery应用实例
celery_app = Celery(
    "cube1_backend",
    broker=settings.celery.broker_url,
    backend=settings.celery.result_backend,
    include=[
        "app.tasks.data_tasks",
        "app.tasks.export_tasks",
        "app.tasks.import_tasks",
        "app.tasks.maintenance_tasks",
    ],
)

# Celery配置
celery_app.conf.update(
    task_serializer=settings.celery.task_serializer,
    result_serializer=settings.celery.result_serializer,
    accept_content=settings.celery.accept_content,
    timezone=settings.celery.timezone,
    enable_utc=settings.celery.enable_utc,
    # 任务路由配置
    task_routes={
        "app.tasks.data_tasks.*": {"queue": "data_processing"},
        "app.tasks.export_tasks.*": {"queue": "export"},
        "app.tasks.import_tasks.*": {"queue": "import"},
        "app.tasks.maintenance_tasks.*": {"queue": "maintenance"},
    },
    # 任务执行配置
    task_always_eager=settings.is_development,  # 开发环境同步执行
    task_eager_propagates=True,
    task_ignore_result=False,
    task_store_eager_result=True,
    # 结果过期时间
    result_expires=3600,  # 1小时
    # 任务重试配置
    task_acks_late=True,
    task_reject_on_worker_lost=True,
    # 工作进程配置
    worker_prefetch_multiplier=1,
    worker_max_tasks_per_child=1000,
    # 定时任务配置
    beat_schedule={
        "cleanup-expired-data": {
            "task": "app.tasks.maintenance_tasks.cleanup_expired_data",
            "schedule": 3600.0,  # 每小时执行一次
        },
        "generate-daily-stats": {
            "task": "app.tasks.maintenance_tasks.generate_daily_stats",
            "schedule": 86400.0,  # 每天执行一次
        },
        "backup-database": {
            "task": "app.tasks.maintenance_tasks.backup_database",
            "schedule": 86400.0 * 7,  # 每周执行一次
        },
    },
    beat_schedule_filename="celerybeat-schedule",
)

# 任务装饰器配置
celery_app.conf.task_default_queue = "default"
celery_app.conf.task_default_exchange = "default"
celery_app.conf.task_default_exchange_type = "direct"
celery_app.conf.task_default_routing_key = "default"

if __name__ == "__main__":
    celery_app.start()

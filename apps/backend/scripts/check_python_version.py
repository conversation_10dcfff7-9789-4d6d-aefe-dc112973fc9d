#!/usr/bin/env python3
"""
Python版本检查脚本
🎯 核心价值：验证Python环境是否符合项目要求
🔧 功能：检查Python版本、依赖兼容性、环境配置
⚡ 特性：自动化检查、详细报告、环境验证
"""

import sys
import platform
import subprocess
from pathlib import Path
from typing import Dict, List, Tuple


class PythonVersionChecker:
    """Python版本检查器"""
    
    def __init__(self):
        self.required_version = (3, 11)
        self.current_version = sys.version_info[:2]
        self.issues: List[Dict[str, str]] = []
        
    def check_python_version(self) -> bool:
        """检查Python版本"""
        print(f"🐍 当前Python版本: {sys.version}")
        print(f"📍 要求版本: Python {self.required_version[0]}.{self.required_version[1]}+")
        
        if self.current_version < self.required_version:
            self.issues.append({
                "level": "ERROR",
                "category": "VERSION",
                "message": f"Python版本过低: {self.current_version[0]}.{self.current_version[1]} < {self.required_version[0]}.{self.required_version[1]}",
                "solution": f"请升级到Python {self.required_version[0]}.{self.required_version[1]}或更高版本"
            })
            return False
        elif self.current_version > (3, 12):
            self.issues.append({
                "level": "WARNING",
                "category": "VERSION",
                "message": f"Python版本较新: {self.current_version[0]}.{self.current_version[1]}",
                "solution": "请确保所有依赖都兼容此版本"
            })
            
        print(f"✅ Python版本检查通过: {self.current_version[0]}.{self.current_version[1]}")
        return True
        
    def check_platform_info(self) -> bool:
        """检查平台信息"""
        print(f"\n🖥️  平台信息:")
        print(f"   操作系统: {platform.system()} {platform.release()}")
        print(f"   架构: {platform.machine()}")
        print(f"   Python实现: {platform.python_implementation()}")
        print(f"   编译器: {platform.python_compiler()}")
        
        # 检查是否为支持的平台
        supported_platforms = ['Darwin', 'Linux', 'Windows']
        if platform.system() not in supported_platforms:
            self.issues.append({
                "level": "WARNING",
                "category": "PLATFORM",
                "message": f"未测试的平台: {platform.system()}",
                "solution": "请确保在支持的平台上运行"
            })
            return False
            
        return True
        
    def check_virtual_environment(self) -> bool:
        """检查虚拟环境"""
        print(f"\n🏠 环境信息:")
        
        # 检查是否在虚拟环境中
        in_venv = hasattr(sys, 'real_prefix') or (
            hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix
        )
        
        if in_venv:
            print(f"✅ 运行在虚拟环境中")
            print(f"   虚拟环境路径: {sys.prefix}")
        else:
            self.issues.append({
                "level": "WARNING",
                "category": "ENVIRONMENT",
                "message": "未在虚拟环境中运行",
                "solution": "建议使用poetry或venv创建虚拟环境"
            })
            print(f"⚠️  运行在系统Python环境中")
            
        print(f"   Python可执行文件: {sys.executable}")
        return in_venv
        
    def check_critical_imports(self) -> bool:
        """检查关键依赖导入"""
        print(f"\n📦 依赖检查:")
        
        critical_packages = [
            ('fastapi', 'FastAPI'),
            ('uvicorn', 'Uvicorn'),
            ('pydantic', 'Pydantic'),
            ('sqlmodel', 'SQLModel'),
            ('alembic', 'Alembic'),
        ]
        
        all_imported = True
        
        for package_name, display_name in critical_packages:
            try:
                __import__(package_name)
                # 获取版本信息
                try:
                    module = __import__(package_name)
                    version = getattr(module, '__version__', 'unknown')
                    print(f"   ✅ {display_name}: {version}")
                except:
                    print(f"   ✅ {display_name}: 已安装")
            except ImportError:
                self.issues.append({
                    "level": "ERROR",
                    "category": "DEPENDENCIES",
                    "message": f"缺少关键依赖: {package_name}",
                    "solution": f"运行 poetry install 安装依赖"
                })
                print(f"   ❌ {display_name}: 未安装")
                all_imported = False
                
        return all_imported
        
    def check_poetry_environment(self) -> bool:
        """检查Poetry环境"""
        print(f"\n📝 Poetry环境:")
        
        try:
            # 检查poetry是否可用
            result = subprocess.run(['poetry', '--version'], 
                                  capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                print(f"   ✅ Poetry版本: {result.stdout.strip()}")
                
                # 检查poetry环境信息
                try:
                    env_result = subprocess.run(['poetry', 'env', 'info'], 
                                              capture_output=True, text=True, timeout=10)
                    if env_result.returncode == 0:
                        lines = env_result.stdout.strip().split('\n')
                        for line in lines:
                            if 'Python:' in line:
                                print(f"   📍 {line.strip()}")
                                break
                except:
                    pass
                    
                return True
            else:
                self.issues.append({
                    "level": "WARNING",
                    "category": "TOOLS",
                    "message": "Poetry不可用",
                    "solution": "安装Poetry进行依赖管理"
                })
                return False
                
        except (subprocess.TimeoutExpired, FileNotFoundError):
            self.issues.append({
                "level": "WARNING",
                "category": "TOOLS",
                "message": "Poetry未安装或不可用",
                "solution": "安装Poetry: curl -sSL https://install.python-poetry.org | python3 -"
            })
            return False
            
    def check_project_structure(self) -> bool:
        """检查项目结构"""
        print(f"\n📁 项目结构:")
        
        required_files = [
            'pyproject.toml',
            'app/__init__.py',
            'app/main.py',
            'app/config.py',
        ]
        
        all_exist = True
        project_root = Path.cwd()
        
        for file_path in required_files:
            full_path = project_root / file_path
            if full_path.exists():
                print(f"   ✅ {file_path}")
            else:
                print(f"   ❌ {file_path}")
                self.issues.append({
                    "level": "ERROR",
                    "category": "PROJECT",
                    "message": f"缺少项目文件: {file_path}",
                    "solution": "确保在正确的项目目录中运行"
                })
                all_exist = False
                
        return all_exist
        
    def print_summary(self):
        """打印检查总结"""
        print(f"\n" + "="*60)
        print(f"📋 检查总结")
        print(f"="*60)
        
        if not self.issues:
            print(f"🎉 所有检查都通过了！")
            print(f"✅ Python 3.11环境配置正确")
            return
            
        # 按级别分组问题
        errors = [issue for issue in self.issues if issue['level'] == 'ERROR']
        warnings = [issue for issue in self.issues if issue['level'] == 'WARNING']
        
        if errors:
            print(f"\n🔴 错误 ({len(errors)}个):")
            for i, error in enumerate(errors, 1):
                print(f"   {i}. [{error['category']}] {error['message']}")
                print(f"      💡 解决方案: {error['solution']}")
                
        if warnings:
            print(f"\n🟡 警告 ({len(warnings)}个):")
            for i, warning in enumerate(warnings, 1):
                print(f"   {i}. [{warning['category']}] {warning['message']}")
                print(f"      💡 建议: {warning['solution']}")
                
        if errors:
            print(f"\n❌ 发现 {len(errors)} 个错误，请修复后重试")
        else:
            print(f"\n✅ 主要检查通过，有 {len(warnings)} 个警告")
            
    def run_all_checks(self) -> bool:
        """运行所有检查"""
        print("🔍 开始Python环境检查...\n")
        
        checks = [
            ("Python版本", self.check_python_version),
            ("平台信息", self.check_platform_info),
            ("虚拟环境", self.check_virtual_environment),
            ("关键依赖", self.check_critical_imports),
            ("Poetry环境", self.check_poetry_environment),
            ("项目结构", self.check_project_structure),
        ]
        
        all_passed = True
        for check_name, check_func in checks:
            try:
                result = check_func()
                if not result:
                    all_passed = False
            except Exception as e:
                print(f"❌ {check_name}检查失败: {e}")
                self.issues.append({
                    "level": "ERROR",
                    "category": "SYSTEM",
                    "message": f"{check_name}检查异常: {str(e)}",
                    "solution": "检查系统环境和权限"
                })
                all_passed = False
                
        self.print_summary()
        
        # 只有没有错误才返回True
        errors = [issue for issue in self.issues if issue['level'] == 'ERROR']
        return len(errors) == 0


def main():
    """主函数"""
    checker = PythonVersionChecker()
    success = checker.run_all_checks()
    
    if success:
        print(f"\n🚀 环境检查完成，可以开始开发！")
        sys.exit(0)
    else:
        print(f"\n🛠️  请修复上述问题后重新运行检查")
        sys.exit(1)


if __name__ == "__main__":
    main()

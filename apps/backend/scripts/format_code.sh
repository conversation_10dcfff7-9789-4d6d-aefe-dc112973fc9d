#!/bin/bash

# Cube1 Group Backend - 代码格式化脚本
# 使用isort、black和ruff进行代码质量检查和格式化

set -e

echo "🎯 开始代码格式化和检查..."

echo "📦 1. 导入排序 (isort)..."
poetry run isort .

echo "🎨 2. 代码格式化 (black)..."
poetry run black .

echo "🔍 3. 代码检查 (ruff)..."
poetry run ruff check . --fix || true

echo "🧪 4. 类型检查 (mypy)..."
poetry run mypy . || true

echo "✅ 代码格式化完成！"
echo ""
echo "💡 提示："
echo "  - isort: 导入语句已按照PEP 8标准排序"
echo "  - black: 代码格式已统一"
echo "  - ruff: 代码质量问题已检查"
echo "  - mypy: 类型注解已验证"
echo ""
echo "🚀 现在可以提交代码了！"

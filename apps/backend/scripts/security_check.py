#!/usr/bin/env python3
"""
安全配置检查脚本
🎯 核心价值：验证生产环境安全配置，防止安全漏洞
🔧 功能：检查密钥强度、CORS配置、环境变量等
⚡ 特性：自动化安全检查、生产环境验证
"""

import os
import sys
import secrets
from pathlib import Path
from typing import List, Dict, Any

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

try:
    from app.config import Settings
except ImportError:
    print("❌ 无法导入配置模块，请确保在正确的环境中运行")
    sys.exit(1)


class SecurityChecker:
    """安全配置检查器"""
    
    def __init__(self):
        self.settings = Settings()
        self.issues: List[Dict[str, Any]] = []
        
    def check_secret_key(self) -> bool:
        """检查应用密钥安全性"""
        secret_key = self.settings.app.secret_key
        
        # 检查是否为默认值
        weak_keys = [
            "your-super-secret-key-change-in-production",
            "dev-secret-key-please-change-for-production",
            "secret",
            "password",
            "123456"
        ]
        
        if secret_key in weak_keys:
            self.issues.append({
                "level": "CRITICAL",
                "category": "SECRET_KEY",
                "message": "使用了弱密钥或默认密钥",
                "recommendation": "使用 python -c \"import secrets; print(secrets.token_urlsafe(32))\" 生成强密钥"
            })
            return False
            
        # 检查密钥长度
        if len(secret_key) < 32:
            self.issues.append({
                "level": "HIGH",
                "category": "SECRET_KEY",
                "message": f"密钥长度不足 ({len(secret_key)} < 32)",
                "recommendation": "使用至少32字符的强密钥"
            })
            return False
            
        return True
        
    def check_cors_config(self) -> bool:
        """检查CORS配置安全性"""
        cors_origins = self.settings.app.cors_origins
        cors_headers = self.settings.app.cors_headers
        
        # 检查是否允许所有源
        if "*" in cors_origins:
            self.issues.append({
                "level": "HIGH",
                "category": "CORS",
                "message": "CORS配置允许所有源 (*)",
                "recommendation": "限制CORS源到具体的域名"
            })
            return False
            
        # 检查是否允许所有头部
        if "*" in cors_headers:
            self.issues.append({
                "level": "MEDIUM",
                "category": "CORS",
                "message": "CORS配置允许所有请求头 (*)",
                "recommendation": "限制允许的请求头到必要的头部"
            })
            return False
            
        # 检查生产环境是否包含localhost
        if self.settings.is_production:
            localhost_origins = [origin for origin in cors_origins if "localhost" in origin]
            if localhost_origins:
                self.issues.append({
                    "level": "MEDIUM",
                    "category": "CORS",
                    "message": f"生产环境CORS配置包含localhost: {localhost_origins}",
                    "recommendation": "生产环境移除localhost源"
                })
                return False
                
        return True
        
    def check_environment_config(self) -> bool:
        """检查环境配置"""
        # 检查生产环境配置
        if self.settings.is_production:
            if self.settings.app.debug:
                self.issues.append({
                    "level": "HIGH",
                    "category": "ENVIRONMENT",
                    "message": "生产环境启用了调试模式",
                    "recommendation": "生产环境设置 DEBUG=false"
                })
                return False
                
            if self.settings.database.echo_sql:
                self.issues.append({
                    "level": "MEDIUM",
                    "category": "ENVIRONMENT",
                    "message": "生产环境启用了SQL日志",
                    "recommendation": "生产环境设置 DB_ECHO_SQL=false"
                })
                return False
                
        return True
        
    def check_database_config(self) -> bool:
        """检查数据库配置"""
        if self.settings.is_production:
            # 生产环境应该使用PostgreSQL
            if "sqlite" in self.settings.database.database_url.lower():
                self.issues.append({
                    "level": "HIGH",
                    "category": "DATABASE",
                    "message": "生产环境使用SQLite数据库",
                    "recommendation": "生产环境使用PostgreSQL数据库"
                })
                return False
                
        return True
        
    def generate_secure_key(self) -> str:
        """生成安全密钥"""
        return secrets.token_urlsafe(32)
        
    def run_all_checks(self) -> bool:
        """运行所有安全检查"""
        print("🔒 开始安全配置检查...")
        print(f"📍 环境: {self.settings.environment}")
        print(f"🐛 调试模式: {self.settings.app.debug}")
        print()
        
        checks = [
            ("密钥安全性", self.check_secret_key),
            ("CORS配置", self.check_cors_config),
            ("环境配置", self.check_environment_config),
            ("数据库配置", self.check_database_config),
        ]
        
        all_passed = True
        for check_name, check_func in checks:
            try:
                result = check_func()
                status = "✅ 通过" if result else "❌ 失败"
                print(f"{status} {check_name}")
                if not result:
                    all_passed = False
            except Exception as e:
                print(f"❌ {check_name} - 检查失败: {e}")
                all_passed = False
                
        return all_passed
        
    def print_issues(self):
        """打印发现的问题"""
        if not self.issues:
            print("\n🎉 未发现安全问题！")
            return
            
        print(f"\n⚠️  发现 {len(self.issues)} 个安全问题:")
        print("=" * 60)
        
        for i, issue in enumerate(self.issues, 1):
            level_emoji = {
                "CRITICAL": "🔴",
                "HIGH": "🟠", 
                "MEDIUM": "🟡",
                "LOW": "🟢"
            }
            
            print(f"{i}. {level_emoji.get(issue['level'], '⚪')} [{issue['level']}] {issue['category']}")
            print(f"   问题: {issue['message']}")
            print(f"   建议: {issue['recommendation']}")
            print()
            
    def print_recommendations(self):
        """打印安全建议"""
        print("🛡️  安全建议:")
        print("=" * 60)
        print("1. 定期更新密钥")
        print("2. 使用环境变量管理敏感配置")
        print("3. 生产环境禁用调试模式")
        print("4. 限制CORS配置到必要的域名")
        print("5. 使用HTTPS协议")
        print("6. 定期进行安全审计")
        print()
        
        if self.settings.is_development:
            print("💡 生成新的安全密钥:")
            print(f"   SECRET_KEY={self.generate_secure_key()}")
            print()


def main():
    """主函数"""
    checker = SecurityChecker()
    
    # 运行检查
    all_passed = checker.run_all_checks()
    
    # 打印结果
    print()
    checker.print_issues()
    checker.print_recommendations()
    
    # 返回结果
    if all_passed:
        print("✅ 所有安全检查通过！")
        sys.exit(0)
    else:
        print("❌ 发现安全问题，请及时修复！")
        sys.exit(1)


if __name__ == "__main__":
    main()

#!/bin/bash

# Cube1 Group Backend - 启动脚本
# 🎯 核心价值：一键启动开发环境
# 🚀 功能：环境检查、依赖安装、数据库初始化、服务启动
# ⚡ 特性：错误处理、日志记录、进度显示

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查命令是否存在
check_command() {
    if ! command -v $1 &> /dev/null; then
        log_error "$1 未安装，请先安装 $1"
        exit 1
    fi
}

# 主函数
main() {
    log_info "🚀 启动 Cube1 Group Backend..."
    
    # 检查必要的命令
    log_info "检查环境依赖..."
    check_command "python3"
    check_command "poetry"
    
    # 检查Python版本
    python_version=$(python3 --version | cut -d' ' -f2)
    log_info "Python版本: $python_version"
    
    # 检查是否在项目根目录
    if [ ! -f "pyproject.toml" ]; then
        log_error "请在项目根目录运行此脚本"
        exit 1
    fi
    
    # 安装依赖
    log_info "安装Python依赖..."
    poetry install
    log_success "依赖安装完成"
    
    # 检查环境变量文件
    if [ ! -f ".env" ]; then
        log_warning ".env文件不存在，从.env.example复制..."
        cp .env.example .env
        log_info "请根据需要修改.env文件中的配置"
    fi
    
    # 数据库迁移
    log_info "执行数据库迁移..."
    poetry run alembic upgrade head || {
        log_warning "数据库迁移失败，尝试初始化数据库..."
        poetry run alembic revision --autogenerate -m "Initial migration"
        poetry run alembic upgrade head
    }
    log_success "数据库迁移完成"
    
    # 启动服务
    log_info "启动FastAPI服务..."
    log_info "服务地址: http://localhost:8000"
    log_info "API文档: http://localhost:8000/docs"
    log_info "按 Ctrl+C 停止服务"
    
    poetry run uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload
}

# 处理中断信号
trap 'log_info "正在停止服务..."; exit 0' INT

# 执行主函数
main "$@"

```mermaid
sequenceDiagram
    participant U as 👤 User
    participant GM as 🎯 GridMatrix
    participant EH as 🧠 EventHandler
    participant BS as 📊 BasicDataStore
    participant LS as 💾 LocalStorage
    participant AC as 🔌 API Client
    participant FA as 🚀 FastAPI
    participant SM as 🗄️ SQLModel
    participant DB as 🏛️ Database
    participant SP as 🎨 StylePanel
    participant SS as 🎭 StyleStore
    
    Note over U, SS: 网格矩阵可视化系统 - 组件交互序列图

    %% 序列1：用户点击网格单元格操作
    Note over U, DB: 序列1：用户点击网格单元格操作

    U->>+GM: 1. 点击单元格(x,y)
    Note right of U: t=0ms

    GM->>+EH: 2. handleCellClick(cell, event)
    Note right of GM: t=1ms

    EH->>+BS: 3. 记录点击事件
    Note right of EH: t=2ms
    
    BS-->>+LS: 4. updateCellData(id, updates)
    Note right of BS: t=3ms (异步)
    
    LS-->>-BS: 5. 本地缓存更新完成
    
    BS-->>+AC: 6. POST /api/v1/data/grid-cells
    Note right of BS: t=5ms (异步)
    
    AC->>+FA: 7. data.update_grid_cell
    Note right of AC: t=10ms
    
    FA->>+SM: 8. 数据验证和转换
    Note right of FA: t=15ms
    
    SM->>+DB: 9. UPDATE操作
    Note right of SM: t=25ms
    
    DB-->>-SM: 数据库响应
    SM-->>-FA: 模型响应
    FA-->>-AC: API响应
    AC-->>-BS: 更新完成
    
    BS-->>-PL: 10. 触发UI重渲染
    Note right of BS: t=50ms
    
    PL-->>-GM: UI状态更新
    GM-->>-U: 界面刷新完成
    
    %% 序列2：应用初始化加载
    Note over U, SS: 序列2：应用初始化加载
    
    U->>+BS: 1. 应用启动 - Store初始化
    Note right of U: t=0ms
    
    BS->>+LS: 2. 读取缓存数据
    Note right of BS: t=5ms
    
    LS-->>-BS: 返回缓存数据
    
    BS-->>+AC: 3. GET /api/v1/projects/{id}/data
    Note right of BS: t=10ms (异步)
    
    AC->>+FA: 4. 获取项目数据
    
    FA->>+DB: 5. 查询数据库
    
    DB-->>-FA: 返回项目数据
    FA-->>-AC: API数据响应
    AC-->>-BS: 数据同步完成
    
    BS-->>GM: 6. 数据同步完成 → UI渲染
    Note right of BS: t=100ms
    
    GM-->>-U: 应用加载完成
    
    %% 序列3：样式配置更新
    Note over U, SS: 序列3：样式配置更新
    
    U->>+SP: 1. 用户更改显示模式
    Note right of U: t=0ms
    
    SP->>+SS: 2. onDisplayModeChange(mode)
    Note right of SP: t=1ms
    
    SS->>SS: 3. 计算动态样式
    Note right of SS: t=3ms
    
    SS-->>GM: 4. 触发GridMatrix重渲染 (1089个单元格)
    Note right of SS: t=5ms (异步)
    
    activate GM
    Note right of GM: 网格重渲染
    deactivate GM
    
    SS-->>+LS: 5. 配置持久化到LocalStorage
    Note right of SS: t=8ms (异步)
    
    LS-->>-SS: 持久化完成
    SS-->>-SP: 样式更新完成
    SP-->>-U: 界面更新完成
    
    %% 性能和错误处理注释
    Note over U, SS: 性能优化要点：<br/>• React.memo防止不必要重渲染<br/>• Zustand状态管理减少prop drilling<br/>• LocalStorage + API双重缓存策略
    
    Note over U, SS: 错误处理机制：<br/>• GridErrorBoundary组件级错误捕获<br/>• API调用失败自动重试<br/>• LocalStorage作为数据备份
    
    Note over U, SS: 架构特性：<br/>• 异步操作不阻塞UI响应<br/>• 分层架构清晰职责分离<br/>• 状态管理现代化 (Zustand)
    
    Note over U, SS: 技术栈版本：<br/>• Next.js 15.1.0 + React 18.3.1<br/>• FastAPI 0.104.1 + SQLModel<br/>• TypeScript 5.8.3 (100%覆盖)
```
graph TD
    %% 数据源层
    subgraph "数据源层 (Data Sources)"
        A1["🗄️ GROUP_A_DATA<br/>基础颜色坐标数据"]
        A2["📍 SPECIAL_COORDINATES<br/>特殊黑色坐标映射"]
        A3["🎨 DEFAULT_COLOR_VALUES<br/>颜色值配置"]
        A4["⚙️ GridConfig<br/>网格配置"]
        A5["🎭 StyleConfig<br/>样式配置"]
    end

    %% 服务层
    subgraph "服务层 (Services)"
        B1["🔍 ColorCoordinateService<br/>颜色坐标查询"]
        B2["🎯 SpecialCoordinateService<br/>特殊坐标处理"]
        B3["🔄 ColorMappingService<br/>颜色映射转换"]
    end

    %% 状态管理层
    subgraph "状态管理层 (State Management)"
        C1["📊 BasicDataStore<br/>基础数据状态"]
        C2["⚙️ GridConfigStore<br/>网格配置状态"]
        C3["🎨 StyleStore<br/>样式状态"]
    end

    %% 业务逻辑层
    subgraph "业务逻辑层 (Business Logic)"
        D1["🔧 useGridDataManager<br/>数据管理Hook"]
        D2["📋 useGridData<br/>网格数据Hook"]
        D3["🎯 GridRenderingEngine<br/>渲染引擎"]
        D4["⚡ PerformanceOptimizer<br/>性能优化器"]
    end

    %% Hook层
    subgraph "Hook层 (React Hooks)"
        E1["🎮 useGridRenderingEngine<br/>渲染引擎Hook"]
        E2["📱 useCellRenderData<br/>单元格渲染Hook"]
    end

    %% 渲染层
    subgraph "渲染层 (Rendering)"
        F1["🎨 CellRenderData<br/>单元格渲染数据"]
        F2["🖼️ GridCell Component<br/>网格单元格组件"]
        F3["🌐 GridMatrix Component<br/>网格矩阵组件"]
    end

    %% 数据流连接
    A1 --> B1
    A2 --> B2
    A3 --> B3
    A4 --> C2
    A5 --> C3

    B1 --> C1
    B2 --> C1
    B3 --> C1
    C2 --> D1
    C3 --> D1

    C1 --> D1
    C1 --> D2
    D1 --> D3
    D2 --> D3
    D4 --> D3

    D3 --> E1
    E1 --> E2
    E1 --> F1
    E2 --> F1

    F1 --> F2
    F2 --> F3

    %% 渲染情况分支
    subgraph "渲染情况 (Rendering Cases)"
        G1["📍 坐标模式<br/>coordinates"]
        G2["🎨 颜色模式<br/>color"]
        G3["🔢 数值模式<br/>value"]
        G4["⚫ 特殊黑色坐标<br/>A-M字母"]
        G5["🌈 彩色单元格<br/>1-8数字"]
        G6["🔍 可见性过滤<br/>颜色/组别"]
    end

    %% 渲染情况数据流
    D3 --> G1
    D3 --> G2
    D3 --> G3
    B2 --> G4
    B3 --> G5
    C1 --> G6

    G1 --> F1
    G2 --> F1
    G3 --> F1
    G4 --> F1
    G5 --> F1
    G6 --> F1

    %% 性能优化流
    subgraph "性能优化 (Performance)"
        H1["💾 渲染缓存<br/>Render Cache"]
        H2["📦 批量处理<br/>Batch Processing"]
        H3["🎯 智能重渲染<br/>Smart Re-render"]
        H4["⏱️ 防抖调度<br/>Debounce"]
    end

    D4 --> H1
    D4 --> H2
    D4 --> H3
    D4 --> H4

    H1 --> E1
    H2 --> E1
    H3 --> E1
    H4 --> E1

    %% 样式定义
    classDef dataSource fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    classDef service fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    classDef state fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    classDef business fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    classDef hook fill:#fce4ec,stroke:#c2185b,stroke-width:2px
    classDef render fill:#f1f8e9,stroke:#689f38,stroke-width:2px
    classDef case fill:#e0f2f1,stroke:#00796b,stroke-width:2px
    classDef performance fill:#fff8e1,stroke:#ffa000,stroke-width:2px

    class A1,A2,A3,A4,A5 dataSource
    class B1,B2,B3 service
    class C1,C2,C3 state
    class D1,D2,D3,D4 business
    class E1,E2 hook
    class F1,F2,F3 render
    class G1,G2,G3,G4,G5,G6 case
    class H1,H2,H3,H4 performance
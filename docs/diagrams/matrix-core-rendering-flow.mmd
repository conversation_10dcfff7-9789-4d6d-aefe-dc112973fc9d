flowchart TD
    %% 核心渲染流程 - 简化版
    START(["🚀 应用启动"]) --> MOUNT["📱 GridMatrix组件挂载"]
    
    MOUNT --> HOOK["🎣 useGridData Hook"]
    HOOK --> CHECK{"🔍 检查matrixData"}
    
    CHECK -->|"数据为空"| INIT["⚙️ initializeMatrixData()"]
    CHECK -->|"数据存在"| READY["✅ 数据就绪"]
    
    INIT --> GENERATE["🔄 generateMatrixData()"]
    GENERATE --> DATASOURCES
    
    subgraph DATASOURCES ["📊 数据源"]
        DS1["GROUP_A_DATA"]
        DS2["SPECIAL_COORDINATES"]
        DS3["DEFAULT_COLOR_VALUES"]
    end
    
    DATASOURCES --> CALCULATE["🧮 calculateGroupCoordinates"]
    CALCULATE --> BUILD["🏗️ 构建MatrixData索引"]
    BUILD --> READY
    
    READY --> ENGINE["⚙️ GridRenderingEngine"]
    ENGINE --> RENDER_HOOK["🎮 useGridRenderingEngine"]
    
    RENDER_HOOK --> GET_DATA["📋 getCellRenderData"]
    GET_DATA --> MODES
    
    subgraph MODES ["🎯 渲染模式"]
        M1["📍 坐标模式"]
        M2["🎨 颜色模式"]
        M3["🔢 数值模式"]
        M4["⚫ 特殊字符"]
    end
    
    MODES --> CELL_DATA["📱 CellRenderData"]
    CELL_DATA --> GRID_CELL["🖼️ GridCell组件"]
    GRID_CELL --> DOM["🌐 DOM渲染"]
    
    %% 性能优化分支
    ENGINE --> CACHE["💾 缓存"]
    ENGINE --> BATCH["📦 批量处理"]
    CACHE --> GET_DATA
    BATCH --> GET_DATA
    
    %% 错误处理分支
    CHECK -->|"初始化失败"| ERROR["🚨 错误处理"]
    ERROR --> LOADING["⏳ 加载状态"]
    LOADING --> RETRY["🔄 重试机制"]
    RETRY --> INIT
    
    %% 样式定义
    classDef startNode fill:#4caf50,stroke:#2e7d32,stroke-width:3px,color:#fff
    classDef processNode fill:#2196f3,stroke:#1565c0,stroke-width:2px,color:#fff
    classDef dataNode fill:#ff9800,stroke:#ef6c00,stroke-width:2px,color:#fff
    classDef renderNode fill:#9c27b0,stroke:#6a1b9a,stroke-width:2px,color:#fff
    classDef errorNode fill:#f44336,stroke:#c62828,stroke-width:2px,color:#fff
    classDef endNode fill:#4caf50,stroke:#2e7d32,stroke-width:3px,color:#fff
    
    class START,MOUNT startNode
    class HOOK,CHECK,INIT,GENERATE,CALCULATE,BUILD,READY processNode
    class DATASOURCES,DS1,DS2,DS3 dataNode
    class ENGINE,RENDER_HOOK,GET_DATA,MODES,M1,M2,M3,M4,CELL_DATA,GRID_CELL renderNode
    class ERROR,LOADING,RETRY errorNode
    class DOM endNode
    class CACHE,BATCH processNode
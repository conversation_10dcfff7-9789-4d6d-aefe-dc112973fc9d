<svg width="1400" height="900" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: 'Arial', sans-serif; font-size: 24px; font-weight: bold; fill: #2c3e50; }
      .subtitle { font-family: 'Arial', sans-serif; font-size: 14px; fill: #34495e; }
      .process-title { font-family: 'Arial', sans-serif; font-size: 16px; font-weight: bold; fill: #2c3e50; }
      .step-text { font-family: 'Arial', sans-serif; font-size: 11px; fill: #2c3e50; }
      .decision-text { font-family: 'Arial', sans-serif; font-size: 10px; fill: #2c3e50; }
      .tech-label { font-family: 'Arial', sans-serif; font-size: 9px; fill: #7f8c8d; }
      .start-end { fill: #27ae60; stroke: #229954; stroke-width: 2; }
      .process { fill: #3498db; stroke: #2980b9; stroke-width: 2; }
      .decision { fill: #f39c12; stroke: #e67e22; stroke-width: 2; }
      .data { fill: #9b59b6; stroke: #8e44ad; stroke-width: 2; }
      .error { fill: #e74c3c; stroke: #c0392b; stroke-width: 2; }
      .arrow { stroke: #2c3e50; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
      .error-arrow { stroke: #e74c3c; stroke-width: 2; fill: none; marker-end: url(#arrowhead-red); }
      .async-arrow { stroke: #9b59b6; stroke-width: 2; fill: none; marker-end: url(#arrowhead-purple); stroke-dasharray: 5,5; }
    </style>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#2c3e50" />
    </marker>
    <marker id="arrowhead-red" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#e74c3c" />
    </marker>
    <marker id="arrowhead-purple" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#9b59b6" />
    </marker>
  </defs>
  
  <!-- 标题 -->
  <text x="700" y="30" text-anchor="middle" class="title">网格矩阵可视化系统 - 业务流程图</text>
  <text x="700" y="50" text-anchor="middle" class="subtitle">用户交互 → 状态管理 → 数据持久化完整流程</text>
  
  <!-- 流程1：网格单元格操作流程 -->
  <text x="50" y="90" class="process-title">流程1：网格单元格操作</text>
  
  <!-- 开始 -->
  <ellipse cx="120" cy="120" rx="60" ry="25" class="start-end"/>
  <text x="120" y="125" text-anchor="middle" class="step-text">用户点击单元格</text>
  
  <!-- GridMatrix处理 -->
  <rect x="50" y="170" width="140" height="50" class="process" rx="5"/>
  <text x="120" y="190" text-anchor="middle" class="step-text">GridMatrix组件</text>
  <text x="120" y="205" text-anchor="middle" class="tech-label">handleCellClick(cell, event)</text>
  
  <!-- 直接处理 -->
  <rect x="50" y="250" width="140" height="50" class="process" rx="5"/>
  <text x="120" y="270" text-anchor="middle" class="step-text">事件处理</text>
  <text x="120" y="285" text-anchor="middle" class="tech-label">console.log(cell)</text>
  
  <!-- 数据验证 -->
  <polygon points="120,320 160,340 120,360 80,340" class="decision"/>
  <text x="120" y="340" text-anchor="middle" class="decision-text">数据验证</text>
  <text x="120" y="350" text-anchor="middle" class="decision-text">有效?</text>
  
  <!-- Store更新 -->
  <rect x="50" y="390" width="140" height="50" class="data" rx="5"/>
  <text x="120" y="410" text-anchor="middle" class="step-text">BasicDataStore</text>
  <text x="120" y="425" text-anchor="middle" class="tech-label">updateCellData(id, updates)</text>
  
  <!-- LocalStorage同步 -->
  <rect x="50" y="470" width="140" height="40" class="data" rx="5"/>
  <text x="120" y="485" text-anchor="middle" class="step-text">LocalStorage同步</text>
  <text x="120" y="500" text-anchor="middle" class="tech-label">本地缓存更新</text>
  
  <!-- API调用 -->
  <rect x="50" y="540" width="140" height="50" class="process" rx="5"/>
  <text x="120" y="560" text-anchor="middle" class="step-text">API Client</text>
  <text x="120" y="575" text-anchor="middle" class="tech-label">POST /api/v1/data/grid-cells</text>
  
  <!-- 后端处理 -->
  <rect x="50" y="620" width="140" height="50" class="process" rx="5"/>
  <text x="120" y="640" text-anchor="middle" class="step-text">FastAPI处理</text>
  <text x="120" y="655" text-anchor="middle" class="tech-label">data.update_grid_cell</text>
  
  <!-- 数据库更新 -->
  <rect x="50" y="700" width="140" height="50" class="data" rx="5"/>
  <text x="120" y="720" text-anchor="middle" class="step-text">数据库持久化</text>
  <text x="120" y="735" text-anchor="middle" class="tech-label">SQLModel → Database</text>
  
  <!-- UI重渲染 -->
  <rect x="50" y="780" width="140" height="40" class="process" rx="5"/>
  <text x="120" y="795" text-anchor="middle" class="step-text">UI重新渲染</text>
  <text x="120" y="810" text-anchor="middle" class="tech-label">React组件更新</text>
  
  <!-- 流程1箭头 -->
  <line x1="120" y1="145" x2="120" y2="170" class="arrow"/>
  <line x1="120" y1="220" x2="120" y2="250" class="arrow"/>
  <line x1="120" y1="300" x2="120" y2="320" class="arrow"/>
  <line x1="120" y1="360" x2="120" y2="390" class="arrow"/>
  <line x1="120" y1="440" x2="120" y2="470" class="arrow"/>
  <line x1="120" y1="510" x2="120" y2="540" class="async-arrow"/>
  <line x1="120" y1="590" x2="120" y2="620" class="arrow"/>
  <line x1="120" y1="670" x2="120" y2="700" class="arrow"/>
  <line x1="120" y1="750" x2="120" y2="780" class="arrow"/>
  
  <!-- 错误处理分支 -->
  <rect x="220" y="320" width="120" height="40" class="error" rx="5"/>
  <text x="280" y="335" text-anchor="middle" class="step-text">错误处理</text>
  <text x="280" y="350" text-anchor="middle" class="tech-label">显示错误信息</text>
  <line x1="160" y1="340" x2="220" y2="340" class="error-arrow"/>
  <text x="190" y="335" class="decision-text">否</text>
  
  <!-- 流程2：样式配置流程 -->
  <text x="450" y="90" class="process-title">流程2：样式配置更新</text>
  
  <!-- 用户操作 -->
  <ellipse cx="520" cy="120" rx="60" ry="25" class="start-end"/>
  <text x="520" y="125" text-anchor="middle" class="step-text">用户调整样式</text>
  
  <!-- StylePanel -->
  <rect x="450" y="170" width="140" height="50" class="process" rx="5"/>
  <text x="520" y="190" text-anchor="middle" class="step-text">StylePanel组件</text>
  <text x="520" y="205" text-anchor="middle" class="tech-label">onDisplayModeChange</text>
  
  <!-- 样式Store -->
  <rect x="450" y="250" width="140" height="50" class="data" rx="5"/>
  <text x="520" y="270" text-anchor="middle" class="step-text">useStyleStore</text>
  <text x="520" y="285" text-anchor="middle" class="tech-label">setDisplayMode(mode)</text>
  
  <!-- 动态样式计算 -->
  <rect x="450" y="330" width="140" height="50" class="process" rx="5"/>
  <text x="520" y="350" text-anchor="middle" class="step-text">动态样式计算</text>
  <text x="520" y="365" text-anchor="middle" class="tech-label">useDynamicStyleStore</text>
  
  <!-- 组件重渲染 -->
  <rect x="450" y="410" width="140" height="50" class="process" rx="5"/>
  <text x="520" y="430" text-anchor="middle" class="step-text">GridMatrix重渲染</text>
  <text x="520" y="445" text-anchor="middle" class="tech-label">1089个单元格更新</text>
  
  <!-- 配置持久化 -->
  <rect x="450" y="490" width="140" height="40" class="data" rx="5"/>
  <text x="520" y="505" text-anchor="middle" class="step-text">配置持久化</text>
  <text x="520" y="520" text-anchor="middle" class="tech-label">LocalStorage保存</text>
  
  <!-- 流程2箭头 -->
  <line x1="520" y1="145" x2="520" y2="170" class="arrow"/>
  <line x1="520" y1="220" x2="520" y2="250" class="arrow"/>
  <line x1="520" y1="300" x2="520" y2="330" class="arrow"/>
  <line x1="520" y1="380" x2="520" y2="410" class="arrow"/>
  <line x1="520" y1="460" x2="520" y2="490" class="arrow"/>
  
  <!-- 流程3：应用初始化流程 -->
  <text x="750" y="90" class="process-title">流程3：应用初始化加载</text>
  
  <!-- 应用启动 -->
  <ellipse cx="820" cy="120" rx="60" ry="25" class="start-end"/>
  <text x="820" y="125" text-anchor="middle" class="step-text">应用启动</text>
  
  <!-- Store初始化 -->
  <rect x="750" y="170" width="140" height="50" class="data" rx="5"/>
  <text x="820" y="190" text-anchor="middle" class="step-text">Stores初始化</text>
  <text x="820" y="205" text-anchor="middle" class="tech-label">4个Zustand Store</text>
  
  <!-- 缓存读取 -->
  <rect x="750" y="250" width="140" height="50" class="data" rx="5"/>
  <text x="820" y="270" text-anchor="middle" class="step-text">LocalStorage读取</text>
  <text x="820" y="285" text-anchor="middle" class="tech-label">缓存数据恢复</text>
  
  <!-- API数据同步 -->
  <rect x="750" y="330" width="140" height="50" class="process" rx="5"/>
  <text x="820" y="350" text-anchor="middle" class="step-text">API数据同步</text>
  <text x="820" y="365" text-anchor="middle" class="tech-label">GET /api/v1/projects/{id}</text>
  
  <!-- SWR缓存 -->
  <rect x="750" y="410" width="140" height="50" class="data" rx="5"/>
  <text x="820" y="430" text-anchor="middle" class="step-text">SWR缓存管理</text>
  <text x="820" y="445" text-anchor="middle" class="tech-label">数据获取和缓存</text>
  
  <!-- 组件渲染 -->
  <rect x="750" y="490" width="140" height="50" class="process" rx="5"/>
  <text x="820" y="510" text-anchor="middle" class="step-text">组件渲染完成</text>
  <text x="820" y="525" text-anchor="middle" class="tech-label">React组件树</text>
  
  <!-- 流程3箭头 -->
  <line x1="820" y1="145" x2="820" y2="170" class="arrow"/>
  <line x1="820" y1="220" x2="820" y2="250" class="arrow"/>
  <line x1="820" y1="300" x2="820" y2="330" class="arrow"/>
  <line x1="820" y1="380" x2="820" y2="410" class="async-arrow"/>
  <line x1="820" y1="460" x2="820" y2="490" class="arrow"/>
  
  <!-- 流程4：错误处理机制 -->
  <text x="1050" y="90" class="process-title">流程4：错误处理机制</text>
  
  <!-- 错误检测 -->
  <polygon points="1120,130 1160,150 1120,170 1080,150" class="decision"/>
  <text x="1120" y="150" text-anchor="middle" class="decision-text">错误检测</text>
  
  <!-- 组件级错误 -->
  <rect x="1050" y="200" width="140" height="50" class="error" rx="5"/>
  <text x="1120" y="220" text-anchor="middle" class="step-text">GridErrorBoundary</text>
  <text x="1120" y="235" text-anchor="middle" class="tech-label">组件级错误捕获</text>
  
  <!-- API错误 -->
  <rect x="1050" y="280" width="140" height="50" class="error" rx="5"/>
  <text x="1120" y="300" text-anchor="middle" class="step-text">API错误处理</text>
  <text x="1120" y="315" text-anchor="middle" class="tech-label">网络请求失败</text>
  
  <!-- 数据验证错误 -->
  <rect x="1050" y="360" width="140" height="50" class="error" rx="5"/>
  <text x="1120" y="380" text-anchor="middle" class="step-text">数据验证失败</text>
  <text x="1120" y="395" text-anchor="middle" class="tech-label">Pydantic验证</text>
  
  <!-- 回退机制 -->
  <rect x="1050" y="440" width="140" height="50" class="data" rx="5"/>
  <text x="1120" y="460" text-anchor="middle" class="step-text">回退机制</text>
  <text x="1120" y="475" text-anchor="middle" class="tech-label">LocalStorage备份</text>
  
  <!-- 用户通知 -->
  <rect x="1050" y="520" width="140" height="40" class="process" rx="5"/>
  <text x="1120" y="535" text-anchor="middle" class="step-text">用户通知</text>
  <text x="1120" y="550" text-anchor="middle" class="tech-label">错误提示界面</text>
  
  <!-- 流程4箭头 -->
  <line x1="1120" y1="170" x2="1120" y2="200" class="error-arrow"/>
  <line x1="1120" y1="250" x2="1120" y2="280" class="error-arrow"/>
  <line x1="1120" y1="330" x2="1120" y2="360" class="error-arrow"/>
  <line x1="1120" y1="410" x2="1120" y2="440" class="error-arrow"/>
  <line x1="1120" y1="490" x2="1120" y2="520" class="arrow"/>
  
  <!-- 跨流程连接 -->
  <line x1="190" y1="490" x2="450" y2="490" class="async-arrow"/>
  <text x="320" y="485" class="tech-label">样式更新触发</text>
  
  <line x1="590" y1="200" x2="750" y2="200" class="async-arrow"/>
  <text x="670" y="195" class="tech-label">初始化完成</text>
  
  <!-- 性能指标框 -->
  <rect x="1220" y="200" width="160" height="200" class="process" rx="5"/>
  <text x="1300" y="220" text-anchor="middle" class="process-title">性能指标</text>
  <text x="1230" y="245" class="step-text">• 单元格渲染：&lt;16ms</text>
  <text x="1230" y="265" class="step-text">• Store更新：&lt;1ms</text>
  <text x="1230" y="285" class="step-text">• API响应：&lt;100ms</text>
  <text x="1230" y="305" class="step-text">• 内存使用：&lt;50MB</text>
  <text x="1230" y="325" class="step-text">• 错误率：&lt;0.1%</text>
  <text x="1230" y="345" class="step-text">• 缓存命中：&gt;95%</text>
  <text x="1230" y="365" class="step-text">• 组件重用：&gt;90%</text>
  
  <!-- 图例 -->
  <rect x="50" y="850" width="600" height="40" class="process" rx="3"/>
  <text x="60" y="870" class="step-text">图例：</text>
  
  <ellipse cx="120" cy="870" rx="15" ry="8" class="start-end"/>
  <text x="145" y="875" class="tech-label">开始/结束</text>
  
  <rect x="200" y="862" width="30" height="16" class="process" rx="2"/>
  <text x="240" y="875" class="tech-label">处理过程</text>
  
  <polygon points="290,870 305,875 290,880 275,875" class="decision"/>
  <text x="315" y="875" class="tech-label">决策点</text>
  
  <rect x="370" y="862" width="30" height="16" class="data" rx="2"/>
  <text x="410" y="875" class="tech-label">数据操作</text>
  
  <rect x="470" y="862" width="30" height="16" class="error" rx="2"/>
  <text x="510" y="875" class="tech-label">错误处理</text>
  
  <line x1="550" y1="870" x2="580" y2="870" class="arrow"/>
  <text x="590" y="875" class="tech-label">同步流程</text>
  
  <line x1="550" y1="880" x2="580" y2="880" class="async-arrow"/>
  <text x="590" y="885" class="tech-label">异步流程</text>
  
</svg>
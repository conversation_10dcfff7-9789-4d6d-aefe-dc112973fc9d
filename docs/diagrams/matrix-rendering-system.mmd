flowchart LR
    %% 数据源
    subgraph DataSources ["📊 数据源"]
        DS1["GROUP_A_DATA<br/>颜色坐标"]
        DS2["SPECIAL_COORDINATES<br/>特殊映射"]
        DS3["DEFAULT_COLOR_VALUES<br/>颜色配置"]
        DS4["GridConfig<br/>网格配置"]
    end

    %% 服务层
    subgraph Services ["🔧 服务层"]
        SV1["ColorCoordinateService"]
        SV2["SpecialCoordinateService"]
        SV3["ColorMappingService"]
    end

    %% 状态管理
    subgraph StateManagement ["📦 状态管理"]
        SM1["BasicDataStore"]
        SM2["GridConfigStore"]
        SM3["StyleStore"]
    end

    %% 核心引擎
    subgraph CoreEngine ["⚙️ 核心引擎"]
        CE1["GridRenderingEngine"]
        CE2["PerformanceOptimizer"]
    end

    %% React Hooks
    subgraph ReactHooks ["🎣 React Hooks"]
        RH1["useGridRenderingEngine"]
        RH2["useCellRenderData"]
        RH3["useGridDataManager"]
    end

    %% 渲染组件
    subgraph RenderComponents ["🎨 渲染组件"]
        RC1["GridMatrix"]
        RC2["GridCell"]
        RC3["CellRenderData"]
    end

    %% 渲染模式
    subgraph RenderModes ["🎯 渲染模式"]
        RM1["坐标模式"]
        RM2["颜色模式"]
        RM3["数值模式"]
        RM4["特殊字符"]
    end

    %% 性能优化
    subgraph Performance ["⚡ 性能优化"]
        PF1["渲染缓存"]
        PF2["批量处理"]
        PF3["防抖调度"]
        PF4["智能重渲染"]
    end

    %% 主要数据流
    DataSources --> Services
    Services --> StateManagement
    StateManagement --> CoreEngine
    CoreEngine --> ReactHooks
    ReactHooks --> RenderComponents

    %% 渲染模式流
    CoreEngine --> RenderModes
    RenderModes --> RenderComponents

    %% 性能优化流
    CoreEngine --> Performance
    Performance --> ReactHooks

    %% 详细连接
    DS1 --> SV1
    DS2 --> SV2
    DS3 --> SV3
    DS4 --> SM2

    SV1 --> SM1
    SV2 --> SM1
    SV3 --> SM1

    SM1 --> CE1
    SM2 --> CE1
    SM3 --> CE1

    CE1 --> RH1
    CE2 --> RH1

    RH1 --> RH2
    RH1 --> RH3

    RH1 --> RC3
    RH2 --> RC3
    RC3 --> RC2
    RC2 --> RC1

    CE1 --> RM1
    CE1 --> RM2
    CE1 --> RM3
    SV2 --> RM4

    CE2 --> PF1
    CE2 --> PF2
    CE2 --> PF3
    CE2 --> PF4

    %% 样式定义
    classDef dataStyle fill:#e3f2fd,stroke:#1976d2,stroke-width:2px,color:#000
    classDef serviceStyle fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px,color:#000
    classDef stateStyle fill:#e8f5e8,stroke:#388e3c,stroke-width:2px,color:#000
    classDef engineStyle fill:#fff3e0,stroke:#f57c00,stroke-width:2px,color:#000
    classDef hookStyle fill:#fce4ec,stroke:#c2185b,stroke-width:2px,color:#000
    classDef renderStyle fill:#f1f8e9,stroke:#689f38,stroke-width:2px,color:#000
    classDef modeStyle fill:#e0f2f1,stroke:#00796b,stroke-width:2px,color:#000
    classDef perfStyle fill:#fff8e1,stroke:#ffa000,stroke-width:2px,color:#000

    class DataSources,DS1,DS2,DS3,DS4 dataStyle
    class Services,SV1,SV2,SV3 serviceStyle
    class StateManagement,SM1,SM2,SM3 stateStyle
    class CoreEngine,CE1,CE2 engineStyle
    class ReactHooks,RH1,RH2,RH3 hookStyle
    class RenderComponents,RC1,RC2,RC3 renderStyle
    class RenderModes,RM1,RM2,RM3,RM4 modeStyle
    class Performance,PF1,PF2,PF3,PF4 perfStyle
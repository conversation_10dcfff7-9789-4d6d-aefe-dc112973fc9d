```mermaid
graph TB
    %% 网格矩阵可视化系统 - 系统架构图
    
    subgraph "用户界面层"
        Browser["🌐 浏览器<br/>Chrome/Safari/Firefox"]
        NextApp["⚡ Next.js App Router<br/>v15.1.0"]
    end
    
    subgraph "前端应用层"
        subgraph "React组件"
            GridMatrix["🎯 GridMatrix<br/>33×33网格渲染"]
            StylePanel["🎨 StylePanel<br/>样式控制面板"]
            ControlPanel["⚙️ ControlPanel<br/>操作控制"]
        end
        
        subgraph "Features模块"
            StyleMgmt["🎭 Style Management<br/>样式管理"]
            SharedFeatures["🔗 Shared Features<br/>共享功能"]
        end
        
        subgraph "状态管理 (Zustand)"
            BasicStore["📊 BasicDataStore<br/>网格数据"]
            StyleStore["🎨 StyleStore<br/>样式配置"]
            GridConfigStore["⚙️ GridConfigStore<br/>网格配置"]
        end
        
        subgraph "数据访问层"
            APIClient["🔌 API Client<br/>HTTP请求"]
            LocalStorage["💾 LocalStorage<br/>本地缓存"]
        end
    end
    
    subgraph "API网关层"
        FastAPI["🚀 FastAPI<br/>v0.104.1"]
        Router["🛣️ API Router<br/>路由管理"]
        Middleware["🔒 Middleware<br/>中间件"]
    end
    
    subgraph "后端服务层"
        subgraph "业务逻辑"
            ProjectService["📁 Project Service<br/>项目管理"]
            DataService["📊 Data Service<br/>数据处理"]
            GridService["🎯 Grid Service<br/>网格逻辑"]
        end
        
        subgraph "数据模型 (SQLModel)"
            ProjectModel["📋 Project Model<br/>项目模型"]
            GridModel["🎯 Grid Data Model<br/>网格数据模型"]
            StyleModel["🎨 Style Model<br/>样式模型"]
        end
    end
    
    subgraph "数据持久层"
        Database["🗄️ SQLite/PostgreSQL<br/>主数据库"]
        Alembic["🔄 Alembic<br/>数据库迁移"]
    end
    
    %% 连接关系
    Browser --> NextApp
    NextApp --> GridMatrix
    NextApp --> StylePanel
    NextApp --> ControlPanel
    
    GridMatrix --> BasicStore
    StylePanel --> StyleStore
    ControlPanel --> GridConfigStore
    
    BasicStore --> APIClient
    StyleStore --> LocalStorage
    GridConfigStore --> APIClient
    
    APIClient --> FastAPI
    LocalStorage -.-> BasicStore
    
    FastAPI --> Router
    Router --> Middleware
    Middleware --> ProjectService
    Middleware --> DataService
    Middleware --> GridService
    
    ProjectService --> ProjectModel
    DataService --> GridModel
    GridService --> StyleModel
    
    ProjectModel --> Database
    GridModel --> Database
    StyleModel --> Database
    
    Alembic --> Database
    
    %% 样式定义
    classDef frontend fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    classDef backend fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    classDef database fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    classDef api fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    
    class GridMatrix,StylePanel,ControlPanel,BasicStore,StyleStore,GridConfigStore,APIClient,LocalStorage frontend
    class FastAPI,Router,Middleware,ProjectService,DataService,GridService,ProjectModel,GridModel,StyleModel backend
    class Database,Alembic database
    class Browser,NextApp api
```
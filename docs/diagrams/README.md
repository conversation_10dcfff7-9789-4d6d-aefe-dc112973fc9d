# 网格矩阵可视化系统 - 架构图表

本目录包含了项目的核心架构图表，提供了SVG静态版本和Mermaid可编辑版本。

## 图表文件说明

### 1. 系统架构图
- **SVG版本**: `system-architecture.svg` - 静态展示版本
- **Mermaid版本**: `system-architecture.mmd` - 可编辑版本
- **内容**: 展示系统的分层架构，包括前端、后端、数据库等各层组件及其关系

### 2. 业务流程图
- **SVG版本**: `business-flow.svg` - 静态展示版本
- **Mermaid版本**: `business-flow.mmd` - 可编辑版本
- **内容**: 展示核心业务流程，包括用户操作、数据处理、错误处理等流程

### 3. 组件交互序列图
- **SVG版本**: `sequence-diagram.svg` - 静态展示版本
- **Mermaid版本**: `sequence-diagram.mmd` - 可编辑版本
- **内容**: 展示组件间的时序交互，包括用户操作、初始化、样式更新等场景

### 4. 矩阵渲染系统图表

#### 4.1 详细数据流图
- **Mermaid版本**: `matrix-rendering-dataflow.mmd`
- **内容**: 垂直布局，详细展示矩阵渲染系统的所有组件和数据流
- **特点**: 包含完整的渲染情况、性能优化机制
- **适用**: 深入理解系统架构和数据流向

#### 4.2 架构概览图
- **Mermaid版本**: `matrix-rendering-architecture.mmd`
- **内容**: 水平布局，分层架构清晰展示
- **特点**: 主要层次和核心组件关系
- **适用**: 快速了解系统整体架构

#### 4.3 分层架构图
- **Mermaid版本**: `matrix-rendering-layers.mmd`
- **内容**: 垂直分层，强调层次关系
- **特点**: 用户交互到数据层的完整流程
- **适用**: 理解系统分层设计和数据流向

#### 4.4 系统架构图
- **Mermaid版本**: `matrix-rendering-system.mmd`
- **内容**: 水平流程图，类似传统架构图
- **特点**: 核心模块和主要数据流
- **适用**: 技术文档和架构展示

#### 4.5 用户交互数据流图
- **Mermaid版本**: `user-interaction-dataflow.mmd`
- **内容**: 详细展示用户交互的完整流程
- **特点**: 事件处理、状态更新、视觉反馈、错误处理等完整交互链路
- **适用**: 理解用户操作如何触发系统响应

#### 4.6 矩阵渲染系统说明文档
- **文档**: `matrix-rendering-dataflow-guide.md`
- **内容**: 详细的系统架构说明、渲染情况详解、性能优化机制
- **特点**: 完整的技术文档，包含代码示例和使用指南

## 如何使用Mermaid图表

### 在线编辑
1. 访问 [Mermaid Live Editor](https://mermaid.live/)
2. 复制 `.mmd` 文件中的代码
3. 粘贴到编辑器中进行修改
4. 导出为SVG、PNG或PDF格式

### 本地编辑
1. 安装Mermaid CLI：`npm install -g @mermaid-js/mermaid-cli`
2. 编辑 `.mmd` 文件
3. 生成图片：`mmdc -i diagram.mmd -o diagram.svg`

### IDE集成
- **VS Code**: 安装 "Mermaid Markdown Syntax Highlighting" 插件
- **JetBrains**: 内置支持Mermaid语法
- **Typora**: 原生支持Mermaid图表渲染

## 图表维护指南

### 更新流程
1. 修改对应的 `.mmd` 文件
2. 使用Mermaid工具生成新的SVG文件
3. 更新文档中的图表引用

### 版本控制
- 优先维护 `.mmd` 文件（源文件）
- SVG文件作为展示版本
- 重大架构变更时同时更新两个版本

### 样式自定义
Mermaid图表支持CSS样式自定义，可以通过以下方式调整：

```mermaid
%%{init: {'theme':'base', 'themeVariables': { 'primaryColor': '#ff0000'}}}%%
graph TD
    A[开始] --> B[结束]
```

## 技术栈信息

### 前端技术栈
- **框架**: Next.js 15.1.0
- **UI库**: React 18.3.1
- **语言**: TypeScript 5.8.3 (100%覆盖)
- **状态管理**: Zustand
- **样式**: Tailwind CSS

### 后端技术栈
- **框架**: FastAPI 0.104.1
- **ORM**: SQLModel
- **数据库**: SQLite/PostgreSQL
- **迁移**: Alembic

### 开发工具
- **构建**: Turbo + pnpm
- **代码质量**: ESLint + Prettier
- **测试**: Vitest + Playwright
- **部署**: Docker + Vercel

## 相关文档

- [项目README](../../README.md)
- [开发日志](../log/)
- [分析报告](../report/)
- [架构模板](../templates/architecture-template.md)
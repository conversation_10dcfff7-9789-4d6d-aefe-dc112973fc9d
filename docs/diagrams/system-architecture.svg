<svg width="1200" height="800" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: 'Arial', sans-serif; font-size: 24px; font-weight: bold; fill: #2c3e50; }
      .layer-title { font-family: 'Arial', sans-serif; font-size: 16px; font-weight: bold; fill: #34495e; }
      .component { font-family: 'Arial', sans-serif; font-size: 12px; fill: #2c3e50; }
      .tech-stack { font-family: 'Arial', sans-serif; font-size: 10px; fill: #7f8c8d; }
      .metric { font-family: 'Arial', sans-serif; font-size: 10px; fill: #e74c3c; font-weight: bold; }
      .layer-bg { fill: #ecf0f1; stroke: #bdc3c7; stroke-width: 2; }
      .component-box { fill: #ffffff; stroke: #3498db; stroke-width: 1.5; }
      .api-box { fill: #f8f9fa; stroke: #e67e22; stroke-width: 1.5; }
      .data-box { fill: #f1f2f6; stroke: #9b59b6; stroke-width: 1.5; }
      .arrow { stroke: #2c3e50; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
      .data-flow { stroke: #e74c3c; stroke-width: 2; fill: none; marker-end: url(#arrowhead-red); stroke-dasharray: 5,5; }
    </style>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#2c3e50" />
    </marker>
    <marker id="arrowhead-red" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#e74c3c" />
    </marker>
  </defs>
  
  <!-- 标题 -->
  <text x="600" y="30" text-anchor="middle" class="title">网格矩阵可视化系统 - 系统架构图</text>
  <text x="600" y="50" text-anchor="middle" class="tech-stack">Monorepo架构 | Next.js 15.1.0 + FastAPI 0.104.1 | 33×33网格系统</text>
  
  <!-- 用户界面层 -->
  <rect x="50" y="80" width="1100" height="80" class="layer-bg" rx="5"/>
  <text x="70" y="105" class="layer-title">用户界面层</text>
  <rect x="200" y="95" width="150" height="50" class="component-box" rx="3"/>
  <text x="275" y="115" text-anchor="middle" class="component">浏览器界面</text>
  <text x="275" y="130" text-anchor="middle" class="tech-stack">React 18.3.1</text>
  
  <rect x="400" y="95" width="150" height="50" class="component-box" rx="3"/>
  <text x="475" y="115" text-anchor="middle" class="component">Next.js App Router</text>
  <text x="475" y="130" text-anchor="middle" class="tech-stack">TypeScript 5.8.3</text>
  
  <rect x="600" y="95" width="150" height="50" class="component-box" rx="3"/>
  <text x="675" y="115" text-anchor="middle" class="component">响应式设计</text>
  <text x="675" y="130" text-anchor="middle" class="tech-stack">Tailwind CSS</text>
  
  <!-- 前端应用层 -->
  <rect x="50" y="180" width="1100" height="180" class="layer-bg" rx="5"/>
  <text x="70" y="205" class="layer-title">前端应用层</text>
  
  <!-- 表现层 -->
  <text x="90" y="230" class="component">表现层</text>
  <rect x="150" y="215" width="120" height="40" class="component-box" rx="3"/>
  <text x="210" y="230" text-anchor="middle" class="component">GridMatrix</text>
  <text x="210" y="245" text-anchor="middle" class="tech-stack">1089个单元格</text>
  
  <rect x="280" y="215" width="120" height="40" class="component-box" rx="3"/>
  <text x="340" y="230" text-anchor="middle" class="component">StylePanel</text>
  <text x="340" y="245" text-anchor="middle" class="tech-stack">样式控制</text>
  
  <rect x="410" y="215" width="120" height="40" class="component-box" rx="3"/>
  <text x="470" y="230" text-anchor="middle" class="component">ControlPanel</text>
  <text x="470" y="245" text-anchor="middle" class="tech-stack">交互控制</text>
  
  <!-- 业务层 -->
  <text x="90" y="280" class="component">业务层</text>
  <rect x="150" y="265" width="120" height="40" class="component-box" rx="3"/>
  <text x="210" y="280" text-anchor="middle" class="component">grid-system</text>
  <text x="210" y="295" text-anchor="middle" class="tech-stack">网格核心</text>
  
  <rect x="280" y="265" width="120" height="40" class="component-box" rx="3"/>
  <text x="340" y="280" text-anchor="middle" class="component">style-management</text>
  <text x="340" y="295" text-anchor="middle" class="tech-stack">样式管理</text>
  
  <rect x="410" y="265" width="120" height="40" class="component-box" rx="3"/>
  <text x="470" y="280" text-anchor="middle" class="component">shared</text>
  <text x="470" y="295" text-anchor="middle" class="tech-stack">共享模块</text>
  
  <!-- 状态层 -->
  <text x="90" y="330" class="component">状态层</text>
  <rect x="150" y="315" width="100" height="35" class="component-box" rx="3"/>
  <text x="200" y="330" text-anchor="middle" class="component">BasicDataStore</text>
  <text x="200" y="342" text-anchor="middle" class="tech-stack">Zustand</text>
  
  <rect x="260" y="315" width="100" height="35" class="component-box" rx="3"/>
  <text x="310" y="330" text-anchor="middle" class="component">StyleStore</text>
  <text x="310" y="342" text-anchor="middle" class="tech-stack">样式状态</text>
  
  <rect x="370" y="315" width="100" height="35" class="component-box" rx="3"/>
  <text x="420" y="330" text-anchor="middle" class="component">GridConfigStore</text>
  <text x="420" y="342" text-anchor="middle" class="tech-stack">配置状态</text>
  
  <rect x="480" y="315" width="100" height="35" class="component-box" rx="3"/>
  <text x="530" y="330" text-anchor="middle" class="component">DynamicStyleStore</text>
  <text x="530" y="342" text-anchor="middle" class="tech-stack">动态样式</text>
  
  <!-- 数据层 -->
  <text x="650" y="230" class="component">数据访问层</text>
  <rect x="720" y="215" width="100" height="35" class="component-box" rx="3"/>
  <text x="770" y="230" text-anchor="middle" class="component">API Client</text>
  <text x="770" y="242" text-anchor="middle" class="tech-stack">SWR缓存</text>
  
  <rect x="830" y="215" width="100" height="35" class="component-box" rx="3"/>
  <text x="880" y="230" text-anchor="middle" class="component">LocalStorage</text>
  <text x="880" y="242" text-anchor="middle" class="tech-stack">本地缓存</text>
  
  <!-- API网关层 -->
  <rect x="50" y="380" width="1100" height="80" class="layer-bg" rx="5"/>
  <text x="70" y="405" class="layer-title">API网关层</text>
  <rect x="200" y="395" width="150" height="50" class="api-box" rx="3"/>
  <text x="275" y="415" text-anchor="middle" class="component">FastAPI路由</text>
  <text x="275" y="430" text-anchor="middle" class="tech-stack">/api/v1/*</text>
  
  <rect x="400" y="395" width="150" height="50" class="api-box" rx="3"/>
  <text x="475" y="415" text-anchor="middle" class="component">中间件层</text>
  <text x="475" y="430" text-anchor="middle" class="tech-stack">CORS + 验证</text>
  
  <rect x="600" y="395" width="150" height="50" class="api-box" rx="3"/>
  <text x="675" y="415" text-anchor="middle" class="component">响应处理</text>
  <text x="675" y="430" text-anchor="middle" class="tech-stack">JSON序列化</text>
  
  <!-- 后端服务层 -->
  <rect x="50" y="480" width="1100" height="120" class="layer-bg" rx="5"/>
  <text x="70" y="505" class="layer-title">后端服务层</text>
  
  <rect x="150" y="515" width="120" height="40" class="component-box" rx="3"/>
  <text x="210" y="530" text-anchor="middle" class="component">项目管理</text>
  <text x="210" y="545" text-anchor="middle" class="tech-stack">Project模型</text>
  
  <rect x="280" y="515" width="120" height="40" class="component-box" rx="3"/>
  <text x="340" y="530" text-anchor="middle" class="component">数据管理</text>
  <text x="340" y="545" text-anchor="middle" class="tech-stack">GridData模型</text>
  
  <rect x="410" y="515" width="120" height="40" class="component-box" rx="3"/>
  <text x="470" y="530" text-anchor="middle" class="component">版本控制</text>
  <text x="470" y="545" text-anchor="middle" class="tech-stack">Version模型</text>
  
  <rect x="540" y="515" width="120" height="40" class="component-box" rx="3"/>
  <text x="600" y="530" text-anchor="middle" class="component">业务逻辑</text>
  <text x="600" y="545" text-anchor="middle" class="tech-stack">Pydantic验证</text>
  
  <rect x="150" y="565" width="200" height="25" class="component-box" rx="3"/>
  <text x="250" y="580" text-anchor="middle" class="component">SQLModel ORM层</text>
  
  <rect x="370" y="565" width="200" height="25" class="component-box" rx="3"/>
  <text x="470" y="580" text-anchor="middle" class="component">Alembic数据迁移</text>
  
  <!-- 数据持久层 -->
  <rect x="50" y="620" width="1100" height="80" class="layer-bg" rx="5"/>
  <text x="70" y="645" class="layer-title">数据持久层</text>
  <rect x="200" y="635" width="150" height="50" class="data-box" rx="3"/>
  <text x="275" y="655" text-anchor="middle" class="component">SQLite (开发)</text>
  <text x="275" y="670" text-anchor="middle" class="tech-stack">本地数据库</text>
  
  <rect x="400" y="635" width="150" height="50" class="data-box" rx="3"/>
  <text x="475" y="655" text-anchor="middle" class="component">PostgreSQL (生产)</text>
  <text x="475" y="670" text-anchor="middle" class="tech-stack">云端数据库</text>
  
  <rect x="600" y="635" width="150" height="50" class="data-box" rx="3"/>
  <text x="675" y="655" text-anchor="middle" class="component">数据备份</text>
  <text x="675" y="670" text-anchor="middle" class="tech-stack">自动备份</text>
  
  <!-- 性能指标 -->
  <rect x="850" y="280" width="280" height="120" class="component-box" rx="5"/>
  <text x="990" y="300" text-anchor="middle" class="layer-title">架构性能指标</text>
  <text x="870" y="320" class="metric">• 主文件重构：7400+ → 82行 (98.9%↓)</text>
  <text x="870" y="335" class="metric">• useState减少：80+ → 10个 (87%↓)</text>
  <text x="870" y="350" class="metric">• 组件化程度：95%+</text>
  <text x="870" y="365" class="metric">• 网格渲染：1089个单元格同时</text>
  <text x="870" y="380" class="metric">• TypeScript覆盖：100%</text>
  
  <!-- 数据流箭头 -->
  <line x1="275" y1="145" x2="275" y2="180" class="arrow"/>
  <line x1="475" y1="145" x2="475" y2="180" class="arrow"/>
  
  <line x1="210" y1="255" x2="210" y2="265" class="arrow"/>
  <line x1="340" y1="255" x2="340" y2="265" class="arrow"/>
  <line x1="470" y1="255" x2="470" y2="265" class="arrow"/>
  
  <line x1="275" y1="305" x2="275" y2="315" class="arrow"/>
  <line x1="405" y1="305" x2="405" y2="315" class="arrow"/>
  
  <line x1="770" y1="250" x2="770" y2="380" class="data-flow"/>
  <line x1="880" y1="250" x2="880" y2="315" class="data-flow"/>
  
  <line x1="475" y1="360" x2="475" y2="395" class="arrow"/>
  <line x1="475" y1="445" x2="475" y2="480" class="arrow"/>
  <line x1="475" y1="600" x2="475" y2="620" class="arrow"/>
  
  <!-- 图例 -->
  <rect x="50" y="720" width="300" height="60" class="component-box" rx="3"/>
  <text x="60" y="740" class="component">图例：</text>
  <line x1="60" y1="750" x2="90" y2="750" class="arrow"/>
  <text x="95" y="755" class="tech-stack">组件调用</text>
  <line x1="160" y1="750" x2="190" y2="750" class="data-flow"/>
  <text x="195" y="755" class="tech-stack">数据流</text>
  
</svg>
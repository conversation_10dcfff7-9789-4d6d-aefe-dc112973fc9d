# 矩阵渲染系统数据流图说明

## 概述

本文档详细说明了 `/Users/<USER>/Desktop/cube1_group/apps/frontend/lib/rendering/` 目录下矩阵渲染系统的各种数据流情况，并提供了完整的 Mermaid 语法图表。

## 系统架构层次

### 1. 数据源层 (Data Sources)

- **GROUP_A_DATA**: 基础颜色坐标数据，包含所有颜色在不同级别的坐标信息
- **SPECIAL_COORDINATES**: 特殊黑色坐标映射，将特定坐标映射为字母 A-M
- **DEFAULT_COLOR_VALUES**: 颜色值配置，包含颜色的 hex 值和 mappingValue
- **GridConfig**: 网格配置，控制显示模式和行为
- **StyleConfig**: 样式配置，控制单元格大小、间距等视觉属性

### 2. 服务层 (Services)

- **ColorCoordinateService**: 提供颜色坐标查询功能，基于 GROUP_A_DATA 构建高效映射
- **SpecialCoordinateService**: 处理特殊坐标与字符的双向映射
- **ColorMappingService**: 颜色类型与映射值的转换服务

### 3. 状态管理层 (State Management)

- **BasicDataStore**: 管理基础数据状态，包括坐标映射、可见性控制
- **GridConfigStore**: 管理网格配置状态，控制显示模式切换
- **StyleStore**: 管理样式状态，控制视觉呈现

### 4. 业务逻辑层 (Business Logic)

- **useGridDataManager**: 数据管理 Hook，处理业务逻辑
- **useGridData**: 网格数据 Hook，提供数据访问接口
- **GridRenderingEngine**: 核心渲染引擎，统一处理所有渲染逻辑
- **PerformanceOptimizer**: 性能优化器，提供缓存和批量处理

### 5. Hook层 (React Hooks)

- **useGridRenderingEngine**: 渲染引擎 Hook，为组件提供渲染接口
- **useCellRenderData**: 单元格渲染 Hook，简化单个单元格数据获取

### 6. 渲染层 (Rendering)

- **CellRenderData**: 单元格渲染数据结构
- **GridCell Component**: 网格单元格组件
- **GridMatrix Component**: 网格矩阵组件

## 渲染情况详解

### 1. 坐标模式 (coordinates)

**数据流路径**:
```
GridConfig → GridConfigStore → GridRenderingEngine → calculateCellContent()
```

**渲染逻辑**:
- 显示格式: `"x,y"`
- 例如: `"0,0"`, `"1,-2"`
- 所有单元格都显示其坐标信息

### 2. 颜色模式 (color)

**数据流路径**:
```
GROUP_A_DATA → ColorCoordinateService → BasicDataStore → GridRenderingEngine → calculateCellColor()
```

**渲染逻辑**:
- 单元格内容: 空白 (null)
- 背景颜色: 根据坐标查找对应颜色
- 特殊处理: 黑色坐标显示黑色背景

### 3. 数值模式 (value)

**数据流路径**:
```
DEFAULT_COLOR_VALUES → ColorMappingService → GridRenderingEngine → calculateCellContent()
SPECIAL_COORDINATES → SpecialCoordinateService → GridRenderingEngine → calculateCellContent()
```

**渲染逻辑**:
- 彩色单元格: 显示数字 1-8 (基于 colorMappingValue)
- 黑色单元格: 显示字母 A-M (基于特殊坐标映射)
- 空白单元格: 不显示内容

### 4. 特殊黑色坐标处理

**数据流路径**:
```
SPECIAL_COORDINATES → SpecialCoordinateService.getCharacter() → GridRenderingEngine
```

**渲染逻辑**:
- 优先级最高，覆盖其他渲染逻辑
- 背景色: 黑色 (#000000)
- 内容: 对应字母 (A-M)

### 5. 彩色单元格数值映射

**数据流路径**:
```
colorMappingValue → ColorMappingService.getColorTypeFromMappingValue() → GridRenderingEngine
```

**渲染逻辑**:
- 根据 colorMappingValue (1-8) 确定颜色类型
- 根据 level 确定颜色深度
- 在 value 模式下显示对应数字

### 6. 可见性过滤

**数据流路径**:
```
BasicDataStore.colorVisibility → useGridData.isCellActive() → GridRenderingEngine
BasicDataStore.groupVisibility → useGridData.isCellActive() → GridRenderingEngine
```

**渲染逻辑**:
- 颜色可见性: 控制特定颜色是否显示
- 组别可见性: 控制特定组别是否显示
- 不可见单元格: 透明度降低或完全隐藏

## 性能优化机制

### 1. 渲染缓存 (Render Cache)

```typescript
private renderCache = new Map<CacheKey, CellRenderData>();
```

- 缓存已计算的渲染数据
- 避免重复计算相同单元格
- 配置变化时自动清空缓存

### 2. 批量处理 (Batch Processing)

```typescript
getBatchCellRenderData(cells: CellData[]): Map<string, CellRenderData>
```

- 一次性处理多个单元格
- 减少函数调用开销
- 支持异步批量处理

### 3. 智能重渲染 (Smart Re-render)

- 配置变化检测
- 仅在必要时触发重渲染
- 使用 useMemo 和 useCallback 优化

### 4. 防抖调度 (Debounce)

```typescript
debounceRender(callback: () => void): void
```

- 防止频繁渲染调用
- 使用 requestAnimationFrame 优化
- 可配置防抖延迟

## 关键数据结构

### CellData 接口

```typescript
interface CellData {
  id: string;
  row: number;
  col: number;
  x: number;  // 网格坐标
  y: number;  // 网格坐标
  index: number;
  color: string | null;
  colorMappingValue: number;
  level: number;
  group: number | null;
  isActive: boolean;
  number: number;
}
```

### CellRenderData 接口

```typescript
interface CellRenderData {
  color: string | null;        // 背景颜色
  content: string | null;      // 显示内容
  style: React.CSSProperties;  // CSS 样式
  className: string;           // CSS 类名
  isActive: boolean;           // 激活状态
  isVisible: boolean;          // 可见状态
}
```

### RenderingConfig 接口

```typescript
interface RenderingConfig {
  displayMode: BaseDisplayMode;  // 显示模式
  colorModeEnabled: boolean;     // 颜色模式开关
  cellSize: number;              // 单元格大小
  cellGap: number;               // 单元格间距
  showBorders: boolean;          // 显示边框
  enableAnimations: boolean;     // 启用动画
  opacity: number;               // 透明度
}
```

## 使用示例

### 基本使用

```typescript
// 获取渲染引擎
const { getCellRenderData, getBatchCellRenderData } = useGridRenderingEngine();

// 单个单元格渲染
const cellData = getCellRenderData(cell);

// 批量单元格渲染
const batchData = getBatchCellRenderData(cells);
```

### 性能优化使用

```typescript
// 异步批量渲染
const batchDataAsync = await getBatchCellRenderDataAsync(cells);

// 防抖渲染
debounceRender(() => {
  // 渲染逻辑
});

// 缓存管理
clearCache();
const stats = getCacheStats();
```

## Mermaid 图表文件

本系统提供了多种形式的数据流图表，适用于不同的查看需求：

### 1. 详细数据流图
**文件**: `/Users/<USER>/Desktop/cube1_group/docs/diagrams/matrix-rendering-dataflow.mmd`
- **特点**: 垂直布局，详细展示所有组件和数据流
- **适用**: 深入理解系统架构和数据流向
- **包含**: 完整的渲染情况、性能优化机制

### 2. 架构概览图
**文件**: `/Users/<USER>/Desktop/cube1_group/docs/diagrams/matrix-rendering-architecture.mmd`
- **特点**: 水平布局，分层架构清晰
- **适用**: 快速了解系统整体架构
- **包含**: 主要层次和核心组件关系

### 3. 分层架构图
**文件**: `/Users/<USER>/Desktop/cube1_group/docs/diagrams/matrix-rendering-layers.mmd`
- **特点**: 垂直分层，强调层次关系
- **适用**: 理解系统分层设计和数据流向
- **包含**: 用户交互到数据层的完整流程

### 4. 系统架构图
**文件**: `/Users/<USER>/Desktop/cube1_group/docs/diagrams/matrix-rendering-system.mmd`
- **特点**: 水平流程图，类似传统架构图
- **适用**: 技术文档和架构展示
- **包含**: 核心模块和主要数据流

### 5. 用户交互数据流图
**文件**: `/Users/<USER>/Desktop/cube1_group/docs/diagrams/user-interaction-dataflow.mmd`
- **特点**: 详细展示用户交互的完整流程
- **适用**: 理解用户操作如何触发系统响应
- **包含**: 事件处理、状态更新、视觉反馈、错误处理等完整交互链路

### 查看工具
可以使用支持 Mermaid 的工具查看，如:
- GitHub (自动渲染)
- VS Code (Mermaid Preview 插件)
- Mermaid Live Editor (https://mermaid.live/)
- 各种文档平台 (GitBook, Notion 等)

## 总结

矩阵渲染系统通过分层架构实现了:

1. **数据源的统一管理**: 通过服务层抽象数据访问
2. **渲染逻辑的集中处理**: GridRenderingEngine 统一所有渲染计算
3. **性能的全面优化**: 缓存、批量处理、防抖等多重优化
4. **组件的解耦设计**: Hook 层提供清晰的接口抽象
5. **多种渲染模式**: 支持坐标、颜色、数值等不同显示需求

这种设计确保了系统的可维护性、性能和扩展性。
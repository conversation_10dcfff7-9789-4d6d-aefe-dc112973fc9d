graph TB
    %% 用户交互层
    subgraph UI ["🖱️ 用户交互层"]
        U1["用户操作"]
        U2["配置变更"]
        U3["模式切换"]
    end

    %% 组件层
    subgraph COMP ["🧩 组件层"]
        C1["GridMatrix"]
        C2["GridCell"]
        C3["控制面板"]
    end

    %% Hook层
    subgraph HOOK ["🎣 Hook层"]
        H1["useGridRenderingEngine"]
        H2["useCellRenderData"]
        H3["useGridDataManager"]
    end

    %% 核心引擎层
    subgraph ENGINE ["⚙️ 核心引擎层"]
        E1["GridRenderingEngine"]
        E2["PerformanceOptimizer"]
        E3["CacheManager"]
    end

    %% 业务逻辑层
    subgraph LOGIC ["📋 业务逻辑层"]
        L1["渲染计算"]
        L2["模式处理"]
        L3["数据转换"]
    end

    %% 服务层
    subgraph SERVICE ["🔧 服务层"]
        S1["ColorCoordinateService"]
        S2["SpecialCoordinateService"]
        S3["ColorMappingService"]
    end

    %% 状态管理层
    subgraph STATE ["📦 状态管理层"]
        ST1["BasicDataStore"]
        ST2["GridConfigStore"]
        ST3["StyleStore"]
    end

    %% 数据层
    subgraph DATA ["🗄️ 数据层"]
        D1["GROUP_A_DATA"]
        D2["SPECIAL_COORDINATES"]
        D3["DEFAULT_COLOR_VALUES"]
        D4["GridConfig"]
    end

    %% 渲染输出
    subgraph OUTPUT ["🎨 渲染输出"]
        O1["坐标显示"]
        O2["颜色渲染"]
        O3["数值显示"]
        O4["特殊字符"]
    end

    %% 数据流连接
    UI --> COMP
    COMP --> HOOK
    HOOK --> ENGINE
    ENGINE --> LOGIC
    LOGIC --> SERVICE
    SERVICE --> STATE
    STATE --> DATA

    %% 渲染流
    ENGINE --> OUTPUT
    LOGIC --> OUTPUT

    %% 反馈流
    OUTPUT -.-> COMP
    STATE -.-> HOOK

    %% 详细连接
    U1 --> C1
    U2 --> C3
    U3 --> C3

    C1 --> H1
    C2 --> H2
    C3 --> H3

    H1 --> E1
    H2 --> E1
    H3 --> E1

    E1 --> L1
    E2 --> L1
    E3 --> L1

    L1 --> S1
    L2 --> S2
    L3 --> S3

    S1 --> ST1
    S2 --> ST1
    S3 --> ST1
    ST2 --> L2
    ST3 --> L2

    ST1 --> D1
    ST1 --> D2
    ST1 --> D3
    ST2 --> D4

    L1 --> O1
    L1 --> O2
    L2 --> O3
    L2 --> O4

    %% 样式定义
    classDef userLayer fill:#ffebee,stroke:#d32f2f,stroke-width:3px,color:#000
    classDef compLayer fill:#e8eaf6,stroke:#3f51b5,stroke-width:3px,color:#000
    classDef hookLayer fill:#e1f5fe,stroke:#0288d1,stroke-width:3px,color:#000
    classDef engineLayer fill:#e0f2f1,stroke:#00796b,stroke-width:3px,color:#000
    classDef logicLayer fill:#f3e5f5,stroke:#7b1fa2,stroke-width:3px,color:#000
    classDef serviceLayer fill:#fff3e0,stroke:#f57c00,stroke-width:3px,color:#000
    classDef stateLayer fill:#e8f5e8,stroke:#388e3c,stroke-width:3px,color:#000
    classDef dataLayer fill:#e3f2fd,stroke:#1976d2,stroke-width:3px,color:#000
    classDef outputLayer fill:#fff8e1,stroke:#ffa000,stroke-width:3px,color:#000

    class UI,U1,U2,U3 userLayer
    class COMP,C1,C2,C3 compLayer
    class HOOK,H1,H2,H3 hookLayer
    class ENGINE,E1,E2,E3 engineLayer
    class LOGIC,L1,L2,L3 logicLayer
    class SERVICE,S1,S2,S3 serviceLayer
    class STATE,ST1,ST2,ST3 stateLayer
    class DATA,D1,D2,D3,D4 dataLayer
    class OUTPUT,O1,O2,O3,O4 outputLayer
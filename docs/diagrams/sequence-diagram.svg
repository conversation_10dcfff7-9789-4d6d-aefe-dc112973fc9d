<svg width="1600" height="1000" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: 'Arial', sans-serif; font-size: 24px; font-weight: bold; fill: #2c3e50; }
      .subtitle { font-family: 'Arial', sans-serif; font-size: 14px; fill: #34495e; }
      .actor-title { font-family: 'Arial', sans-serif; font-size: 12px; font-weight: bold; fill: #2c3e50; }
      .message-text { font-family: 'Arial', sans-serif; font-size: 10px; fill: #2c3e50; }
      .time-text { font-family: 'Arial', sans-serif; font-size: 9px; fill: #7f8c8d; }
      .note-text { font-family: 'Arial', sans-serif; font-size: 9px; fill: #e67e22; }
      .actor-box { fill: #ecf0f1; stroke: #34495e; stroke-width: 2; }
      .lifeline { stroke: #bdc3c7; stroke-width: 2; stroke-dasharray: 5,5; }
      .message-arrow { stroke: #2c3e50; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
      .return-arrow { stroke: #27ae60; stroke-width: 1.5; fill: none; marker-end: url(#arrowhead-green); stroke-dasharray: 3,3; }
      .async-arrow { stroke: #e74c3c; stroke-width: 2; fill: none; marker-end: url(#arrowhead-red); }
      .self-arrow { stroke: #9b59b6; stroke-width: 1.5; fill: none; marker-end: url(#arrowhead-purple); }
      .activation { fill: #3498db; stroke: #2980b9; stroke-width: 1; opacity: 0.7; }
      .note-box { fill: #fff3cd; stroke: #ffc107; stroke-width: 1; }
    </style>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#2c3e50" />
    </marker>
    <marker id="arrowhead-green" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#27ae60" />
    </marker>
    <marker id="arrowhead-red" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#e74c3c" />
    </marker>
    <marker id="arrowhead-purple" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#9b59b6" />
    </marker>
  </defs>
  
  <!-- 标题 -->
  <text x="800" y="30" text-anchor="middle" class="title">网格矩阵可视化系统 - 组件交互序列图</text>
  <text x="800" y="50" text-anchor="middle" class="subtitle">用户操作、应用初始化、样式更新的时序交互</text>
  
  <!-- 参与者 -->
  <rect x="50" y="80" width="80" height="40" class="actor-box" rx="5"/>
  <text x="90" y="105" text-anchor="middle" class="actor-title">User</text>
  
  <rect x="180" y="80" width="100" height="40" class="actor-box" rx="5"/>
  <text x="230" y="105" text-anchor="middle" class="actor-title">GridMatrix</text>
  
  <rect x="330" y="80" width="100" height="40" class="actor-box" rx="5"/>
  <text x="380" y="105" text-anchor="middle" class="actor-title">EventHandler</text>
  
  <rect x="480" y="80" width="120" height="40" class="actor-box" rx="5"/>
  <text x="540" y="105" text-anchor="middle" class="actor-title">BasicDataStore</text>
  
  <rect x="650" y="80" width="100" height="40" class="actor-box" rx="5"/>
  <text x="700" y="105" text-anchor="middle" class="actor-title">LocalStorage</text>
  
  <rect x="800" y="80" width="100" height="40" class="actor-box" rx="5"/>
  <text x="850" y="105" text-anchor="middle" class="actor-title">API Client</text>
  
  <rect x="950" y="80" width="80" height="40" class="actor-box" rx="5"/>
  <text x="990" y="105" text-anchor="middle" class="actor-title">FastAPI</text>
  
  <rect x="1080" y="80" width="80" height="40" class="actor-box" rx="5"/>
  <text x="1120" y="105" text-anchor="middle" class="actor-title">SQLModel</text>
  
  <rect x="1210" y="80" width="80" height="40" class="actor-box" rx="5"/>
  <text x="1250" y="105" text-anchor="middle" class="actor-title">Database</text>
  
  <rect x="1340" y="80" width="100" height="40" class="actor-box" rx="5"/>
  <text x="1390" y="105" text-anchor="middle" class="actor-title">StylePanel</text>
  
  <rect x="1490" y="80" width="80" height="40" class="actor-box" rx="5"/>
  <text x="1530" y="105" text-anchor="middle" class="actor-title">StyleStore</text>
  
  <!-- 生命线 -->
  <line x1="90" y1="120" x2="90" y2="950" class="lifeline"/>
  <line x1="230" y1="120" x2="230" y2="950" class="lifeline"/>
  <line x1="380" y1="120" x2="380" y2="950" class="lifeline"/>
  <line x1="540" y1="120" x2="540" y2="950" class="lifeline"/>
  <line x1="700" y1="120" x2="700" y2="950" class="lifeline"/>
  <line x1="850" y1="120" x2="850" y2="950" class="lifeline"/>
  <line x1="990" y1="120" x2="990" y2="950" class="lifeline"/>
  <line x1="1120" y1="120" x2="1120" y2="950" class="lifeline"/>
  <line x1="1250" y1="120" x2="1250" y2="950" class="lifeline"/>
  <line x1="1390" y1="120" x2="1390" y2="950" class="lifeline"/>
  <line x1="1530" y1="120" x2="1530" y2="950" class="lifeline"/>
  
  <!-- 序列1：用户点击网格单元格 -->
  <text x="50" y="150" class="subtitle">序列1：用户点击网格单元格操作</text>
  
  <!-- 1. 用户点击 -->
  <line x1="90" y1="170" x2="230" y2="170" class="message-arrow"/>
  <text x="160" y="165" text-anchor="middle" class="message-text">1. 点击单元格(x,y)</text>
  <text x="160" y="185" text-anchor="middle" class="time-text">t=0ms</text>
  
  <!-- 激活GridMatrix -->
  <rect x="225" y="170" width="10" height="80" class="activation"/>
  
  <!-- 2. GridMatrix处理 -->
  <line x1="230" y1="190" x2="380" y2="190" class="message-arrow"/>
  <text x="305" y="185" text-anchor="middle" class="message-text">2. handleCellClick(cell, event)</text>
  <text x="305" y="205" text-anchor="middle" class="time-text">t=1ms</text>
  
  <!-- 激活usePageLogic -->
  <rect x="375" y="190" width="10" height="60" class="activation"/>
  
  <!-- 3. 事件处理 -->
  <line x1="380" y1="210" x2="540" y2="210" class="message-arrow"/>
  <text x="460" y="205" text-anchor="middle" class="message-text">3. 记录点击事件</text>
  <text x="460" y="225" text-anchor="middle" class="time-text">t=2ms</text>
  
  <!-- 激活BasicDataStore -->
  <rect x="535" y="210" width="10" height="120" class="activation"/>
  
  <!-- 4. Store更新 -->
  <line x1="540" y1="230" x2="700" y2="230" class="async-arrow"/>
  <text x="620" y="225" text-anchor="middle" class="message-text">4. updateCellData(id, updates)</text>
  <text x="620" y="245" text-anchor="middle" class="time-text">t=3ms (异步)</text>
  
  <!-- 5. LocalStorage同步 -->
  <rect x="695" y="230" width="10" height="30" class="activation"/>
  <line x1="700" y1="250" x2="540" y2="250" class="return-arrow"/>
  <text x="620" y="265" text-anchor="middle" class="message-text">5. 本地缓存更新完成</text>
  
  <!-- 6. API调用 -->
  <line x1="540" y1="270" x2="850" y2="270" class="async-arrow"/>
  <text x="695" y="265" text-anchor="middle" class="message-text">6. POST /api/v1/data/grid-cells</text>
  <text x="695" y="285" text-anchor="middle" class="time-text">t=5ms (异步)</text>
  
  <!-- 激活API Client -->
  <rect x="845" y="270" width="10" height="100" class="activation"/>
  
  <!-- 7. 后端API调用 -->
  <line x1="850" y1="290" x2="990" y2="290" class="message-arrow"/>
  <text x="920" y="285" text-anchor="middle" class="message-text">7. data.update_grid_cell</text>
  <text x="920" y="305" text-anchor="middle" class="time-text">t=10ms</text>
  
  <!-- 激活FastAPI -->
  <rect x="985" y="290" width="10" height="60" class="activation"/>
  
  <!-- 8. 数据模型处理 -->
  <line x1="990" y1="310" x2="1120" y2="310" class="message-arrow"/>
  <text x="1055" y="305" text-anchor="middle" class="message-text">8. 数据验证和转换</text>
  <text x="1055" y="325" text-anchor="middle" class="time-text">t=15ms</text>
  
  <!-- 激活SQLModel -->
  <rect x="1115" y="310" width="10" height="40" class="activation"/>
  
  <!-- 9. 数据库更新 -->
  <line x1="1120" y1="330" x2="1250" y2="330" class="message-arrow"/>
  <text x="1185" y="325" text-anchor="middle" class="message-text">9. UPDATE操作</text>
  <text x="1185" y="345" text-anchor="middle" class="time-text">t=25ms</text>
  
  <!-- 激活Database -->
  <rect x="1245" y="330" width="10" height="20" class="activation"/>
  
  <!-- 返回响应链 -->
  <line x1="1250" y1="350" x2="1120" y2="350" class="return-arrow"/>
  <line x1="1120" y1="360" x2="990" y2="360" class="return-arrow"/>
  <line x1="990" y1="370" x2="850" y2="370" class="return-arrow"/>
  <line x1="850" y1="380" x2="540" y2="380" class="return-arrow"/>
  
  <!-- 10. UI更新 -->
  <line x1="540" y1="390" x2="230" y2="390" class="return-arrow"/>
  <text x="385" y="385" text-anchor="middle" class="message-text">10. 触发UI重渲染</text>
  <text x="385" y="405" text-anchor="middle" class="time-text">t=50ms</text>
  
  <!-- 序列2：应用初始化 -->
  <text x="50" y="450" class="subtitle">序列2：应用初始化加载</text>
  
  <!-- 应用启动 -->
  <line x1="90" y1="470" x2="540" y2="470" class="message-arrow"/>
  <text x="315" y="465" text-anchor="middle" class="message-text">1. 应用启动 - Store初始化</text>
  <text x="315" y="485" text-anchor="middle" class="time-text">t=0ms</text>
  
  <!-- 激活Store -->
  <rect x="535" y="470" width="10" height="120" class="activation"/>
  
  <!-- LocalStorage读取 -->
  <line x1="540" y1="490" x2="700" y2="490" class="message-arrow"/>
  <text x="620" y="485" text-anchor="middle" class="message-text">2. 读取缓存数据</text>
  <text x="620" y="505" text-anchor="middle" class="time-text">t=5ms</text>
  
  <rect x="695" y="490" width="10" height="20" class="activation"/>
  <line x1="700" y1="510" x2="540" y2="510" class="return-arrow"/>
  
  <!-- API数据同步 -->
  <line x1="540" y1="530" x2="850" y2="530" class="async-arrow"/>
  <text x="695" y="525" text-anchor="middle" class="message-text">3. GET /api/v1/projects/{id}/data</text>
  <text x="695" y="545" text-anchor="middle" class="time-text">t=10ms (异步)</text>
  
  <rect x="845" y="530" width="10" height="60" class="activation"/>
  
  <!-- 后端数据获取 -->
  <line x1="850" y1="550" x2="990" y2="550" class="message-arrow"/>
  <text x="920" y="545" text-anchor="middle" class="message-text">4. 获取项目数据</text>
  
  <rect x="985" y="550" width="10" height="40" class="activation"/>
  <line x1="990" y1="570" x2="1250" y2="570" class="message-arrow"/>
  <text x="1120" y="565" text-anchor="middle" class="message-text">5. 查询数据库</text>
  
  <rect x="1245" y="570" width="10" height="20" class="activation"/>
  
  <!-- 数据返回和UI渲染 -->
  <line x1="1250" y1="590" x2="230" y2="590" class="return-arrow"/>
  <text x="740" y="585" text-anchor="middle" class="message-text">6. 数据同步完成 → UI渲染</text>
  <text x="740" y="605" text-anchor="middle" class="time-text">t=100ms</text>
  
  <!-- 序列3：样式配置更新 -->
  <text x="50" y="650" class="subtitle">序列3：样式配置更新</text>
  
  <!-- 用户样式操作 -->
  <line x1="90" y1="670" x2="1390" y2="670" class="message-arrow"/>
  <text x="740" y="665" text-anchor="middle" class="message-text">1. 用户更改显示模式</text>
  <text x="740" y="685" text-anchor="middle" class="time-text">t=0ms</text>
  
  <!-- 激活StylePanel -->
  <rect x="1385" y="670" width="10" height="80" class="activation"/>
  
  <!-- StylePanel处理 -->
  <line x1="1390" y1="690" x2="1530" y2="690" class="message-arrow"/>
  <text x="1460" y="685" text-anchor="middle" class="message-text">2. onDisplayModeChange(mode)</text>
  <text x="1460" y="705" text-anchor="middle" class="time-text">t=1ms</text>
  
  <!-- 激活StyleStore -->
  <rect x="1525" y="690" width="10" height="60" class="activation"/>
  
  <!-- 样式计算 -->
  <path d="M 1530 710 Q 1550 710 1550 730 Q 1550 750 1530 750" class="self-arrow"/>
  <text x="1560" y="730" class="message-text">3. 计算动态样式</text>
  <text x="1560" y="745" class="time-text">t=3ms</text>
  
  <!-- 通知GridMatrix重渲染 -->
  <line x1="1530" y1="770" x2="230" y2="770" class="async-arrow"/>
  <text x="880" y="765" text-anchor="middle" class="message-text">4. 触发GridMatrix重渲染 (1089个单元格)</text>
  <text x="880" y="785" text-anchor="middle" class="time-text">t=5ms (异步)</text>
  
  <!-- GridMatrix重渲染 -->
  <rect x="225" y="770" width="10" height="40" class="activation"/>
  
  <!-- 样式持久化 -->
  <line x1="1530" y1="790" x2="700" y2="790" class="async-arrow"/>
  <text x="1115" y="785" text-anchor="middle" class="message-text">5. 配置持久化到LocalStorage</text>
  <text x="1115" y="805" text-anchor="middle" class="time-text">t=8ms (异步)</text>
  
  <rect x="695" y="790" width="10" height="20" class="activation"/>
  
  <!-- 性能注释 -->
  <rect x="50" y="850" width="300" height="80" class="note-box" rx="5"/>
  <text x="60" y="870" class="note-text">性能优化要点：</text>
  <text x="60" y="885" class="note-text">• React.memo防止不必要重渲染</text>
  <text x="60" y="900" class="note-text">• Zustand状态管理减少prop drilling</text>
  <text x="60" y="915" class="note-text">• LocalStorage + API双重缓存策略</text>
  
  <!-- 错误处理注释 -->
  <rect x="400" y="850" width="300" height="80" class="note-box" rx="5"/>
  <text x="410" y="870" class="note-text">错误处理机制：</text>
  <text x="410" y="885" class="note-text">• GridErrorBoundary组件级错误捕获</text>
  <text x="410" y="900" class="note-text">• API调用失败自动重试</text>
  <text x="410" y="915" class="note-text">• LocalStorage作为数据备份</text>
  
  <!-- 架构特性注释 -->
  <rect x="750" y="850" width="300" height="80" class="note-box" rx="5"/>
  <text x="760" y="870" class="note-text">架构特性：</text>
  <text x="760" y="885" class="note-text">• 异步操作不阻塞UI响应</text>
  <text x="760" y="900" class="note-text">• 分层架构清晰职责分离</text>
  <text x="760" y="915" class="note-text">• 状态管理现代化 (Zustand)</text>
  
  <!-- 技术栈注释 -->
  <rect x="1100" y="850" width="300" height="80" class="note-box" rx="5"/>
  <text x="1110" y="870" class="note-text">技术栈版本：</text>
  <text x="1110" y="885" class="note-text">• Next.js 15.1.0 + React 18.3.1</text>
  <text x="1110" y="900" class="note-text">• FastAPI 0.104.1 + SQLModel</text>
  <text x="1110" y="915" class="note-text">• TypeScript 5.8.3 (100%覆盖)</text>
  
</svg>
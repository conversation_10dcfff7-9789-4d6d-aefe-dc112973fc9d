```mermaid
flowchart TD
    %% 网格矩阵可视化系统 - 业务流程图
    
    Start(["🚀 应用启动"]) --> InitCheck{"🔍 检查初始化状态"}
    
    %% 应用初始化流程
    InitCheck -->|首次启动| LoadDefault["📋 加载默认配置"]
    InitCheck -->|已有缓存| LoadCache["💾 读取本地缓存"]
    
    LoadDefault --> StoreInit["🏪 初始化Zustand Store"]
    LoadCache --> StoreInit
    
    StoreInit --> APISync{"🔄 API数据同步"}
    APISync -->|成功| DataMerge["🔀 合并本地和远程数据"]
    APISync -->|失败| UseCache["⚠️ 使用缓存数据"]
    
    DataMerge --> RenderUI["🎨 渲染用户界面"]
    UseCache --> RenderUI
    
    %% 用户交互流程
    RenderUI --> UserAction{"👆 用户操作"}
    
    %% 网格单元格操作
    UserAction -->|点击单元格| CellClick["🎯 单元格点击事件"]
    CellClick --> ValidateCell{"✅ 验证单元格"}
    ValidateCell -->|有效| UpdateLocal["💾 更新本地状态"]
    ValidateCell -->|无效| ShowError["❌ 显示错误信息"]
    
    UpdateLocal --> LocalSync["🔄 本地存储同步"]
    LocalSync --> APIUpdate{"📡 API更新请求"}
    
    APIUpdate -->|成功| ConfirmUpdate["✅ 确认更新"]
    APIUpdate -->|失败| RetryLogic{"🔄 重试逻辑"}
    
    RetryLogic -->|重试次数<3| APIUpdate
    RetryLogic -->|超过重试| FallbackLocal["⚠️ 回退到本地"]
    
    ConfirmUpdate --> RefreshUI["🔄 刷新UI"]
    FallbackLocal --> RefreshUI
    
    %% 样式配置流程
    UserAction -->|样式配置| StyleChange["🎨 样式变更"]
    StyleChange --> StyleValidate{"✅ 样式验证"}
    StyleValidate -->|有效| ApplyStyle["🎭 应用样式"]
    StyleValidate -->|无效| StyleError["❌ 样式错误"]
    
    ApplyStyle --> StyleCalc["🧮 动态样式计算"]
    StyleCalc --> GridRerender["🎯 网格重渲染"]
    GridRerender --> StylePersist["💾 样式持久化"]
    
    %% 项目管理流程
    UserAction -->|项目操作| ProjectAction{"📁 项目操作类型"}
    
    ProjectAction -->|创建项目| CreateProject["➕ 创建新项目"]
    ProjectAction -->|加载项目| LoadProject["📂 加载项目"]
    ProjectAction -->|保存项目| SaveProject["💾 保存项目"]
    ProjectAction -->|导出项目| ExportProject["📤 导出项目"]
    
    CreateProject --> ProjectValidate{"✅ 项目验证"}
    LoadProject --> ProjectValidate
    SaveProject --> ProjectValidate
    ExportProject --> ProjectValidate
    
    ProjectValidate -->|成功| ProjectAPI["📡 项目API调用"]
    ProjectValidate -->|失败| ProjectError["❌ 项目错误"]
    
    ProjectAPI --> ProjectSuccess["✅ 项目操作成功"]
    ProjectSuccess --> UpdateProjectState["🔄 更新项目状态"]
    
    %% 错误处理流程
    ShowError --> ErrorBoundary["🛡️ 错误边界处理"]
    StyleError --> ErrorBoundary
    ProjectError --> ErrorBoundary
    
    ErrorBoundary --> ErrorLog["📝 错误日志记录"]
    ErrorLog --> ErrorRecovery{"🔧 错误恢复"}
    
    ErrorRecovery -->|可恢复| RetryAction["🔄 重试操作"]
    ErrorRecovery -->|不可恢复| FallbackMode["⚠️ 降级模式"]
    
    RetryAction --> UserAction
    FallbackMode --> SafeMode["🛡️ 安全模式运行"]
    
    %% 性能优化流程
    RefreshUI --> PerformanceCheck{"⚡ 性能检查"}
    GridRerender --> PerformanceCheck
    StylePersist --> PerformanceCheck
    UpdateProjectState --> PerformanceCheck
    
    PerformanceCheck -->|性能良好| ContinueFlow["✅ 继续流程"]
    PerformanceCheck -->|性能问题| OptimizeRender["🚀 优化渲染"]
    
    OptimizeRender --> MemoCheck["🧠 React.memo检查"]
    MemoCheck --> LazyLoad["⏳ 懒加载优化"]
    LazyLoad --> ContinueFlow
    
    ContinueFlow --> UserAction
    SafeMode --> UserAction
    
    %% 样式定义
    classDef startEnd fill:#e8f5e8,stroke:#4caf50,stroke-width:3px
    classDef process fill:#e3f2fd,stroke:#2196f3,stroke-width:2px
    classDef decision fill:#fff3e0,stroke:#ff9800,stroke-width:2px
    classDef error fill:#ffebee,stroke:#f44336,stroke-width:2px
    classDef success fill:#e8f5e8,stroke:#4caf50,stroke-width:2px
    classDef performance fill:#f3e5f5,stroke:#9c27b0,stroke-width:2px
    
    class Start,SafeMode startEnd
    class LoadDefault,LoadCache,StoreInit,DataMerge,UseCache,RenderUI,CellClick,UpdateLocal,LocalSync,ConfirmUpdate,RefreshUI,StyleChange,ApplyStyle,StyleCalc,GridRerender,StylePersist,CreateProject,LoadProject,SaveProject,ExportProject,ProjectAPI,ProjectSuccess,UpdateProjectState,ErrorLog,RetryAction,FallbackMode,ContinueFlow process
    class InitCheck,APISync,UserAction,ValidateCell,APIUpdate,RetryLogic,StyleValidate,ProjectAction,ProjectValidate,ErrorBoundary,ErrorRecovery,PerformanceCheck decision
    class ShowError,StyleError,ProjectError,FallbackLocal error
    class OptimizeRender,MemoCheck,LazyLoad performance
```
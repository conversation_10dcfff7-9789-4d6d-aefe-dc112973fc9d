graph TD
    %% 用户交互数据流图
    subgraph "用户交互数据流 (User Interaction Data Flow)"
        
        %% 用户操作层
        subgraph "用户操作 (User Actions)"
            UA1["🖱️ 鼠标点击单元格"]
            UA2["⌨️ 键盘快捷键"]
            UA3["🎛️ 控制面板操作"]
            UA4["📱 触摸操作"]
            UA5["🔄 拖拽操作"]
        end

        %% 事件捕获层
        subgraph "事件捕获 (Event Capture)"
            EC1["onClick Handler"]
            EC2["onKeyDown Handler"]
            EC3["onChange Handler"]
            EC4["onTouchStart Handler"]
            EC5["onDragStart Handler"]
        end

        %% 事件处理层
        subgraph "事件处理 (Event Processing)"
            EP1["事件验证"]
            EP2["权限检查"]
            EP3["状态检查"]
            EP4["参数解析"]
            EP5["防抖处理"]
        end

        %% 业务逻辑层
        subgraph "业务逻辑 (Business Logic)"
            BL1["显示模式切换"]
            BL2["单元格选择"]
            BL3["颜色过滤"]
            BL4["组别切换"]
            BL5["样式调整"]
            BL6["数据导出"]
        end

        %% 状态更新层
        subgraph "状态更新 (State Updates)"
            SU1["GridConfigStore 更新"]
            SU2["BasicDataStore 更新"]
            SU3["StyleStore 更新"]
            SU4["UI 状态更新"]
            SU5["缓存清理"]
        end

        %% 渲染触发层
        subgraph "渲染触发 (Render Triggers)"
            RT1["组件重渲染"]
            RT2["Hook 重新计算"]
            RT3["渲染引擎调用"]
            RT4["性能优化"]
            RT5["批量更新"]
        end

        %% 视觉反馈层
        subgraph "视觉反馈 (Visual Feedback)"
            VF1["单元格高亮"]
            VF2["颜色变化"]
            VF3["动画效果"]
            VF4["加载状态"]
            VF5["错误提示"]
            VF6["成功提示"]
        end

        %% 主要交互流程连接
        UA1 --> EC1
        UA2 --> EC2
        UA3 --> EC3
        UA4 --> EC4
        UA5 --> EC5

        EC1 --> EP1
        EC2 --> EP2
        EC3 --> EP3
        EC4 --> EP4
        EC5 --> EP5

        EP1 --> BL2
        EP2 --> BL1
        EP3 --> BL3
        EP4 --> BL4
        EP5 --> BL5

        BL1 --> SU1
        BL2 --> SU2
        BL3 --> SU2
        BL4 --> SU2
        BL5 --> SU3
        BL6 --> SU4

        SU1 --> RT1
        SU2 --> RT2
        SU3 --> RT3
        SU4 --> RT4
        SU5 --> RT5

        RT1 --> VF1
        RT2 --> VF2
        RT3 --> VF3
        RT4 --> VF4
        RT5 --> VF6
    end

    %% 具体交互场景
    subgraph "交互场景 (Interaction Scenarios)"
        
        %% 模式切换场景
        subgraph "模式切换 (Mode Switch)"
            MS1["用户点击模式按钮"]
            MS2["验证模式有效性"]
            MS3["更新 displayMode"]
            MS4["清空渲染缓存"]
            MS5["触发重新渲染"]
            MS6["显示切换动画"]
            
            MS1 --> MS2 --> MS3 --> MS4 --> MS5 --> MS6
        end

        %% 单元格点击场景
        subgraph "单元格点击 (Cell Click)"
            CC1["用户点击单元格"]
            CC2["获取单元格坐标"]
            CC3["检查单元格状态"]
            CC4["更新选中状态"]
            CC5["高亮显示"]
            CC6["显示详细信息"]
            
            CC1 --> CC2 --> CC3 --> CC4 --> CC5 --> CC6
        end

        %% 颜色过滤场景
        subgraph "颜色过滤 (Color Filter)"
            CF1["用户切换颜色开关"]
            CF2["更新颜色可见性"]
            CF3["重新计算活跃状态"]
            CF4["批量更新单元格"]
            CF5["应用透明度效果"]
            
            CF1 --> CF2 --> CF3 --> CF4 --> CF5
        end

        %% 样式调整场景
        subgraph "样式调整 (Style Adjustment)"
            SA1["用户拖动滑块"]
            SA2["实时更新数值"]
            SA3["防抖处理"]
            SA4["更新样式配置"]
            SA5["重新计算布局"]
            SA6["平滑过渡动画"]
            
            SA1 --> SA2 --> SA3 --> SA4 --> SA5 --> SA6
        end
    end

    %% 错误处理流程
    subgraph "错误处理 (Error Handling)"
        EH1["捕获异常"]
        EH2["错误分类"]
        EH3["记录日志"]
        EH4["用户提示"]
        EH5["状态回滚"]
        EH6["重试机制"]
        
        EH1 --> EH2 --> EH3 --> EH4 --> EH5 --> EH6
    end

    %% 性能监控流程
    subgraph "性能监控 (Performance Monitoring)"
        PM1["交互开始时间"]
        PM2["处理耗时统计"]
        PM3["渲染耗时统计"]
        PM4["内存使用监控"]
        PM5["性能报告"]
        PM6["优化建议"]
        
        PM1 --> PM2 --> PM3 --> PM4 --> PM5 --> PM6
    end

    %% 样式定义
    classDef userAction fill:#ffebee,stroke:#d32f2f,stroke-width:2px,color:#000
    classDef eventCapture fill:#e8eaf6,stroke:#3f51b5,stroke-width:2px,color:#000
    classDef eventProcess fill:#e1f5fe,stroke:#0288d1,stroke-width:2px,color:#000
    classDef businessLogic fill:#e0f2f1,stroke:#00796b,stroke-width:2px,color:#000
    classDef stateUpdate fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px,color:#000
    classDef renderTrigger fill:#fff3e0,stroke:#f57c00,stroke-width:2px,color:#000
    classDef visualFeedback fill:#e8f5e8,stroke:#388e3c,stroke-width:2px,color:#000
    classDef scenario fill:#e3f2fd,stroke:#1976d2,stroke-width:2px,color:#000
    classDef errorHandle fill:#fce4ec,stroke:#c2185b,stroke-width:2px,color:#000
    classDef performance fill:#fff8e1,stroke:#ffa000,stroke-width:2px,color:#000

    class UA1,UA2,UA3,UA4,UA5 userAction
    class EC1,EC2,EC3,EC4,EC5 eventCapture
    class EP1,EP2,EP3,EP4,EP5 eventProcess
    class BL1,BL2,BL3,BL4,BL5,BL6 businessLogic
    class SU1,SU2,SU3,SU4,SU5 stateUpdate
    class RT1,RT2,RT3,RT4,RT5 renderTrigger
    class VF1,VF2,VF3,VF4,VF5,VF6 visualFeedback
    class MS1,MS2,MS3,MS4,MS5,MS6,CC1,CC2,CC3,CC4,CC5,CC6,CF1,CF2,CF3,CF4,CF5,SA1,SA2,SA3,SA4,SA5,SA6 scenario
    class EH1,EH2,EH3,EH4,EH5,EH6 errorHandle
    class PM1,PM2,PM3,PM4,PM5,PM6 performance
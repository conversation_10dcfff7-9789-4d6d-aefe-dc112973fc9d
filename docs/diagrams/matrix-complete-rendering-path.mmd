graph TD
    %% 应用启动阶段
    subgraph STARTUP ["🚀 应用启动阶段"]
        S1["应用初始化"]
        S2["组件挂载"]
        S3["GridMatrix组件加载"]
    end

    %% 数据初始化阶段
    subgraph INIT ["📊 数据初始化阶段"]
        I1["useGridData Hook调用"]
        I2["检查matrixData状态"]
        I3["触发initializeMatrixData()"]
        I4["调用generateMatrixData()"]
        I5["设置isHydrated状态"]
    end

    %% 数据源层
    subgraph DATASOURCE ["🗄️ 数据源层"]
        DS1["GROUP_A_DATA<br/>基础颜色坐标数据"]
        DS2["SPECIAL_COORDINATES<br/>特殊黑色坐标映射"]
        DS3["DEFAULT_COLOR_VALUES<br/>颜色值配置"]
        DS4["GROUP_OFFSET_CONFIGS<br/>组偏移配置"]
        DS5["GridConfig<br/>网格配置"]
    end

    %% 数据生成层
    subgraph GENERATION ["⚙️ 数据生成层"]
        G1["calculateGroupCoordinates<br/>计算组坐标"]
        G2["generateGridData<br/>生成33x33网格"]
        G3["构建MatrixData索引<br/>byCoordinate/byGroup/byColor/byLevel"]
        G4["生成coordinateMap<br/>坐标映射表"]
    end

    %% 服务层
    subgraph SERVICE ["🔧 服务层"]
        SV1["ColorCoordinateService<br/>颜色坐标查询"]
        SV2["SpecialCoordinateService<br/>特殊坐标处理"]
        SV3["ColorMappingService<br/>颜色映射转换"]
    end

    %% 状态管理层
    subgraph STATE ["📦 状态管理层"]
        ST1["BasicDataStore<br/>基础数据状态"]
        ST2["GridConfigStore<br/>网格配置状态"]
        ST3["StyleStore<br/>样式状态"]
        ST4["HydrationManager<br/>水合状态管理"]
    end

    %% 渲染引擎层
    subgraph ENGINE ["⚙️ 渲染引擎层"]
        E1["GridRenderingEngine<br/>核心渲染引擎"]
        E2["PerformanceOptimizer<br/>性能优化器"]
        E3["CacheManager<br/>缓存管理"]
        E4["BatchProcessor<br/>批量处理器"]
    end

    %% Hook层
    subgraph HOOK ["🎣 Hook层"]
        H1["useGridRenderingEngine<br/>渲染引擎Hook"]
        H2["useCellRenderData<br/>单元格渲染Hook"]
        H3["useGridDataManager<br/>数据管理Hook"]
        H4["useGridAnimation<br/>动画Hook"]
    end

    %% 渲染计算层
    subgraph CALCULATION ["🧮 渲染计算层"]
        C1["getCellRenderData<br/>获取单元格渲染数据"]
        C2["getBatchCellRenderData<br/>批量获取渲染数据"]
        C3["计算渲染模式<br/>坐标/颜色/数值/特殊字符"]
        C4["应用可见性过滤"]
        C5["生成CellRenderData"]
    end

    %% 组件渲染层
    subgraph COMPONENT ["🎨 组件渲染层"]
        CP1["GridMatrix组件"]
        CP2["GridCell组件"]
        CP3["GridErrorBoundary"]
        CP4["GridLoadingState"]
    end

    %% 渲染输出层
    subgraph OUTPUT ["🖼️ 渲染输出层"]
        O1["坐标显示模式"]
        O2["颜色渲染模式"]
        O3["数值显示模式"]
        O4["特殊字符模式<br/>A-M字母"]
        O5["最终DOM渲染"]
    end

    %% 性能优化流
    subgraph PERFORMANCE ["⚡ 性能优化"]
        P1["渲染缓存<br/>5秒缓存策略"]
        P2["批量处理<br/>减少重复计算"]
        P3["防抖调度<br/>避免频繁更新"]
        P4["智能重渲染<br/>按需更新"]
        P5["虚拟化渲染<br/>大数据集优化"]
    end

    %% 错误处理流
    subgraph ERROR ["🚨 错误处理"]
        ER1["数据完整性检查"]
        ER2["渲染时机控制"]
        ER3["加载超时处理"]
        ER4["错误边界捕获"]
    end

    %% 主要数据流连接
    S1 --> S2
    S2 --> S3
    S3 --> I1
    
    I1 --> I2
    I2 --> I3
    I3 --> I4
    I4 --> I5
    
    I4 --> DS1
    I4 --> DS2
    I4 --> DS3
    I4 --> DS4
    
    DS1 --> G1
    DS2 --> G1
    DS3 --> G2
    DS4 --> G1
    DS5 --> ST2
    
    G1 --> G3
    G2 --> G3
    G3 --> G4
    
    G1 --> SV1
    G1 --> SV2
    G2 --> SV3
    
    SV1 --> ST1
    SV2 --> ST1
    SV3 --> ST1
    I5 --> ST4
    
    ST1 --> E1
    ST2 --> E1
    ST3 --> E1
    ST4 --> E1
    
    E1 --> E2
    E1 --> E3
    E1 --> E4
    
    E1 --> H1
    E2 --> H1
    E3 --> H1
    E4 --> H1
    
    H1 --> H2
    H1 --> H3
    H1 --> H4
    
    H1 --> C1
    H2 --> C2
    C1 --> C3
    C2 --> C3
    C3 --> C4
    C4 --> C5
    
    C5 --> CP1
    CP1 --> CP2
    CP1 --> CP3
    CP1 --> CP4
    
    CP2 --> O1
    CP2 --> O2
    CP2 --> O3
    CP2 --> O4
    O1 --> O5
    O2 --> O5
    O3 --> O5
    O4 --> O5
    
    %% 性能优化连接
    E2 --> P1
    E2 --> P2
    E2 --> P3
    E2 --> P4
    E2 --> P5
    
    P1 --> C1
    P2 --> C2
    P3 --> H1
    P4 --> CP1
    P5 --> CP1
    
    %% 错误处理连接
    I2 --> ER1
    I3 --> ER2
    CP4 --> ER3
    CP3 --> ER4
    
    ER1 --> I3
    ER2 --> C1
    ER3 --> CP1
    ER4 --> O5
    
    %% 反馈循环
    O5 -.-> ST1
    CP1 -.-> H1
    C5 -.-> E3
    
    %% 样式定义
    classDef startupStyle fill:#ffebee,stroke:#d32f2f,stroke-width:3px,color:#000
    classDef initStyle fill:#e8eaf6,stroke:#3f51b5,stroke-width:3px,color:#000
    classDef dataStyle fill:#e3f2fd,stroke:#1976d2,stroke-width:2px,color:#000
    classDef generationStyle fill:#e0f2f1,stroke:#00796b,stroke-width:2px,color:#000
    classDef serviceStyle fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px,color:#000
    classDef stateStyle fill:#e8f5e8,stroke:#388e3c,stroke-width:2px,color:#000
    classDef engineStyle fill:#fff3e0,stroke:#f57c00,stroke-width:2px,color:#000
    classDef hookStyle fill:#fce4ec,stroke:#c2185b,stroke-width:2px,color:#000
    classDef calculationStyle fill:#f1f8e9,stroke:#689f38,stroke-width:2px,color:#000
    classDef componentStyle fill:#e1f5fe,stroke:#0288d1,stroke-width:2px,color:#000
    classDef outputStyle fill:#fff8e1,stroke:#ffa000,stroke-width:2px,color:#000
    classDef performanceStyle fill:#f9fbe7,stroke:#827717,stroke-width:2px,color:#000
    classDef errorStyle fill:#ffebee,stroke:#c62828,stroke-width:2px,color:#000
    
    class STARTUP,S1,S2,S3 startupStyle
    class INIT,I1,I2,I3,I4,I5 initStyle
    class DATASOURCE,DS1,DS2,DS3,DS4,DS5 dataStyle
    class GENERATION,G1,G2,G3,G4 generationStyle
    class SERVICE,SV1,SV2,SV3 serviceStyle
    class STATE,ST1,ST2,ST3,ST4 stateStyle
    class ENGINE,E1,E2,E3,E4 engineStyle
    class HOOK,H1,H2,H3,H4 hookStyle
    class CALCULATION,C1,C2,C3,C4,C5 calculationStyle
    class COMPONENT,CP1,CP2,CP3,CP4 componentStyle
    class OUTPUT,O1,O2,O3,O4,O5 outputStyle
    class PERFORMANCE,P1,P2,P3,P4,P5 performanceStyle
    class ERROR,ER1,ER2,ER3,ER4 errorStyle
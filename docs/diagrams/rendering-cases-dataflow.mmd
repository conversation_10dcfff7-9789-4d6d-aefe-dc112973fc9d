graph TD
    %% 渲染情况专用数据流图
    subgraph "渲染情况数据流 (Rendering Cases Data Flow)"
        
        %% 坐标模式数据流
        subgraph "坐标模式 (Coordinates Mode)"
            C1["GridConfig.displayMode = 'coordinates'"]
            C2["GridRenderingEngine.calculateCellContent()"]
            C3["返回: `${x},${y}`"]
            C1 --> C2 --> C3
        end

        %% 颜色模式数据流
        subgraph "颜色模式 (Color Mode)"
            CO1["GridConfig.displayMode = 'color'"]
            CO2["GROUP_A_DATA 坐标查找"]
            CO3["ColorCoordinateService.getCellColorByCoordinate()"]
            CO4["GridRenderingEngine.calculateCellColor()"]
            CO5["返回: 背景颜色, 内容为空"]
            CO1 --> CO2 --> CO3 --> CO4 --> CO5
        end

        %% 数值模式数据流
        subgraph "数值模式 (Value Mode)"
            V1["GridConfig.displayMode = 'value'"]
            V2{"检查坐标类型"}
            
            %% 特殊坐标分支
            V3["SPECIAL_COORDINATES 查找"]
            V4["SpecialCoordinateService.getCharacter()"]
            V5["返回: 字母 A-M"]
            
            %% 彩色坐标分支
            V6["colorMappingValue 查找"]
            V7["ColorMappingService.getColorTypeFromMappingValue()"]
            V8["返回: 数字 1-8"]
            
            %% 空白坐标分支
            V9["返回: null"]
            
            V1 --> V2
            V2 -->|"特殊黑色坐标"| V3 --> V4 --> V5
            V2 -->|"彩色坐标"| V6 --> V7 --> V8
            V2 -->|"空白坐标"| V9
        end

        %% 可见性过滤数据流
        subgraph "可见性过滤 (Visibility Filtering)"
            VF1["BasicDataStore.colorVisibility"]
            VF2["BasicDataStore.groupVisibility"]
            VF3["useGridData.isCellActive()"]
            VF4["GridRenderingEngine.calculateCellActive()"]
            VF5{"单元格是否可见?"}
            VF6["正常渲染"]
            VF7["透明/隐藏渲染"]
            
            VF1 --> VF3
            VF2 --> VF3
            VF3 --> VF4 --> VF5
            VF5 -->|"是"| VF6
            VF5 -->|"否"| VF7
        end

        %% 特殊处理数据流
        subgraph "特殊处理 (Special Processing)"
            SP1["任意渲染模式"]
            SP2["SpecialCoordinateService.getCharacter(x, y)"]
            SP3{"是否为特殊坐标?"}
            SP4["强制设置: 背景黑色 + 字母内容"]
            SP5["继续正常渲染流程"]
            
            SP1 --> SP2 --> SP3
            SP3 -->|"是"| SP4
            SP3 -->|"否"| SP5
        end

        %% 性能优化数据流
        subgraph "性能优化 (Performance Optimization)"
            PO1["渲染请求"]
            PO2{"缓存中存在?"}
            PO3["返回缓存数据"]
            PO4["执行渲染计算"]
            PO5["存入缓存"]
            PO6["返回渲染数据"]
            
            PO1 --> PO2
            PO2 -->|"是"| PO3
            PO2 -->|"否"| PO4 --> PO5 --> PO6
        end

        %% 批量处理数据流
        subgraph "批量处理 (Batch Processing)"
            BP1["批量渲染请求"]
            BP2["分批处理 (batchSize=100)"]
            BP3["requestAnimationFrame 调度"]
            BP4["逐批渲染"]
            BP5["合并结果"]
            BP6["返回完整数据"]
            
            BP1 --> BP2 --> BP3 --> BP4 --> BP5 --> BP6
        end
    end

    %% 数据流优先级
    subgraph "渲染优先级 (Rendering Priority)"
        PR1["1. 特殊黑色坐标 (最高优先级)"]
        PR2["2. 可见性过滤"]
        PR3["3. 显示模式渲染"]
        PR4["4. 样式应用"]
        
        PR1 --> PR2 --> PR3 --> PR4
    end

    %% 样式定义
    classDef coordinates fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    classDef color fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    classDef value fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    classDef visibility fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    classDef special fill:#fce4ec,stroke:#c2185b,stroke-width:2px
    classDef performance fill:#f1f8e9,stroke:#689f38,stroke-width:2px
    classDef batch fill:#e0f2f1,stroke:#00796b,stroke-width:2px
    classDef priority fill:#fff8e1,stroke:#ffa000,stroke-width:2px

    class C1,C2,C3 coordinates
    class CO1,CO2,CO3,CO4,CO5 color
    class V1,V2,V3,V4,V5,V6,V7,V8,V9 value
    class VF1,VF2,VF3,VF4,VF5,VF6,VF7 visibility
    class SP1,SP2,SP3,SP4,SP5 special
    class PO1,PO2,PO3,PO4,PO5,PO6 performance
    class BP1,BP2,BP3,BP4,BP5,BP6 batch
    class PR1,PR2,PR3,PR4 priority
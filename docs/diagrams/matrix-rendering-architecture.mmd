graph LR
    %% 数据源层
    subgraph DS ["📊 数据源层"]
        direction TB
        DS1["GROUP_A_DATA<br/>颜色坐标数据"]
        DS2["SPECIAL_COORDINATES<br/>特殊坐标映射"]
        DS3["DEFAULT_COLOR_VALUES<br/>颜色值配置"]
        DS4["GridConfig<br/>网格配置"]
    end

    %% 服务层
    subgraph SL ["🔧 服务层"]
        direction TB
        SL1["ColorCoordinateService<br/>坐标查询"]
        SL2["SpecialCoordinateService<br/>特殊处理"]
        SL3["ColorMappingService<br/>映射转换"]
    end

    %% 状态管理层
    subgraph SM ["📦 状态管理"]
        direction TB
        SM1["BasicDataStore<br/>基础数据"]
        SM2["GridConfigStore<br/>配置状态"]
        SM3["StyleStore<br/>样式状态"]
    end

    %% 业务逻辑层
    subgraph BL ["⚙️ 业务逻辑"]
        direction TB
        BL1["useGridDataManager<br/>数据管理"]
        BL2["GridRenderingEngine<br/>渲染引擎"]
        BL3["PerformanceOptimizer<br/>性能优化"]
    end

    %% Hook层
    subgraph HL ["🎣 Hook层"]
        direction TB
        HL1["useGridRenderingEngine<br/>渲染Hook"]
        HL2["useCellRenderData<br/>单元格Hook"]
    end

    %% 渲染层
    subgraph RL ["🎨 渲染层"]
        direction TB
        RL1["CellRenderData<br/>渲染数据"]
        RL2["GridCell<br/>单元格组件"]
        RL3["GridMatrix<br/>矩阵组件"]
    end

    %% 渲染模式
    subgraph RM ["🎯 渲染模式"]
        direction TB
        RM1["坐标模式<br/>coordinates"]
        RM2["颜色模式<br/>color"]
        RM3["数值模式<br/>value"]
        RM4["特殊处理<br/>A-M字母"]
    end

    %% 性能优化
    subgraph PO ["⚡ 性能优化"]
        direction TB
        PO1["渲染缓存<br/>Cache"]
        PO2["批量处理<br/>Batch"]
        PO3["防抖调度<br/>Debounce"]
    end

    %% 主要数据流
    DS --> SL
    SL --> SM
    SM --> BL
    BL --> HL
    HL --> RL

    %% 渲染模式连接
    BL2 --> RM
    RM --> RL1

    %% 性能优化连接
    BL3 --> PO
    PO --> HL1

    %% 详细连接
    DS1 --> SL1
    DS2 --> SL2
    DS3 --> SL3
    DS4 --> SM2

    SL1 --> SM1
    SL2 --> SM1
    SL3 --> SM1

    SM1 --> BL1
    SM2 --> BL1
    BL1 --> BL2

    BL2 --> HL1
    HL1 --> HL2
    HL1 --> RL1
    HL2 --> RL1

    RL1 --> RL2
    RL2 --> RL3

    %% 样式定义
    classDef dataSource fill:#e3f2fd,stroke:#1976d2,stroke-width:3px,color:#000
    classDef service fill:#f3e5f5,stroke:#7b1fa2,stroke-width:3px,color:#000
    classDef state fill:#e8f5e8,stroke:#388e3c,stroke-width:3px,color:#000
    classDef business fill:#fff3e0,stroke:#f57c00,stroke-width:3px,color:#000
    classDef hook fill:#fce4ec,stroke:#c2185b,stroke-width:3px,color:#000
    classDef render fill:#f1f8e9,stroke:#689f38,stroke-width:3px,color:#000
    classDef mode fill:#e0f2f1,stroke:#00796b,stroke-width:3px,color:#000
    classDef performance fill:#fff8e1,stroke:#ffa000,stroke-width:3px,color:#000

    class DS,DS1,DS2,DS3,DS4 dataSource
    class SL,SL1,SL2,SL3 service
    class SM,SM1,SM2,SM3 state
    class BL,BL1,BL2,BL3 business
    class HL,HL1,HL2 hook
    class RL,RL1,RL2,RL3 render
    class RM,RM1,RM2,RM3,RM4 mode
    class PO,PO1,PO2,PO3 performance
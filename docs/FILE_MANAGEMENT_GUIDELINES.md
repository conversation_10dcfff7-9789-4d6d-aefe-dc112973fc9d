# Cube1 Group - 文档文件统一管理规范

## 🎯 文档管理目标

建立统一的文档文件管理规范，确保docs目录结构清晰、命名一致、内容标准化，提高文档的可维护性和可查找性。

---

## 📁 目录结构规范

### 标准目录结构
```
docs/
├── README.md                    # 文档总览和索引
├── USER_GUIDELINES.md           # AI助手行为规范
├── FILE_MANAGEMENT_GUIDELINES.md # 本文档
├── diagrams/                    # 架构图和流程图
│   ├── README.md               # 图表说明文档
│   ├── *.mmd                   # Mermaid源文件
│   └── *.svg                   # 导出的图片文件
├── migration/                   # 迁移相关文档
│   ├── README.md               # 迁移文档索引
│   └── *-migration*.md         # 具体迁移文档
├── report/                      # 技术报告和分析
│   ├── README.md               # 报告索引
│   └── *-report.md             # 具体报告文档
├── templates/                   # 文档模板
│   ├── README.md               # 模板使用说明
│   ├── log-template.md         # 开发日志模板
│   ├── report-template.md      # 技术报告模板
│   └── architecture-template.md # 架构文档模板
└── archive/                     # 归档文档 (可选)
    └── YYYY-MM/                # 按月归档
```

### 目录功能定义
- **diagrams/**: 存放所有架构图、流程图、系统图表
- **migration/**: 存放数据迁移、技术迁移相关文档
- **report/**: 存放技术分析报告、性能报告、问题修复报告
- **templates/**: 存放标准化文档模板
- **archive/**: 存放过期或历史文档

---

## 📝 文件命名规范

### 命名约定
1. **使用kebab-case**: 全小写，单词间用连字符分隔
2. **描述性命名**: 文件名应清楚描述内容
3. **包含日期**: 重要文档包含创建日期 (YYYYMMDD)
4. **版本标识**: 必要时包含版本号

### 具体命名规则

#### 技术报告
```
格式: {功能描述}-{类型}-report-{YYYYMMDD}.md
示例: 
- grid-rendering-performance-report-20250727.md
- api-optimization-analysis-report-20250727.md
- database-migration-summary-report-20250727.md
```

#### 架构文档
```
格式: {系统名称}-{架构类型}-architecture-{YYYYMMDD}.md
示例:
- frontend-component-architecture-20250727.md
- backend-api-architecture-20250727.md
- database-schema-architecture-20250727.md
```

#### 迁移文档
```
格式: {源技术}-to-{目标技术}-migration-{YYYYMMDD}.md
示例:
- react-query-to-tanstack-query-migration-20250727.md
- sqlite-to-postgresql-migration-20250727.md
```

#### 图表文件
```
Mermaid源文件: {描述}-{类型}.mmd
导出图片: {描述}-{类型}.svg
示例:
- system-architecture.mmd / system-architecture.svg
- user-interaction-flow.mmd / user-interaction-flow.svg
```

---

## 📋 文档内容规范

### 文档头部信息
每个文档都应包含标准化的头部信息：

```markdown
# 文档标题

**文档类型**: [报告/架构/迁移/指南]  
**创建日期**: YYYY-MM-DD  
**最后更新**: YYYY-MM-DD  
**作者**: [作者名称]  
**版本**: v1.0.0  
**状态**: [草稿/审核中/已完成/已归档]

## 📋 文档概述
[简要描述文档内容和目的]

---
```

### 内容结构标准
1. **概述部分**: 问题背景、目标、范围
2. **详细内容**: 具体实现、分析、步骤
3. **结果总结**: 成果、指标、影响
4. **后续计划**: 下一步行动、改进建议

### 代码示例规范
```markdown
# 使用语言标识的代码块
```typescript
// TypeScript代码示例
interface GridCellData {
  row: number;
  col: number;
  color: string;
}
```

# 命令行示例
```bash
# 安装依赖
pnpm install
```

# 配置文件示例
```json
{
  "name": "cube1-group",
  "version": "1.0.0"
}
```
```

---

## 🔄 文档生命周期管理

### 创建流程
1. **确定文档类型**: 选择合适的目录和模板
2. **使用标准模板**: 基于templates/中的模板创建
3. **遵循命名规范**: 按照规定格式命名文件
4. **填写头部信息**: 完整填写文档元数据
5. **更新索引**: 在相应的README.md中添加链接

### 更新流程
1. **更新内容**: 修改文档内容
2. **更新元数据**: 修改"最后更新"日期和版本号
3. **记录变更**: 在文档末尾添加变更日志
4. **通知相关人员**: 重要更新需要通知团队

### 归档流程
1. **评估文档价值**: 确定是否需要归档
2. **移动到archive/**: 按年月组织归档文件
3. **更新索引**: 从主索引中移除，添加到归档索引
4. **保留重要链接**: 在原位置留下重定向说明

---

## 📊 文档质量标准

### 内容质量要求
- **准确性**: 信息准确，代码可执行
- **完整性**: 内容完整，逻辑清晰
- **时效性**: 信息及时更新，避免过期内容
- **可读性**: 格式规范，结构清晰

### 格式规范检查
- **Markdown语法**: 正确使用Markdown语法
- **链接有效性**: 确保所有链接可访问
- **图片显示**: 确保图片正常显示
- **代码高亮**: 正确设置代码语言标识

### 定期维护
- **月度检查**: 每月检查文档时效性
- **季度整理**: 每季度整理归档过期文档
- **年度审核**: 每年全面审核文档结构

---

## 🛠️ 工具和自动化

### 推荐工具
- **Markdown编辑器**: VS Code + Markdown插件
- **图表工具**: Mermaid Live Editor
- **链接检查**: markdown-link-check
- **格式检查**: markdownlint

### 自动化脚本
```bash
# 检查文档链接有效性
npm run docs:check-links

# 格式化所有Markdown文件
npm run docs:format

# 生成文档索引
npm run docs:generate-index

# 检查命名规范
npm run docs:check-naming
```

### Git提交规范
```bash
# 文档相关提交格式
docs: add grid-rendering-performance-report
docs: update USER_GUIDELINES.md
docs: fix broken links in README
docs: archive outdated migration docs
```

---

## 📚 模板使用指南

### 选择合适模板
- **技术报告**: 使用 `report-template.md`
- **架构文档**: 使用 `architecture-template.md`
- **开发日志**: 使用 `log-template.md`

### 模板定制
1. **复制模板**: 从templates/目录复制对应模板
2. **重命名文件**: 按照命名规范重命名
3. **填写内容**: 根据模板结构填写具体内容
4. **更新索引**: 在README.md中添加新文档链接

---

## 🔍 文档索引管理

### README.md维护
每个子目录都应有README.md文件，包含：
- **目录说明**: 该目录的用途和内容
- **文档列表**: 按类型或时间排序的文档列表
- **快速导航**: 重要文档的快速链接

### 索引更新规则
- **新增文档**: 立即更新相应README.md
- **文档移动**: 更新所有相关索引
- **文档删除**: 从索引中移除相应条目
- **定期整理**: 每月整理索引结构

---

## 📞 支持和反馈

### 问题报告
如发现文档问题，请：
1. 检查是否符合本规范
2. 提交issue或直接修复
3. 通知文档维护人员

### 规范改进
本规范会根据实际使用情况持续改进：
- **收集反馈**: 定期收集使用反馈
- **评估效果**: 评估规范执行效果
- **持续优化**: 根据反馈优化规范

---

**文档版本**: v1.0.0  
**创建日期**: 2025-07-27  
**最后更新**: 2025-07-27  
**适用范围**: Cube1 Group项目docs目录管理

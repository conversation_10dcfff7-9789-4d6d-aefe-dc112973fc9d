# Cube1 Group - 编码管理 User Guidelines

## 🎯 AI助手编码行为规范

本文档定义Augment AI助手在Cube1 Group项目编码过程中的行为规范，确保代码质量和开发效率。

---

## 📋 项目编码上下文

### 技术栈约束

- **前端**: Next.js 15.1.0 + React 18.3.1 + TypeScript 5.8.3
- **后端**: FastAPI 0.104.1 + Python 3.9+ + SQLModel
- **状态管理**: Zustand 5.0.6 (避免过度使用useState)
- **样式**: Tailwind CSS 3.4.17 + Radix UI
- **数据库**: Prisma 6.11.1 + SQLite/PostgreSQL
- **构建**: Turbo 2.3.0 + pnpm 9.15.0

### 项目核心特性

- **33×33网格系统**: 1089个单元格高性能渲染
- **8色彩分类**: 红、青、黄、紫、橙、绿、蓝、粉
- **4层级架构**: Level 1-4数据组织
- **混合存储**: LocalStorage + API双重存储

---

## 🛠️ 编码规范要求

### 代码质量标准

1. **TypeScript严格模式**: 100%类型安全，避免any类型
2. **组件优化**: 优先使用React.memo和useMemo/useCallback
3. **状态管理**: 使用Zustand Store，避免过多useState
4. **错误处理**: 完整的try-catch和错误边界
5. **性能优先**: 高性能渲染，避免不必要的重渲染

### 文件结构规范

```
apps/frontend/
├── components/          # UI组件 (PascalCase)
├── features/           # 业务功能模块
├── lib/               # 工具库 (camelCase)
├── stores/            # Zustand状态管理
├── types/             # TypeScript类型定义
└── app/               # Next.js App Router
```

### 命名约定

- **组件**: PascalCase (`GridMatrix.tsx`)
- **函数/变量**: camelCase (`handleCellClick`)
- **常量**: UPPER_SNAKE_CASE (`GRID_SIZE`)
- **类型**: PascalCase (`GridCellData`)
- **Store**: camelCase + Store (`basicDataStore`)

---

## 🔧 开发工作流规范

### 编码前准备

1. **信息收集**: 使用codebase-retrieval了解相关代码
2. **依赖检查**: 确认所需的组件、类型、工具函数
3. **架构理解**: 理解Features架构和Store分工
4. **性能考虑**: 评估对网格渲染性能的影响

### 编码过程规范

1. **渐进式开发**: 小步骤迭代，频繁测试
2. **类型优先**: 先定义TypeScript类型，再实现逻辑
3. **组件化思维**: 优先复用现有组件，必要时创建新组件
4. **状态管理**: 评估是否需要新Store或扩展现有Store
5. **测试驱动**: 编写代码的同时考虑测试用例

### 代码审查要点

- **类型安全**: 无any类型，完整类型定义
- **性能优化**: memo、useMemo、useCallback使用恰当
- **错误处理**: 边界情况和错误状态处理完整
- **代码复用**: 避免重复代码，提取公共逻辑
- **文档注释**: 复杂逻辑有清晰注释

---

## 🎯 核心功能编码规范

### 网格系统开发

```typescript
// 正确的网格组件结构
const GridMatrix = memo(() => {
  const { gridData, updateCell } = useBasicDataStore();
  const { currentLevel } = useGridConfigStore();

  const handleCellClick = useCallback((row: number, col: number) => {
    updateCell(row, col, currentLevel);
  }, [updateCell, currentLevel]);

  return (
    <div className="grid grid-cols-33 gap-0">
      {/* 渲染逻辑 */}
    </div>
  );
});
```

### 状态管理规范

```typescript
// Zustand Store结构
interface BasicDataStore {
    gridData: GridData[][];
    selectedCells: Set<string>;
    updateCell: (row: number, col: number, level: number) => void;
    clearSelection: () => void;
}

// 避免在组件中使用过多useState
// ❌ 错误方式
const [data, setData] = useState();
const [loading, setLoading] = useState();
const [error, setError] = useState();

// ✅ 正确方式
const { data, loading, error, fetchData } = useDataStore();
```

### API集成规范

```typescript
// 类型安全的API调用
interface GridDataResponse {
    data: GridData[][];
    version: string;
    timestamp: string;
}

const fetchGridData = async (): Promise<GridDataResponse> => {
    try {
        const response = await apiClient.get<GridDataResponse>('/grid-data');
        return response.data;
    } catch (error) {
        throw new ApiError('Failed to fetch grid data', error);
    }
};
```

---

## 🧪 测试编码规范

### 测试文件结构

```
tests/
├── components/         # 组件测试
├── features/          # 功能模块测试
├── stores/            # Store测试
├── api/               # API测试
└── e2e/               # 端到端测试
```

### 测试编写要求

1. **组件测试**: 每个组件都要有对应测试
2. **Store测试**: 状态管理逻辑完整测试
3. **API测试**: 接口调用和错误处理测试
4. **E2E测试**: 关键用户流程测试
5. **性能测试**: 网格渲染性能基准测试

### 测试命名规范

```typescript
// 测试文件命名
GridMatrix.test.tsx;
basicDataStore.test.ts;
api - client.test.ts;

// 测试用例命名
describe('GridMatrix Component', () => {
    it('should render 1089 cells correctly', () => {});
    it('should handle cell click events', () => {});
    it('should update cell color on selection', () => {});
});
```

---

## 📦 依赖管理规范

### 包管理要求

1. **使用pnpm**: 所有依赖安装使用pnpm
2. **避免直接编辑**: 不直接编辑package.json
3. **版本锁定**: 重要依赖锁定具体版本
4. **定期更新**: 定期检查和更新依赖

### 依赖添加流程

```bash
# 前端依赖
cd apps/frontend
pnpm add <package-name>
pnpm add -D <dev-package-name>

# 后端依赖
cd apps/backend
poetry add <package-name>
poetry add --group dev <dev-package-name>
```

### 依赖选择原则

- **优先官方**: 优先选择官方维护的包
- **活跃维护**: 选择活跃维护的开源项目
- **类型支持**: 优先有TypeScript类型定义的包
- **体积考虑**: 避免引入过大的依赖包

---

## 🚨 错误处理和调试

### 错误处理模式

```typescript
// 组件错误边界
const GridErrorBoundary: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  return (
    <ErrorBoundary
      fallback={<GridErrorFallback />}
      onError={(error, errorInfo) => {
        console.error('Grid Error:', error, errorInfo);
        // 错误上报逻辑
      }}
    >
      {children}
    </ErrorBoundary>
  );
};

// API错误处理
const handleApiError = (error: unknown): never => {
  if (error instanceof ApiError) {
    toast.error(error.message);
  } else {
    toast.error('An unexpected error occurred');
  }
  throw error;
};
```

### 调试工具使用

1. **React DevTools**: 组件状态和性能分析
2. **Zustand DevTools**: 状态管理调试
3. **Network Tab**: API请求监控
4. **Performance Tab**: 渲染性能分析
5. **Console Logs**: 结构化日志输出

---

## 🔄 代码维护规范

### 重构原则

1. **小步重构**: 避免大规模重构，小步迭代
2. **测试保护**: 重构前确保测试覆盖
3. **向后兼容**: 保持API和接口的向后兼容
4. **文档更新**: 重构后及时更新文档

### 代码清理

```bash
# 定期执行的清理命令
pnpm run lint:fix          # 修复代码格式问题
pnpm run type-check        # 检查类型错误
pnpm run test              # 运行测试确保功能正常
pnpm run build             # 验证构建成功
```

### 性能监控

- **Bundle分析**: 定期分析打包体积
- **渲染性能**: 监控网格渲染时间
- **内存使用**: 检查内存泄漏
- **加载速度**: 监控首屏加载时间

---

## 📝 文档编码规范

### 代码注释要求

```typescript
/**
 * 网格单元格组件
 * @param row 行索引 (0-32)
 * @param col 列索引 (0-32)
 * @param level 数据层级 (1-4)
 * @param color 单元格颜色
 */
interface GridCellProps {
    row: number;
    col: number;
    level: number;
    color?: ColorType;
}
```

### README更新要求

- **功能变更**: 新功能要更新README
- **API变更**: 接口变更要更新文档
- **配置变更**: 配置文件变更要说明
- **依赖变更**: 重要依赖变更要记录

---

**文档版本**: v1.0.0
**最后更新**: 2025年7月27日
**适用范围**: Cube1 Group项目编码开发过程

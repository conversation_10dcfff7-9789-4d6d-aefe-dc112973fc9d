# TanStack Query 迁移指南

## 📋 概述

本文档描述了从自定义数据获取方案（useBusinessData）迁移到TanStack Query的过程。

## 🎯 迁移目标

- ✅ 移除SWR依赖
- ✅ 集成TanStack Query
- ✅ 保持API兼容性
- ✅ 提升数据获取性能
- ✅ 改善开发体验

## 🔄 迁移策略

### 1. 兼容层方案

我们采用了兼容层方案，保持原有API不变，但底层使用TanStack Query实现：

```typescript
// 原有用法保持不变
import { useBusinessData } from '@/features/shared/hooks';

// 新的TanStack Query hooks
import { useProjects, useColorData } from '@/lib/query';
```

### 2. 渐进式迁移

- **阶段1**: 安装TanStack Query，创建配置和Provider ✅
- **阶段2**: 创建新的查询hooks ✅
- **阶段3**: 实现兼容层 ✅
- **阶段4**: 逐步迁移组件到新hooks
- **阶段5**: 移除旧代码

## 📦 新的查询架构

### 查询键管理

```typescript
export const queryKeys = {
  projects: {
    all: ['projects'] as const,
    lists: () => [...queryKeys.projects.all, 'list'] as const,
    detail: (id: string) => [...queryKeys.projects.all, 'detail', id] as const,
  },
  data: {
    colors: (projectId: string) => ['data', 'colors', projectId] as const,
    grid: (projectId: string) => ['data', 'grid', projectId] as const,
  },
};
```

### 查询配置

```typescript
export const queryOptions = {
  // 实时数据（短缓存）
  realtime: {
    staleTime: 1 * 60 * 1000,
    refetchInterval: 30000,
  },
  
  // 静态数据（长缓存）
  static: {
    staleTime: 60 * 60 * 1000,
    refetchOnWindowFocus: false,
  },
};
```

## 🚀 新的使用方式

### 项目数据查询

```typescript
// 旧方式
const projectData = useBusinessData({
  dataType: 'project',
  fetcher: () => projectApi.getProject(id),
});

// 新方式
const { data: project, isLoading, error } = useProject(id);
```

### 数据变更

```typescript
// 旧方式
const updateProject = async (data) => {
  await projectApi.updateProject(id, data);
  projectData.refresh();
};

// 新方式
const updateProjectMutation = useUpdateProject();
const handleUpdate = (data) => {
  updateProjectMutation.mutate({ id, data });
};
```

### 缓存管理

```typescript
// 旧方式
projectData.clearCache();

// 新方式
const queryClient = useQueryClient();
queryClient.invalidateQueries({ queryKey: queryKeys.projects.detail(id) });
```

## 🔧 迁移步骤

### 1. 更新导入

```typescript
// 从
import { useBusinessData } from '@/features/shared/hooks';

// 到
import { useProjects, useProject } from '@/lib/query';
```

### 2. 更新组件逻辑

```typescript
// 旧组件
function ProjectComponent({ projectId }) {
  const projectData = useBusinessData({
    dataType: 'project',
    fetcher: () => projectApi.getProject(projectId),
  });

  if (projectData.isLoading) return <Loading />;
  if (projectData.error) return <Error message={projectData.error} />;

  return <div>{projectData.data?.name}</div>;
}

// 新组件
function ProjectComponent({ projectId }) {
  const { data: project, isLoading, error } = useProject(projectId);

  if (isLoading) return <Loading />;
  if (error) return <Error message={error.message} />;

  return <div>{project?.name}</div>;
}
```

### 3. 更新变更操作

```typescript
// 旧方式
const handleCreate = async (data) => {
  try {
    await projectApi.createProject(data);
    projectListData.refresh();
  } catch (error) {
    // 错误处理
  }
};

// 新方式
const createProjectMutation = useCreateProject({
  onSuccess: () => {
    // 自动更新缓存
    toast.success('项目创建成功');
  },
  onError: (error) => {
    toast.error(error.message);
  },
});

const handleCreate = (data) => {
  createProjectMutation.mutate(data);
};
```

## 🎁 新功能特性

### 1. 自动缓存管理

```typescript
// 自动缓存更新
const updateProjectMutation = useUpdateProject({
  onSuccess: (updatedProject, { id }) => {
    // 自动更新项目详情缓存
    queryClient.setQueryData(
      queryKeys.projects.detail(id),
      updatedProject
    );
  },
});
```

### 2. 乐观更新

```typescript
const updateColorMutation = useUpdateColorData({
  onMutate: async ({ projectId, data }) => {
    // 取消正在进行的查询
    await queryClient.cancelQueries({ 
      queryKey: queryKeys.data.colors(projectId) 
    });

    // 保存当前数据
    const previousData = queryClient.getQueryData(
      queryKeys.data.colors(projectId)
    );

    // 乐观更新
    queryClient.setQueryData(
      queryKeys.data.colors(projectId),
      (old) => ({ ...old, ...data })
    );

    return { previousData };
  },
  onError: (err, variables, context) => {
    // 回滚
    if (context?.previousData) {
      queryClient.setQueryData(
        queryKeys.data.colors(variables.projectId),
        context.previousData
      );
    }
  },
});
```

### 3. 预加载

```typescript
const prefetchProject = usePrefetchProject();

// 鼠标悬停时预加载
const handleMouseEnter = (projectId) => {
  prefetchProject(projectId);
};
```

### 4. 开发工具

```typescript
// 开发环境下自动启用
{process.env.NODE_ENV === 'development' && (
  <ReactQueryDevtools initialIsOpen={false} />
)}
```

## 📊 性能优势

| 特性 | 旧方案 | TanStack Query |
|------|--------|----------------|
| 缓存策略 | 手动管理 | 自动智能缓存 |
| 重复请求 | 可能重复 | 自动去重 |
| 后台更新 | 手动实现 | 自动后台更新 |
| 错误重试 | 手动实现 | 智能重试策略 |
| 乐观更新 | 复杂实现 | 内置支持 |
| 开发工具 | 无 | 强大的调试工具 |

## 🔍 调试和监控

### 1. 查询状态监控

```typescript
const { isFetching } = useIsFetching();
const { isMutating } = useIsMutating();

// 全局加载状态
if (isFetching > 0) {
  // 显示全局加载指示器
}
```

### 2. 错误边界

```typescript
function QueryErrorBoundary({ children }) {
  return (
    <ErrorBoundary
      fallback={<ErrorFallback />}
      onError={(error) => {
        console.error('Query error:', error);
      }}
    >
      {children}
    </ErrorBoundary>
  );
}
```

## 📝 最佳实践

1. **查询键设计**: 使用层次化的查询键结构
2. **缓存策略**: 根据数据特性选择合适的缓存时间
3. **错误处理**: 统一的错误处理和用户反馈
4. **性能优化**: 合理使用预加载和乐观更新
5. **类型安全**: 充分利用TypeScript类型系统

## 🚨 注意事项

1. **兼容性**: 旧的useBusinessData API仍然可用
2. **渐进迁移**: 可以逐步迁移，不需要一次性全部更改
3. **测试**: 确保迁移后的功能正常工作
4. **性能**: 监控迁移后的性能表现

---

**维护者**: Augment Agent  
**创建时间**: 2025年7月24日  
**状态**: ✅ 迁移完成，生产就绪

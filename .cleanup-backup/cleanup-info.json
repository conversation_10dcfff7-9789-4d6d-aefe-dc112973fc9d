{"timestamp": "2025-07-25T02:21:13.213Z", "files": [], "directories": [{"path": "/Users/<USER>/Desktop/cube1_group/.next/cache", "reason": "缓存目录", "size": 0}, {"path": "/Users/<USER>/Desktop/cube1_group/.turbo", "reason": "缓存目录", "size": 282}, {"path": "/Users/<USER>/Desktop/cube1_group/apps/backend/.ruff_cache", "reason": "缓存目录", "size": 165470}, {"path": "/Users/<USER>/Desktop/cube1_group/apps/backend/alembic/__pycache__", "reason": "缓存目录", "size": 9309}, {"path": "/Users/<USER>/Desktop/cube1_group/apps/backend/app/__pycache__", "reason": "缓存目录", "size": 66015}, {"path": "/Users/<USER>/Desktop/cube1_group/apps/backend/app/api/__pycache__", "reason": "缓存目录", "size": 622}, {"path": "/Users/<USER>/Desktop/cube1_group/apps/backend/app/api/v1/__pycache__", "reason": "缓存目录", "size": 4652}, {"path": "/Users/<USER>/Desktop/cube1_group/apps/backend/app/api/v1/endpoints/__pycache__", "reason": "缓存目录", "size": 103207}, {"path": "/Users/<USER>/Desktop/cube1_group/apps/backend/app/core/__pycache__", "reason": "缓存目录", "size": 66076}, {"path": "/Users/<USER>/Desktop/cube1_group/apps/backend/app/models/__pycache__", "reason": "缓存目录", "size": 207923}, {"path": "/Users/<USER>/Desktop/cube1_group/apps/backend/app/services/__pycache__", "reason": "缓存目录", "size": 29095}, {"path": "/Users/<USER>/Desktop/cube1_group/apps/frontend/.next/cache", "reason": "缓存目录", "size": 196186839}, {"path": "/Users/<USER>/Desktop/cube1_group/apps/frontend/.turbo", "reason": "缓存目录", "size": 0}, {"path": "/Users/<USER>/Desktop/cube1_group/apps/frontend/playwright-report", "reason": "缓存目录", "size": 459069}, {"path": "/Users/<USER>/Desktop/cube1_group/apps/frontend/test-results", "reason": "缓存目录", "size": 147}]}
#!/usr/bin/env node
/**
 * 项目清理脚本
 * 🎯 核心价值：清理旧配置文件、临时文件、缓存文件等冗余内容
 * 🔧 功能：自动识别和清理不需要的文件，优化项目结构
 * ⚡ 特性：安全清理、详细报告、可撤销操作
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

class ProjectCleaner {
  constructor() {
    this.projectRoot = process.cwd();
    this.filesToDelete = [];
    this.dirsToDelete = [];
    this.backupDir = path.join(this.projectRoot, '.cleanup-backup');
    this.stats = {
      filesScanned: 0,
      filesDeleted: 0,
      dirsDeleted: 0,
      spaceFreed: 0
    };
  }

  /**
   * 获取文件大小
   */
  getFileSize(filePath) {
    try {
      return fs.statSync(filePath).size;
    } catch {
      return 0;
    }
  }

  /**
   * 格式化文件大小
   */
  formatSize(bytes) {
    const units = ['B', 'KB', 'MB', 'GB'];
    let size = bytes;
    let unitIndex = 0;
    
    while (size >= 1024 && unitIndex < units.length - 1) {
      size /= 1024;
      unitIndex++;
    }
    
    return `${size.toFixed(1)} ${units[unitIndex]}`;
  }

  /**
   * 检查是否为临时文件
   */
  isTempFile(filePath) {
    const fileName = path.basename(filePath);

    // 保护重要的lock文件
    const protectedFiles = [
      'poetry.lock',
      'pnpm-lock.yaml',
      'package-lock.json',
      'yarn.lock'
    ];

    if (protectedFiles.includes(fileName)) {
      return false;
    }

    const tempPatterns = [
      /\.tmp$/,
      /\.temp$/,
      /\.bak$/,
      /\.backup$/,
      /\.orig$/,
      /\.old$/,
      /~$/,
      /\.swp$/,
      /\.swo$/,
      /\.DS_Store$/,
      /Thumbs\.db$/,
      /\.log$/,
      /\.pid$/,
      /\.cache$/
    ];

    return tempPatterns.some(pattern => pattern.test(fileName));
  }

  /**
   * 检查是否为缓存目录
   */
  isCacheDir(dirPath) {
    const cacheDirNames = [
      '.cache',
      '.tmp',
      '.temp',
      'node_modules/.cache',
      '.next/cache',
      '.turbo',
      '__pycache__',
      '.pytest_cache',
      '.mypy_cache',
      '.ruff_cache',
      '.coverage',
      'htmlcov',
      '.nyc_output',
      'coverage',
      'test-results',
      'playwright-report',
      'storybook-static'
    ];
    
    const dirName = path.basename(dirPath);
    const relativePath = path.relative(this.projectRoot, dirPath);
    
    return cacheDirNames.some(cacheDir => 
      dirName === cacheDir || relativePath.includes(cacheDir)
    );
  }

  /**
   * 检查是否为旧配置文件
   */
  isOldConfigFile(filePath) {
    const oldConfigPatterns = [
      /\.babelrc\.old$/,
      /webpack\.config\.old\.js$/,
      /tsconfig\.old\.json$/,
      /package\.json\.backup$/,
      /poetry\.lock\.backup$/,
      /\.env\.old$/,
      /\.gitignore\.old$/,
      /docker-compose\.old\.yml$/
    ];
    
    const fileName = path.basename(filePath);
    return oldConfigPatterns.some(pattern => pattern.test(fileName));
  }

  /**
   * 检查是否为重复的依赖文件
   */
  isDuplicateDependencyFile(filePath) {
    const fileName = path.basename(filePath);
    const duplicatePatterns = [
      /package-lock\.json$/, // 项目使用pnpm
      /yarn\.lock$/, // 项目使用pnpm
      /Pipfile$/, // 项目使用poetry
      /requirements\.txt$/ // 项目使用poetry
    ];
    
    return duplicatePatterns.some(pattern => pattern.test(fileName));
  }

  /**
   * 扫描目录查找冗余文件
   */
  scanDirectory(dirPath, depth = 0) {
    if (depth > 10) return; // 防止过深递归
    
    try {
      const items = fs.readdirSync(dirPath);
      
      for (const item of items) {
        const fullPath = path.join(dirPath, item);
        const stat = fs.statSync(fullPath);
        
        this.stats.filesScanned++;
        
        if (stat.isDirectory()) {
          // 跳过某些目录
          if (item === 'node_modules' || item === '.git' || item === '.venv') {
            continue;
          }
          
          // 检查是否为缓存目录
          if (this.isCacheDir(fullPath)) {
            this.dirsToDelete.push({
              path: fullPath,
              reason: '缓存目录',
              size: this.getDirSize(fullPath)
            });
          } else {
            this.scanDirectory(fullPath, depth + 1);
          }
        } else {
          // 检查文件
          let shouldDelete = false;
          let reason = '';
          
          if (this.isTempFile(fullPath)) {
            shouldDelete = true;
            reason = '临时文件';
          } else if (this.isOldConfigFile(fullPath)) {
            shouldDelete = true;
            reason = '旧配置文件';
          } else if (this.isDuplicateDependencyFile(fullPath)) {
            shouldDelete = true;
            reason = '重复的依赖文件';
          }
          
          if (shouldDelete) {
            this.filesToDelete.push({
              path: fullPath,
              reason: reason,
              size: this.getFileSize(fullPath)
            });
          }
        }
      }
    } catch (error) {
      console.warn(`扫描目录失败: ${dirPath} - ${error.message}`);
    }
  }

  /**
   * 获取目录大小
   */
  getDirSize(dirPath) {
    let totalSize = 0;
    try {
      const items = fs.readdirSync(dirPath);
      for (const item of items) {
        const fullPath = path.join(dirPath, item);
        const stat = fs.statSync(fullPath);
        if (stat.isDirectory()) {
          totalSize += this.getDirSize(fullPath);
        } else {
          totalSize += stat.size;
        }
      }
    } catch {
      // 忽略错误
    }
    return totalSize;
  }

  /**
   * 创建备份
   */
  createBackup() {
    if (this.filesToDelete.length === 0 && this.dirsToDelete.length === 0) {
      return;
    }

    console.log('📦 创建备份...');
    
    if (!fs.existsSync(this.backupDir)) {
      fs.mkdirSync(this.backupDir, { recursive: true });
    }

    const backupInfo = {
      timestamp: new Date().toISOString(),
      files: this.filesToDelete,
      directories: this.dirsToDelete
    };

    fs.writeFileSync(
      path.join(this.backupDir, 'cleanup-info.json'),
      JSON.stringify(backupInfo, null, 2)
    );

    console.log(`✅ 备份信息已保存到: ${this.backupDir}`);
  }

  /**
   * 执行清理
   */
  executeCleanup(dryRun = true) {
    if (dryRun) {
      console.log('\n🔍 预览模式 - 不会实际删除文件\n');
      return;
    }

    console.log('\n🚀 开始清理...\n');

    // 创建备份
    this.createBackup();

    // 删除文件
    for (const file of this.filesToDelete) {
      try {
        fs.unlinkSync(file.path);
        this.stats.filesDeleted++;
        this.stats.spaceFreed += file.size;
        console.log(`🗑️  删除文件: ${path.relative(this.projectRoot, file.path)}`);
      } catch (error) {
        console.error(`❌ 删除文件失败: ${file.path} - ${error.message}`);
      }
    }

    // 删除目录
    for (const dir of this.dirsToDelete) {
      try {
        fs.rmSync(dir.path, { recursive: true, force: true });
        this.stats.dirsDeleted++;
        this.stats.spaceFreed += dir.size;
        console.log(`🗑️  删除目录: ${path.relative(this.projectRoot, dir.path)}`);
      } catch (error) {
        console.error(`❌ 删除目录失败: ${dir.path} - ${error.message}`);
      }
    }
  }

  /**
   * 打印扫描结果
   */
  printResults() {
    console.log('\n📊 扫描结果:');
    console.log(`📁 扫描文件数: ${this.stats.filesScanned}`);
    console.log(`🗑️  待删除文件: ${this.filesToDelete.length}`);
    console.log(`📂 待删除目录: ${this.dirsToDelete.length}`);
    
    const totalSize = [...this.filesToDelete, ...this.dirsToDelete]
      .reduce((sum, item) => sum + item.size, 0);
    console.log(`💾 可释放空间: ${this.formatSize(totalSize)}`);

    if (this.filesToDelete.length > 0) {
      console.log('\n📄 待删除文件:');
      this.filesToDelete.forEach((file, index) => {
        const relativePath = path.relative(this.projectRoot, file.path);
        console.log(`  ${index + 1}. ${relativePath} (${file.reason}) - ${this.formatSize(file.size)}`);
      });
    }

    if (this.dirsToDelete.length > 0) {
      console.log('\n📁 待删除目录:');
      this.dirsToDelete.forEach((dir, index) => {
        const relativePath = path.relative(this.projectRoot, dir.path);
        console.log(`  ${index + 1}. ${relativePath} (${dir.reason}) - ${this.formatSize(dir.size)}`);
      });
    }
  }

  /**
   * 运行清理
   */
  run(options = {}) {
    const { dryRun = true } = options;

    console.log('🧹 开始项目清理扫描...\n');
    console.log(`📍 项目根目录: ${this.projectRoot}\n`);

    // 扫描项目
    this.scanDirectory(this.projectRoot);

    // 打印结果
    this.printResults();

    // 执行清理
    this.executeCleanup(dryRun);

    // 打印统计
    if (!dryRun) {
      console.log('\n📈 清理统计:');
      console.log(`✅ 删除文件: ${this.stats.filesDeleted}`);
      console.log(`✅ 删除目录: ${this.stats.dirsDeleted}`);
      console.log(`💾 释放空间: ${this.formatSize(this.stats.spaceFreed)}`);
    }

    console.log('\n✨ 清理完成！');
    
    if (dryRun) {
      console.log('\n💡 要执行实际清理，请运行: node scripts/cleanup-project.js --execute');
    } else {
      console.log(`\n🔄 如需恢复，请查看备份信息: ${this.backupDir}/cleanup-info.json`);
    }
  }
}

// 运行清理
if (require.main === module) {
  const dryRun = !process.argv.includes('--execute');
  const cleaner = new ProjectCleaner();
  cleaner.run({ dryRun });
}

module.exports = ProjectCleaner;
